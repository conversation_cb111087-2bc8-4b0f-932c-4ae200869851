<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%admin}}`.
 */
class m250826_052516_create_admin_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%admin}}', [
            'id'           => $this->primaryKey(),
            'username'     => $this->string(),
            'email'        => $this->string(),
            'otp_code'     => 'varchar(255)',
            'valid_time'   => 'integer',
            'password'     => 'varchar(255)',
            'password_key' => 'varchar(255)',
            'access_token' => 'varchar(255)',
            'role'         => 'varchar(100)',
            'is_suspend'   => 'tinyint(1) default 0',
            'created_at'   => $this->bigInteger()->notNull(),
            'updated_at'   => $this->bigInteger(),
            'is_delete'    => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByEmail` (`email`,`is_delete`)',
            'key `findByUsername` (`username`,`is_delete`)',
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%admin}}');
    }
}
