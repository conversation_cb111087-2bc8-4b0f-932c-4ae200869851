// cache.js
import enum_data from "../constants/enum.js"

const cache = {
    [enum_data.CACHE_KEYS.TASK_LOOKUP]: null,
};

const getCache = (key) => cache[key];
const setCache = (key, value) => {
    cache[key] = value;
};

const clearCache = (key) => {
    if (cache[key]) {
        cache[key] = null;
    }
};

const getAllCache = () => {
    return { ...cache };
};

export default {
    getCache,
    setCache,
    clearCache,
    getAllCache,
};
