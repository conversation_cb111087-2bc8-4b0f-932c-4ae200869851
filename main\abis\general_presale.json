{"name": "GeneralPreSale", "types": [{"name": "DataSize", "header": null, "fields": [{"name": "cells", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bits", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "refs", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}]}, {"name": "SignedBundle", "header": null, "fields": [{"name": "signature", "type": {"kind": "simple", "type": "fixed-bytes", "optional": false, "format": 64}}, {"name": "signedData", "type": {"kind": "simple", "type": "slice", "optional": false, "format": "remainder"}}]}, {"name": "StateInit", "header": null, "fields": [{"name": "code", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "Context", "header": null, "fields": [{"name": "bounceable", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "raw", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "SendParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "code", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "MessageParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "DeployParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "init", "type": {"kind": "simple", "type": "StateInit", "optional": false}}]}, {"name": "StdAddress", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 8}}, {"name": "address", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 32}}, {"name": "address", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "BasechainAdd<PERSON>", "header": null, "fields": [{"name": "hash", "type": {"kind": "simple", "type": "int", "optional": true, "format": 257}}]}, {"name": "ChangeOwner", "header": 2174598809, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "ChangeOwnerOk", "header": 846932810, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "TokenTransfer", "header": 260734629, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "destination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "responseDestination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "customPayload", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "forwardAmount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "forwardPayload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "TokenTransferInternal", "header": 395134233, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "from", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "responseDestination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forwardAmount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "forwardPayload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "TransferNotification", "header": 1935855772, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forwardPayload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "Excesses", "header": **********, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "GrantRoleMessage", "header": **********, "fields": [{"name": "account", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "RevokeRoleMessage", "header": *********, "fields": [{"name": "account", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "GrantRoleEvent", "header": **********, "fields": [{"name": "account", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "isAdmin", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "RevokeRoleEvent", "header": **********, "fields": [{"name": "account", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "isAdmin", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "WithdrawJetton", "header": **********, "fields": [{"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "jettonWallet", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "destinationWallet", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "WithdrawTon", "header": **********, "fields": [{"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "AddRound", "header": **********, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "hardcap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokenPrice", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "UpdateRound", "header": 3733775956, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "hardcap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokenPrice", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "PauseRound", "header": 4033829822, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "ResumeRound", "header": 547084238, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "SetActiveRound", "header": 1359463884, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "active", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "PauseContract", "header": 2652837487, "fields": []}, {"name": "ResumeContract", "header": 3017751040, "fields": []}, {"name": "AddPaymentJetton", "header": 2537708685, "fields": [{"name": "jettonWallet", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "decimals", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}]}, {"name": "RemovePaymentJetton", "header": 4215364412, "fields": [{"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "SetPaymentJettonStatus", "header": 978999898, "fields": [{"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "active", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "Round", "header": null, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "hardcap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokenPrice", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "totalPurchased", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "isActive", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "isPaused", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "PaymentJetton", "header": null, "fields": [{"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "decimals", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "isActive", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "PurchaseRecord", "header": null, "fields": [{"name": "buyer", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "jettonAmount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "usdAmount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "jettonWallet", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "timestamp", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "status", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "reason", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 16}}]}, {"name": "RoundStats", "header": null, "fields": [{"name": "roundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "hardcap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokenPrice", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "totalPurchased", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "isActive", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "isPaused", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "remaining", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "percentageSold", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "GeneralPreSale$Data", "header": null, "fields": [{"name": "initialized", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "_list", "type": {"kind": "dict", "key": "address", "value": "bool"}}, {"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "logId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "paused", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "maxRoundId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "rounds", "type": {"kind": "dict", "key": "uint", "keyFormat": 64, "value": "Round", "valueFormat": "ref"}}, {"name": "paymentJettons", "type": {"kind": "dict", "key": "address", "value": "PaymentJetton", "valueFormat": "ref"}}, {"name": "jettonWalletToMaster", "type": {"kind": "dict", "key": "address", "value": "address"}}, {"name": "logs", "type": {"kind": "dict", "key": "uint", "keyFormat": 64, "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "processedQueries", "type": {"kind": "dict", "key": "uint", "keyFormat": 64, "value": "bool"}}]}], "receivers": [{"receiver": "internal", "message": {"kind": "typed", "type": "PauseContract"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "ResumeContract"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "AddRound"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "UpdateRound"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "PauseRound"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "ResumeRound"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "SetActiveRound"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "AddPaymentJetton"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "RemovePaymentJetton"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "SetPaymentJettonStatus"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "TransferNotification"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "WithdrawJetton"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "WithdrawTon"}}, {"receiver": "internal", "message": {"kind": "text", "text": "withdraw all"}}, {"receiver": "internal", "message": {"kind": "empty"}}, {"receiver": "internal", "message": {"kind": "text"}}, {"receiver": "internal", "message": {"kind": "any"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "ChangeOwner"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "GrantRoleMessage"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "RevokeRoleMessage"}}], "getters": [{"name": "isInitialized", "methodId": 68815, "arguments": [], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "getLatestActiveRound", "methodId": 74802, "arguments": [], "returnType": {"kind": "simple", "type": "Round", "optional": true}}, {"name": "getRound", "methodId": 75858, "arguments": [{"name": "roundId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "Round", "optional": true}}, {"name": "getPaymentJetton", "methodId": 83476, "arguments": [{"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}], "returnType": {"kind": "simple", "type": "PaymentJetton", "optional": true}}, {"name": "isPaymentJettonActive", "methodId": 77723, "arguments": [{"name": "jettonMaster", "type": {"kind": "simple", "type": "address", "optional": false}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "getPurchaseLog", "methodId": 73561, "arguments": [{"name": "logId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "PurchaseRecord", "optional": true}}, {"name": "getLatestLogId", "methodId": 103476, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "isPaused", "methodId": 126174, "arguments": [], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "isQueryProcessed", "methodId": 107306, "arguments": [{"name": "queryId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "getMaxRoundId", "methodId": 107582, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "roundExists", "methodId": 77003, "arguments": [{"name": "roundId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "getRoundStats", "methodId": 116636, "arguments": [{"name": "roundId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "RoundStats", "optional": true}}, {"name": "getUserPurchases", "methodId": 116982, "arguments": [{"name": "buyer", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "limit", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "getJettonMasterByWallet", "methodId": 108608, "arguments": [{"name": "jettonWallet", "type": {"kind": "simple", "type": "address", "optional": false}}], "returnType": {"kind": "simple", "type": "address", "optional": true}}, {"name": "getRoundPurchases", "methodId": 83146, "arguments": [{"name": "roundId", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "limit", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "getBalance", "methodId": 106323, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "owner", "methodId": 83229, "arguments": [], "returnType": {"kind": "simple", "type": "address", "optional": false}}, {"name": "isAdmin", "methodId": 122448, "arguments": [{"name": "account", "type": {"kind": "simple", "type": "address", "optional": false}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}], "errors": {"2": {"message": "Stack underflow"}, "3": {"message": "Stack overflow"}, "4": {"message": "Integer overflow"}, "5": {"message": "Integer out of expected range"}, "6": {"message": "Invalid opcode"}, "7": {"message": "Type check error"}, "8": {"message": "Cell overflow"}, "9": {"message": "Cell underflow"}, "10": {"message": "Dictionary error"}, "11": {"message": "'Unknown' error"}, "12": {"message": "Fatal error"}, "13": {"message": "Out of gas error"}, "14": {"message": "Virtualization error"}, "32": {"message": "Action list is invalid"}, "33": {"message": "Action list is too long"}, "34": {"message": "Action is invalid or not supported"}, "35": {"message": "Invalid source address in outbound message"}, "36": {"message": "Invalid destination address in outbound message"}, "37": {"message": "Not enough Toncoin"}, "38": {"message": "Not enough extra currencies"}, "39": {"message": "Outbound message does not fit into a cell after rewriting"}, "40": {"message": "Cannot process a message"}, "41": {"message": "Library reference is null"}, "42": {"message": "Library change action error"}, "43": {"message": "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree"}, "50": {"message": "Account state size exceeded limits"}, "128": {"message": "Null reference exception"}, "129": {"message": "Invalid serialization prefix"}, "130": {"message": "Invalid incoming message"}, "131": {"message": "Constraints error"}, "132": {"message": "Access denied"}, "133": {"message": "Contract stopped"}, "134": {"message": "Invalid argument"}, "135": {"message": "Code of a contract was not found"}, "136": {"message": "Invalid standard address"}, "138": {"message": "Not a basechain address"}}, "interfaces": ["org.ton.introspection.v0", "org.ton.abi.ipfs.v0", "org.ton.deploy.lazy.v0", "org.ton.ownable.transferable.v2", "org.ton.ownable"]}