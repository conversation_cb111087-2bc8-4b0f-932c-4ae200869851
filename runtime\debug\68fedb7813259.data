a:14:{s:6:"config";s:6366:"a:5:{s:10:"phpVersion";s:6:"7.4.33";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:5:"Aleko";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"7.4.33";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:26:{s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:64:"C:\xampp\htdocs\aleko-bo\vendor/2amigos/yii2-ckeditor-widget/src";}}s:25:"alexantr/yii2-colorpicker";a:3:{s:4:"name";s:25:"alexantr/yii2-colorpicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@alexantr/colorpicker";s:57:"C:\xampp\htdocs\aleko-bo\vendor/alexantr/yii2-colorpicker";}}s:24:"asmoday74/yii2-ckeditor5";a:3:{s:4:"name";s:24:"asmoday74/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:20:"@asmoday74/ckeditor5";s:56:"C:\xampp\htdocs\aleko-bo\vendor/asmoday74/yii2-ckeditor5";}}s:29:"cetver/yii2-language-selector";a:3:{s:4:"name";s:29:"cetver/yii2-language-selector";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:24:"@cetver/LanguageSelector";s:61:"C:\xampp\htdocs\aleko-bo\vendor/cetver/yii2-language-selector";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:65:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:58:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte3/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:61:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:56:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-dialog/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-fileinput/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/date";s:67:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datepicker/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:60:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-date-range/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:71:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-typeahead/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:54:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-grid/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:64:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-select2/src";}}s:20:"nickcv/yii2-mandrill";a:3:{s:4:"name";s:20:"nickcv/yii2-mandrill";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@nickcv/mandrill";s:56:"C:\xampp\htdocs\aleko-bo\vendor/nickcv/yii2-mandrill/src";}}s:23:"sangroya/yii2-ckeditor5";a:3:{s:4:"name";s:23:"sangroya/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@sangroya/ckeditor5";s:55:"C:\xampp\htdocs\aleko-bo\vendor/sangroya/yii2-ckeditor5";}}s:27:"unclead/yii2-multiple-input";a:3:{s:4:"name";s:27:"unclead/yii2-multiple-input";s:7:"version";s:8:"********";s:5:"alias";a:3:{s:22:"@unclead/multipleinput";s:63:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/src";s:28:"@unclead/multipleinput/tests";s:65:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/tests";s:31:"@unclead/multipleinput/examples";s:68:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/examples";}}s:21:"yiier/yii2-aliyun-oss";a:3:{s:4:"name";s:21:"yiier/yii2-aliyun-oss";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@yiier/AliyunOSS";s:53:"C:\xampp\htdocs\aleko-bo\vendor/yiier/yii2-aliyun-oss";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-faker/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-httpclient/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-jui/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:10:"@yii/debug";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-debug/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.1.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:60:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-swiftmailer/src";}}}}";s:3:"log";s:65363:"a:1:{s:8:"messages";a:214:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532792.070518;i:4;a:0:{}i:5;i:2706504;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532792.07053;i:4;a:0:{}i:5;i:2707680;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532792.07794;i:4;a:0:{}i:5;i:3885144;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532792.08114;i:4;a:0:{}i:5;i:4205096;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532792.081451;i:4;a:0:{}i:5;i:4229232;}i:8;a:6:{i:0;s:35:"Route requested: 'gii/default/view'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1761532792.085207;i:4;a:0:{}i:5;i:4746512;}i:9;a:6:{i:0;s:30:"Route to run: gii/default/view";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1761532792.086569;i:4;a:0:{}i:5;i:4884328;}i:10;a:6:{i:0;s:67:"Running action: yii\gii\controllers\DefaultController::actionView()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1761532792.093138;i:4;a:0:{}i:5;i:5667416;}i:11;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.104705;i:4;a:0:{}i:5;i:6933088;}i:12;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532792.104729;i:4;a:0:{}i:5;i:6934776;}i:17;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149484;i:4;a:0:{}i:5;i:7001280;}i:20;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.150449;i:4;a:0:{}i:5;i:7019640;}i:23;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.152345;i:4;a:0:{}i:5;i:7113888;}i:26;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.153007;i:4;a:0:{}i:5;i:7120896;}i:29;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.155888;i:4;a:0:{}i:5;i:7136624;}i:32;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156178;i:4;a:0:{}i:5;i:7132256;}i:35;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156502;i:4;a:0:{}i:5;i:7134184;}i:38;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159655;i:4;a:0:{}i:5;i:7146960;}i:41;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159993;i:4;a:0:{}i:5;i:7144288;}i:44;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.16035;i:4;a:0:{}i:5;i:7145456;}i:47;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163419;i:4;a:0:{}i:5;i:7153968;}i:50;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.16369;i:4;a:0:{}i:5;i:7153496;}i:53;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.16401;i:4;a:0:{}i:5;i:7153768;}i:56;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166815;i:4;a:0:{}i:5;i:7168448;}i:59;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167128;i:4;a:0:{}i:5;i:7164776;}i:62;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167476;i:4;a:0:{}i:5;i:7167664;}i:65;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170305;i:4;a:0:{}i:5;i:7185008;}i:68;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170605;i:4;a:0:{}i:5;i:7182904;}i:71;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170909;i:4;a:0:{}i:5;i:7184080;}i:74;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173751;i:4;a:0:{}i:5;i:7189648;}i:77;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173981;i:4;a:0:{}i:5;i:7190832;}i:80;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.174311;i:4;a:0:{}i:5;i:7192000;}i:83;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.176956;i:4;a:0:{}i:5;i:7201400;}i:86;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.17726;i:4;a:0:{}i:5;i:7200432;}i:89;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177601;i:4;a:0:{}i:5;i:7201936;}i:92;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.180819;i:4;a:0:{}i:5;i:7206544;}i:95;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18109;i:4;a:0:{}i:5;i:7208256;}i:98;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.181443;i:4;a:0:{}i:5;i:7209944;}i:101;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184334;i:4;a:0:{}i:5;i:7216400;}i:104;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184589;i:4;a:0:{}i:5;i:7217096;}i:107;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184885;i:4;a:0:{}i:5;i:7217664;}i:110;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187682;i:4;a:0:{}i:5;i:7228048;}i:113;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187996;i:4;a:0:{}i:5;i:7226584;}i:116;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188329;i:4;a:0:{}i:5;i:7226840;}i:119;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191445;i:4;a:0:{}i:5;i:7236224;}i:122;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191713;i:4;a:0:{}i:5;i:7235288;}i:125;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.192059;i:4;a:0:{}i:5;i:7235544;}i:128;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195221;i:4;a:0:{}i:5;i:7250208;}i:131;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.19554;i:4;a:0:{}i:5;i:7250544;}i:134;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195885;i:4;a:0:{}i:5;i:7250792;}i:137;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199248;i:4;a:0:{}i:5;i:7260200;}i:140;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199554;i:4;a:0:{}i:5;i:7259272;}i:143;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.19994;i:4;a:0:{}i:5;i:7260416;}i:146;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203182;i:4;a:0:{}i:5;i:7274120;}i:149;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203511;i:4;a:0:{}i:5;i:7270880;}i:152;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203908;i:4;a:0:{}i:5;i:7272056;}i:155;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.20736;i:4;a:0:{}i:5;i:7289720;}i:158;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207731;i:4;a:0:{}i:5;i:7284376;}i:161;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.208109;i:4;a:0:{}i:5;i:7285288;}i:164;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211249;i:4;a:0:{}i:5;i:7295696;}i:167;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211535;i:4;a:0:{}i:5;i:7294200;}i:170;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211928;i:4;a:0:{}i:5;i:7295096;}i:173;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215487;i:4;a:0:{}i:5;i:7304552;}i:176;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.21583;i:4;a:0:{}i:5;i:7303544;}i:179;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.21622;i:4;a:0:{}i:5;i:7303800;}i:182;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219602;i:4;a:0:{}i:5;i:7308360;}i:185;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.21982;i:4;a:0:{}i:5;i:7310112;}i:188;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.220159;i:4;a:0:{}i:5;i:7310368;}i:191;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223507;i:4;a:0:{}i:5;i:7323088;}i:194;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223829;i:4;a:0:{}i:5;i:7320464;}i:197;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.224189;i:4;a:0:{}i:5;i:7320720;}i:200;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227467;i:4;a:0:{}i:5;i:7331104;}i:203;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227748;i:4;a:0:{}i:5;i:7329648;}i:206;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.228063;i:4;a:0:{}i:5;i:7329920;}i:209;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.231435;i:4;a:0:{}i:5;i:7340344;}i:212;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.231729;i:4;a:0:{}i:5;i:7338848;}i:215;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23206;i:4;a:0:{}i:5;i:7339120;}i:218;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235611;i:4;a:0:{}i:5;i:7348624;}i:221;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235934;i:4;a:0:{}i:5;i:7347616;}i:224;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23633;i:4;a:0:{}i:5;i:7347888;}i:227;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.239897;i:4;a:0:{}i:5;i:7357288;}i:230;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240198;i:4;a:0:{}i:5;i:7356360;}i:233;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240585;i:4;a:0:{}i:5;i:7357544;}i:236;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.243884;i:4;a:0:{}i:5;i:7371240;}i:239;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244183;i:4;a:0:{}i:5;i:7368080;}i:242;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244539;i:4;a:0:{}i:5;i:7368352;}i:245;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248177;i:4;a:0:{}i:5;i:7383104;}i:248;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248484;i:4;a:0:{}i:5;i:7379296;}i:251;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248854;i:4;a:0:{}i:5;i:7379552;}i:254;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252501;i:4;a:0:{}i:5;i:7392296;}i:257;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252821;i:4;a:0:{}i:5;i:7397824;}i:260;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.253187;i:4;a:0:{}i:5;i:7398072;}i:263;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256096;i:4;a:0:{}i:5;i:7410736;}i:266;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256377;i:4;a:0:{}i:5;i:7408168;}i:269;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256687;i:4;a:0:{}i:5;i:7408424;}i:272;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.259741;i:4;a:0:{}i:5;i:7425064;}i:275;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260078;i:4;a:0:{}i:5;i:7420336;}i:278;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260417;i:4;a:0:{}i:5;i:7422544;}i:281;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.26347;i:4;a:0:{}i:5;i:7431016;}i:284;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263797;i:4;a:0:{}i:5;i:7430576;}i:287;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.264219;i:4;a:0:{}i:5;i:7430848;}i:290;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267576;i:4;a:0:{}i:5;i:7443616;}i:293;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267881;i:4;a:0:{}i:5;i:7440904;}i:296;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.268235;i:4;a:0:{}i:5;i:7441152;}i:299;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271597;i:4;a:0:{}i:5;i:7470664;}i:302;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.272027;i:4;a:0:{}i:5;i:7458896;}i:305;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27239;i:4;a:0:{}i:5;i:7460448;}i:308;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.275812;i:4;a:0:{}i:5;i:7470784;}i:311;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276106;i:4;a:0:{}i:5;i:7469336;}i:314;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27649;i:4;a:0:{}i:5;i:7472888;}i:317;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279375;i:4;a:0:{}i:5;i:7482312;}i:320;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279665;i:4;a:0:{}i:5;i:7481344;}i:323;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.280014;i:4;a:0:{}i:5;i:7482520;}i:326;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.285837;i:4;a:0:{}i:5;i:7492848;}i:329;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286172;i:4;a:0:{}i:5;i:7491400;}i:332;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286589;i:4;a:0:{}i:5;i:7493640;}i:335;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.29062;i:4;a:0:{}i:5;i:7505344;}i:338;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290921;i:4;a:0:{}i:5;i:7503280;}i:341;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.291276;i:4;a:0:{}i:5;i:7504448;}i:344;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.294839;i:4;a:0:{}i:5;i:7513848;}i:347;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295112;i:4;a:0:{}i:5;i:7512928;}i:350;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295484;i:4;a:0:{}i:5;i:7514664;}i:353;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.298819;i:4;a:0:{}i:5;i:7524048;}i:356;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299101;i:4;a:0:{}i:5;i:7523128;}i:359;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299506;i:4;a:0:{}i:5;i:7524280;}i:362;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302794;i:4;a:0:{}i:5;i:7534600;}i:365;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303073;i:4;a:0:{}i:5;i:7533152;}i:368;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303451;i:4;a:0:{}i:5;i:7535384;}i:371;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.306799;i:4;a:0:{}i:5;i:7544744;}i:374;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307092;i:4;a:0:{}i:5;i:7543824;}i:377;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307479;i:4;a:0:{}i:5;i:7545528;}i:380;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.3107;i:4;a:0:{}i:5;i:7557120;}i:383;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.310996;i:4;a:0:{}i:5;i:7555144;}i:386;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31137;i:4;a:0:{}i:5;i:7557384;}i:389;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315145;i:4;a:0:{}i:5;i:7577456;}i:392;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315572;i:4;a:0:{}i:5;i:7571192;}i:395;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.316038;i:4;a:0:{}i:5;i:7577784;}i:398;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319355;i:4;a:0:{}i:5;i:7593536;}i:401;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319659;i:4;a:0:{}i:5;i:7589240;}i:404;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.32002;i:4;a:0:{}i:5;i:7590400;}i:407;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323195;i:4;a:0:{}i:5;i:7608912;}i:410;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323516;i:4;a:0:{}i:5;i:7603480;}i:413;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.324913;i:4;a:0:{}i:5;i:7692360;}i:416;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328623;i:4;a:0:{}i:5;i:7700848;}i:419;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328963;i:4;a:0:{}i:5;i:7703912;}i:422;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329243;i:4;a:0:{}i:5;i:7706600;}i:425;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329485;i:4;a:0:{}i:5;i:7708912;}i:428;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.32971;i:4;a:0:{}i:5;i:7711224;}i:431;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329897;i:4;a:0:{}i:5;i:7712984;}i:434;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330081;i:4;a:0:{}i:5;i:7715080;}i:437;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330331;i:4;a:0:{}i:5;i:7718072;}i:440;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330556;i:4;a:0:{}i:5;i:7720712;}i:443;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330768;i:4;a:0:{}i:5;i:7724032;}i:446;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33097;i:4;a:0:{}i:5;i:7726264;}i:449;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331137;i:4;a:0:{}i:5;i:7727976;}i:452;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331384;i:4;a:0:{}i:5;i:7731256;}i:455;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331628;i:4;a:0:{}i:5;i:7733888;}i:458;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331853;i:4;a:0:{}i:5;i:7736488;}i:461;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332072;i:4;a:0:{}i:5;i:7738584;}i:464;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332311;i:4;a:0:{}i:5;i:7741392;}i:467;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332566;i:4;a:0:{}i:5;i:7744008;}i:470;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332848;i:4;a:0:{}i:5;i:7746936;}i:473;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333091;i:4;a:0:{}i:5;i:7750752;}i:476;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333327;i:4;a:0:{}i:5;i:7752960;}i:479;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33353;i:4;a:0:{}i:5;i:7755184;}i:482;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333719;i:4;a:0:{}i:5;i:7757280;}i:485;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333923;i:4;a:0:{}i:5;i:7760768;}i:488;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334153;i:4;a:0:{}i:5;i:7763032;}i:491;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334365;i:4;a:0:{}i:5;i:7765632;}i:494;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334547;i:4;a:0:{}i:5;i:7767360;}i:497;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334735;i:4;a:0:{}i:5;i:7769480;}i:500;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334954;i:4;a:0:{}i:5;i:7772136;}i:503;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335163;i:4;a:0:{}i:5;i:7774368;}i:506;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335396;i:4;a:0:{}i:5;i:7776976;}i:509;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335598;i:4;a:0:{}i:5;i:7778704;}i:512;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335797;i:4;a:0:{}i:5;i:7780816;}i:515;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33605;i:4;a:0:{}i:5;i:7799848;}i:518;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336268;i:4;a:0:{}i:5;i:7802144;}i:521;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336463;i:4;a:0:{}i:5;i:7804264;}i:524;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336654;i:4;a:0:{}i:5;i:7806368;}i:527;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336861;i:4;a:0:{}i:5;i:7809312;}i:530;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337097;i:4;a:0:{}i:5;i:7811520;}i:533;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337341;i:4;a:0:{}i:5;i:7813776;}i:536;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337572;i:4;a:0:{}i:5;i:7815496;}i:539;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337806;i:4;a:0:{}i:5;i:7817600;}i:542;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33802;i:4;a:0:{}i:5;i:7819808;}i:545;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338206;i:4;a:0:{}i:5;i:7821888;}i:548;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33839;i:4;a:0:{}i:5;i:7824000;}i:551;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338617;i:4;a:0:{}i:5;i:7826256;}i:554;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338821;i:4;a:0:{}i:5;i:7830128;}i:557;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339001;i:4;a:0:{}i:5;i:7831848;}i:560;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339192;i:4;a:0:{}i:5;i:7833984;}i:563;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339463;i:4;a:0:{}i:5;i:7836312;}i:566;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339719;i:4;a:0:{}i:5;i:7838544;}i:569;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339967;i:4;a:0:{}i:5;i:7841184;}i:572;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340203;i:4;a:0:{}i:5;i:7843520;}i:575;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340491;i:4;a:0:{}i:5;i:7845872;}i:578;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34076;i:4;a:0:{}i:5;i:7848232;}i:581;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340993;i:4;a:0:{}i:5;i:7850592;}i:584;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341257;i:4;a:0:{}i:5;i:7853264;}i:587;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341512;i:4;a:0:{}i:5;i:7855592;}i:590;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34172;i:4;a:0:{}i:5;i:7857392;}i:593;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342028;i:4;a:0:{}i:5;i:7859048;}i:596;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342352;i:4;a:0:{}i:5;i:7861672;}i:599;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342626;i:4;a:0:{}i:5;i:7863904;}i:602;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342885;i:4;a:0:{}i:5;i:7866136;}i:605;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343082;i:4;a:0:{}i:5;i:7867864;}i:608;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343356;i:4;a:0:{}i:5;i:7875816;}i:611;a:6:{i:0;s:108:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/default/model.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.345116;i:4;a:0:{}i:5;i:8032440;}i:612;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.34645;i:4;a:0:{}i:5;i:7957840;}i:613;a:6:{i:0;s:99:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.350284;i:4;a:0:{}i:5;i:8515240;}i:614;a:6:{i:0;s:104:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view/results.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.356973;i:4;a:0:{}i:5;i:8667624;}i:615;a:6:{i:0;s:101:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\layouts\generator.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.380576;i:4;a:0:{}i:5;i:8639864;}i:616;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/views/layouts/main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532792.38111;i:4;a:0:{}i:5;i:8692144;}}}";s:9:"profiling";s:126034:"a:3:{s:6:"memory";i:9021160;s:4:"time";d:0.32273006439208984;s:8:"messages";a:400:{i:13;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532792.104734;i:4;a:0:{}i:5;i:6935152;}i:14;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532792.127232;i:4;a:0:{}i:5;i:6983816;}i:15;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.127261;i:4;a:0:{}i:5;i:6983328;}i:16;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149422;i:4;a:0:{}i:5;i:7000032;}i:18;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149502;i:4;a:0:{}i:5;i:7001896;}i:19;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149696;i:4;a:0:{}i:5;i:7004392;}i:21;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.150467;i:4;a:0:{}i:5;i:7021752;}i:22;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15081;i:4;a:0:{}i:5;i:7025280;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.152356;i:4;a:0:{}i:5;i:7114456;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.152974;i:4;a:0:{}i:5;i:7119408;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.153017;i:4;a:0:{}i:5;i:7121496;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.155842;i:4;a:0:{}i:5;i:7135984;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1559;i:4;a:0:{}i:5;i:7137224;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156047;i:4;a:0:{}i:5;i:7139040;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1562;i:4;a:0:{}i:5;i:7135648;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156472;i:4;a:0:{}i:5;i:7137280;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15651;i:4;a:0:{}i:5;i:7134824;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159598;i:4;a:0:{}i:5;i:7146352;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15967;i:4;a:0:{}i:5;i:7147576;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159835;i:4;a:0:{}i:5;i:7149536;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160005;i:4;a:0:{}i:5;i:7146400;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160315;i:4;a:0:{}i:5;i:7148888;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160358;i:4;a:0:{}i:5;i:7146072;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163362;i:4;a:0:{}i:5;i:7153344;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163433;i:4;a:0:{}i:5;i:7154568;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163586;i:4;a:0:{}i:5;i:7155880;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163701;i:4;a:0:{}i:5;i:7155608;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163978;i:4;a:0:{}i:5;i:7157240;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.164018;i:4;a:0:{}i:5;i:7154408;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166773;i:4;a:0:{}i:5;i:7167824;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166828;i:4;a:0:{}i:5;i:7169088;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166977;i:4;a:0:{}i:5;i:7171560;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.16714;i:4;a:0:{}i:5;i:7166888;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167438;i:4;a:0:{}i:5;i:7171096;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167486;i:4;a:0:{}i:5;i:7168280;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170261;i:4;a:0:{}i:5;i:7178752;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170318;i:4;a:0:{}i:5;i:7185608;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170487;i:4;a:0:{}i:5;i:7187304;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170615;i:4;a:0:{}i:5;i:7185016;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170879;i:4;a:0:{}i:5;i:7187496;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170917;i:4;a:0:{}i:5;i:7184720;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173705;i:4;a:0:{}i:5;i:7189040;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173764;i:4;a:0:{}i:5;i:7190264;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173899;i:4;a:0:{}i:5;i:7191640;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173992;i:4;a:0:{}i:5;i:7192944;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.174279;i:4;a:0:{}i:5;i:7195432;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.17432;i:4;a:0:{}i:5;i:7192616;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.176914;i:4;a:0:{}i:5;i:7200776;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.176968;i:4;a:0:{}i:5;i:7202000;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.17714;i:4;a:0:{}i:5;i:7203568;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177271;i:4;a:0:{}i:5;i:7202544;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177571;i:4;a:0:{}i:5;i:7205032;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177609;i:4;a:0:{}i:5;i:7202576;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18076;i:4;a:0:{}i:5;i:7205936;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.180837;i:4;a:0:{}i:5;i:7207160;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18101;i:4;a:0:{}i:5;i:7208600;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.181101;i:4;a:0:{}i:5;i:7210368;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18141;i:4;a:0:{}i:5;i:7213376;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.181451;i:4;a:0:{}i:5;i:7210560;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184285;i:4;a:0:{}i:5;i:7215776;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184368;i:4;a:0:{}i:5;i:7217000;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184501;i:4;a:0:{}i:5;i:7218248;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184601;i:4;a:0:{}i:5;i:7219208;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184855;i:4;a:0:{}i:5;i:7220840;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184893;i:4;a:0:{}i:5;i:7218264;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187627;i:4;a:0:{}i:5;i:7227408;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187698;i:4;a:0:{}i:5;i:7228648;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187846;i:4;a:0:{}i:5;i:7230208;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188013;i:4;a:0:{}i:5;i:7228696;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188295;i:4;a:0:{}i:5;i:7230328;}i:117;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188338;i:4;a:0:{}i:5;i:7227456;}i:118;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191405;i:4;a:0:{}i:5;i:7235592;}i:120;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191458;i:4;a:0:{}i:5;i:7236840;}i:121;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191608;i:4;a:0:{}i:5;i:7238408;}i:123;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191724;i:4;a:0:{}i:5;i:7237400;}i:124;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.192022;i:4;a:0:{}i:5;i:7239032;}i:126;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.192071;i:4;a:0:{}i:5;i:7236160;}i:127;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195186;i:4;a:0:{}i:5;i:7249584;}i:129;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195234;i:4;a:0:{}i:5;i:7254904;}i:130;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195396;i:4;a:0:{}i:5;i:7256720;}i:132;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195552;i:4;a:0:{}i:5;i:7252656;}i:133;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195858;i:4;a:0:{}i:5;i:7254288;}i:135;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195893;i:4;a:0:{}i:5;i:7251392;}i:136;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1992;i:4;a:0:{}i:5;i:7259560;}i:138;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199262;i:4;a:0:{}i:5;i:7260800;}i:139;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199433;i:4;a:0:{}i:5;i:7262360;}i:141;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199567;i:4;a:0:{}i:5;i:7261384;}i:142;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199904;i:4;a:0:{}i:5;i:7263848;}i:144;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199949;i:4;a:0:{}i:5;i:7261032;}i:145;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203143;i:4;a:0:{}i:5;i:7273496;}i:147;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203193;i:4;a:0:{}i:5;i:7274720;}i:148;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203358;i:4;a:0:{}i:5;i:7276544;}i:150;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203535;i:4;a:0:{}i:5;i:7272992;}i:151;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203874;i:4;a:0:{}i:5;i:7275472;}i:153;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203916;i:4;a:0:{}i:5;i:7272696;}i:154;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207295;i:4;a:0:{}i:5;i:7289112;}i:156;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207377;i:4;a:0:{}i:5;i:7290336;}i:157;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207581;i:4;a:0:{}i:5;i:7292168;}i:159;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207743;i:4;a:0:{}i:5;i:7286488;}i:160;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.208073;i:4;a:0:{}i:5;i:7288120;}i:162;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.208118;i:4;a:0:{}i:5;i:7285928;}i:163;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211202;i:4;a:0:{}i:5;i:7295072;}i:165;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211263;i:4;a:0:{}i:5;i:7296336;}i:166;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211413;i:4;a:0:{}i:5;i:7297784;}i:168;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211547;i:4;a:0:{}i:5;i:7296312;}i:169;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211896;i:4;a:0:{}i:5;i:7297944;}i:171;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211937;i:4;a:0:{}i:5;i:7295712;}i:172;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215425;i:4;a:0:{}i:5;i:7303928;}i:174;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215503;i:4;a:0:{}i:5;i:7305152;}i:175;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215685;i:4;a:0:{}i:5;i:7306720;}i:177;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215841;i:4;a:0:{}i:5;i:7305656;}i:178;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.216191;i:4;a:0:{}i:5;i:7307288;}i:180;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.216228;i:4;a:0:{}i:5;i:7304416;}i:181;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219559;i:4;a:0:{}i:5;i:7307736;}i:183;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219614;i:4;a:0:{}i:5;i:7308960;}i:184;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219753;i:4;a:0:{}i:5;i:7310112;}i:186;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219829;i:4;a:0:{}i:5;i:7312224;}i:187;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.220132;i:4;a:0:{}i:5;i:7313856;}i:189;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.220167;i:4;a:0:{}i:5;i:7310984;}i:190;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223463;i:4;a:0:{}i:5;i:7322456;}i:192;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22352;i:4;a:0:{}i:5;i:7323704;}i:193;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22368;i:4;a:0:{}i:5;i:7325272;}i:195;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223841;i:4;a:0:{}i:5;i:7322576;}i:196;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.224158;i:4;a:0:{}i:5;i:7324208;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.224198;i:4;a:0:{}i:5;i:7321336;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227425;i:4;a:0:{}i:5;i:7330472;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227481;i:4;a:0:{}i:5;i:7331720;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22763;i:4;a:0:{}i:5;i:7333288;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227765;i:4;a:0:{}i:5;i:7331760;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.228034;i:4;a:0:{}i:5;i:7333392;}i:207;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.228071;i:4;a:0:{}i:5;i:7330560;}i:208;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23138;i:4;a:0:{}i:5;i:7339720;}i:210;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23145;i:4;a:0:{}i:5;i:7340984;}i:211;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.231615;i:4;a:0:{}i:5;i:7342560;}i:213;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23174;i:4;a:0:{}i:5;i:7340960;}i:214;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23203;i:4;a:0:{}i:5;i:7342592;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.232068;i:4;a:0:{}i:5;i:7339760;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235553;i:4;a:0:{}i:5;i:7348000;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235627;i:4;a:0:{}i:5;i:7349264;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23582;i:4;a:0:{}i:5;i:7350712;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235945;i:4;a:0:{}i:5;i:7349728;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.236298;i:4;a:0:{}i:5;i:7351360;}i:225;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.236338;i:4;a:0:{}i:5;i:7348528;}i:226;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.239846;i:4;a:0:{}i:5;i:7356680;}i:228;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23991;i:4;a:0:{}i:5;i:7357904;}i:229;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240078;i:4;a:0:{}i:5;i:7359480;}i:231;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240208;i:4;a:0:{}i:5;i:7358472;}i:232;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.24055;i:4;a:0:{}i:5;i:7360960;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240602;i:4;a:0:{}i:5;i:7358184;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.243841;i:4;a:0:{}i:5;i:7370632;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.243897;i:4;a:0:{}i:5;i:7371856;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244061;i:4;a:0:{}i:5;i:7373560;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244194;i:4;a:0:{}i:5;i:7370192;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244504;i:4;a:0:{}i:5;i:7371824;}i:243;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244548;i:4;a:0:{}i:5;i:7368992;}i:244;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248122;i:4;a:0:{}i:5;i:7382496;}i:246;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.24819;i:4;a:0:{}i:5;i:7383720;}i:247;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248356;i:4;a:0:{}i:5;i:7385424;}i:249;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248496;i:4;a:0:{}i:5;i:7381408;}i:250;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248826;i:4;a:0:{}i:5;i:7383040;}i:252;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248862;i:4;a:0:{}i:5;i:7380168;}i:253;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252446;i:4;a:0:{}i:5;i:7391672;}i:255;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252517;i:4;a:0:{}i:5;i:7392896;}i:256;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252683;i:4;a:0:{}i:5;i:7394464;}i:258;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25284;i:4;a:0:{}i:5;i:7399936;}i:259;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.253157;i:4;a:0:{}i:5;i:7401568;}i:261;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.253195;i:4;a:0:{}i:5;i:7398672;}i:262;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256055;i:4;a:0:{}i:5;i:7410096;}i:264;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25611;i:4;a:0:{}i:5;i:7411336;}i:265;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256259;i:4;a:0:{}i:5;i:7413024;}i:267;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256389;i:4;a:0:{}i:5;i:7410280;}i:268;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256653;i:4;a:0:{}i:5;i:7411912;}i:270;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256696;i:4;a:0:{}i:5;i:7409040;}i:271;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25968;i:4;a:0:{}i:5;i:7424432;}i:273;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.259755;i:4;a:0:{}i:5;i:7425680;}i:274;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.259917;i:4;a:0:{}i:5;i:7428144;}i:276;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260091;i:4;a:0:{}i:5;i:7422448;}i:277;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.26038;i:4;a:0:{}i:5;i:7425976;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260427;i:4;a:0:{}i:5;i:7423160;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263396;i:4;a:0:{}i:5;i:7430384;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263495;i:4;a:0:{}i:5;i:7431632;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263687;i:4;a:0:{}i:5;i:7433072;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263819;i:4;a:0:{}i:5;i:7432688;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.264187;i:4;a:0:{}i:5;i:7434320;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.264227;i:4;a:0:{}i:5;i:7431488;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267524;i:4;a:0:{}i:5;i:7443008;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267591;i:4;a:0:{}i:5;i:7444232;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267761;i:4;a:0:{}i:5;i:7445936;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267892;i:4;a:0:{}i:5;i:7443016;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.268207;i:4;a:0:{}i:5;i:7444648;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.268243;i:4;a:0:{}i:5;i:7441752;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271552;i:4;a:0:{}i:5;i:7470024;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271614;i:4;a:0:{}i:5;i:7471264;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271837;i:4;a:0:{}i:5;i:7474232;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.272049;i:4;a:0:{}i:5;i:7461008;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27236;i:4;a:0:{}i:5;i:7462640;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.272398;i:4;a:0:{}i:5;i:7461088;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27578;i:4;a:0:{}i:5;i:7470176;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.275822;i:4;a:0:{}i:5;i:7471400;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.275987;i:4;a:0:{}i:5;i:7473360;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276119;i:4;a:0:{}i:5;i:7471448;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276444;i:4;a:0:{}i:5;i:7475024;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276499;i:4;a:0:{}i:5;i:7473528;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279325;i:4;a:0:{}i:5;i:7481704;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279389;i:4;a:0:{}i:5;i:7482928;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27955;i:4;a:0:{}i:5;i:7484760;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279678;i:4;a:0:{}i:5;i:7483456;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279976;i:4;a:0:{}i:5;i:7485936;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.280023;i:4;a:0:{}i:5;i:7483160;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.285783;i:4;a:0:{}i:5;i:7492240;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.285852;i:4;a:0:{}i:5;i:7493464;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286049;i:4;a:0:{}i:5;i:7495424;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286193;i:4;a:0:{}i:5;i:7493512;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286555;i:4;a:0:{}i:5;i:7497072;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286597;i:4;a:0:{}i:5;i:7494256;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290578;i:4;a:0:{}i:5;i:7504712;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290633;i:4;a:0:{}i:5;i:7505960;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290805;i:4;a:0:{}i:5;i:7507784;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290931;i:4;a:0:{}i:5;i:7505392;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.291246;i:4;a:0:{}i:5;i:7507864;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.291284;i:4;a:0:{}i:5;i:7505088;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.294797;i:4;a:0:{}i:5;i:7513224;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.294852;i:4;a:0:{}i:5;i:7514488;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295013;i:4;a:0:{}i:5;i:7516320;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295122;i:4;a:0:{}i:5;i:7515040;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.29545;i:4;a:0:{}i:5;i:7518080;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295493;i:4;a:0:{}i:5;i:7515304;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.298767;i:4;a:0:{}i:5;i:7523440;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.298834;i:4;a:0:{}i:5;i:7524664;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299;i:4;a:0:{}i:5;i:7526232;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299112;i:4;a:0:{}i:5;i:7525240;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299463;i:4;a:0:{}i:5;i:7527712;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299517;i:4;a:0:{}i:5;i:7524896;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302754;i:4;a:0:{}i:5;i:7533968;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302806;i:4;a:0:{}i:5;i:7535216;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302972;i:4;a:0:{}i:5;i:7537168;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303083;i:4;a:0:{}i:5;i:7535264;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303417;i:4;a:0:{}i:5;i:7538816;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.30346;i:4;a:0:{}i:5;i:7536000;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.306756;i:4;a:0:{}i:5;i:7544112;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.306818;i:4;a:0:{}i:5;i:7545360;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.30699;i:4;a:0:{}i:5;i:7547056;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307103;i:4;a:0:{}i:5;i:7545936;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307445;i:4;a:0:{}i:5;i:7548944;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307488;i:4;a:0:{}i:5;i:7546168;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.310661;i:4;a:0:{}i:5;i:7556512;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.310712;i:4;a:0:{}i:5;i:7557736;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31089;i:4;a:0:{}i:5;i:7559952;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311011;i:4;a:0:{}i:5;i:7557256;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311337;i:4;a:0:{}i:5;i:7560800;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311378;i:4;a:0:{}i:5;i:7558032;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315081;i:4;a:0:{}i:5;i:7576840;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315161;i:4;a:0:{}i:5;i:7578096;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315412;i:4;a:0:{}i:5;i:7582112;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315584;i:4;a:0:{}i:5;i:7573304;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315995;i:4;a:0:{}i:5;i:7581224;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31605;i:4;a:0:{}i:5;i:7578384;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319312;i:4;a:0:{}i:5;i:7592896;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319369;i:4;a:0:{}i:5;i:7594136;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319534;i:4;a:0:{}i:5;i:7596336;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31967;i:4;a:0:{}i:5;i:7591352;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31999;i:4;a:0:{}i:5;i:7593816;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.320028;i:4;a:0:{}i:5;i:7591040;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323162;i:4;a:0:{}i:5;i:7608304;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323206;i:4;a:0:{}i:5;i:7609528;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323366;i:4;a:0:{}i:5;i:7612000;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323526;i:4;a:0:{}i:5;i:7605592;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323847;i:4;a:0:{}i:5;i:7609144;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.324928;i:4;a:0:{}i:5;i:7692976;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328383;i:4;a:0:{}i:5;i:7733880;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328635;i:4;a:0:{}i:5;i:7700824;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328811;i:4;a:0:{}i:5;i:7702736;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328973;i:4;a:0:{}i:5;i:7703912;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329129;i:4;a:0:{}i:5;i:7706384;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329253;i:4;a:0:{}i:5;i:7706600;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329377;i:4;a:0:{}i:5;i:7709072;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329494;i:4;a:0:{}i:5;i:7708912;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329617;i:4;a:0:{}i:5;i:7711384;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.32972;i:4;a:0:{}i:5;i:7711224;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329835;i:4;a:0:{}i:5;i:7713696;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329905;i:4;a:0:{}i:5;i:7712984;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330009;i:4;a:0:{}i:5;i:7715456;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330089;i:4;a:0:{}i:5;i:7715040;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330225;i:4;a:0:{}i:5;i:7716736;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330339;i:4;a:0:{}i:5;i:7718048;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330469;i:4;a:0:{}i:5;i:7719424;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330563;i:4;a:0:{}i:5;i:7720672;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330682;i:4;a:0:{}i:5;i:7722240;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330775;i:4;a:0:{}i:5;i:7724008;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330897;i:4;a:0:{}i:5;i:7725448;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330977;i:4;a:0:{}i:5;i:7726240;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331081;i:4;a:0:{}i:5;i:7727680;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331144;i:4;a:0:{}i:5;i:7727952;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331256;i:4;a:0:{}i:5;i:7729392;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331392;i:4;a:0:{}i:5;i:7731232;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331532;i:4;a:0:{}i:5;i:7733696;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331638;i:4;a:0:{}i:5;i:7733864;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331771;i:4;a:0:{}i:5;i:7736328;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33186;i:4;a:0:{}i:5;i:7736464;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331996;i:4;a:0:{}i:5;i:7738928;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332085;i:4;a:0:{}i:5;i:7738560;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332226;i:4;a:0:{}i:5;i:7741024;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33232;i:4;a:0:{}i:5;i:7741352;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33246;i:4;a:0:{}i:5;i:7742912;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332578;i:4;a:0:{}i:5;i:7743968;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332709;i:4;a:0:{}i:5;i:7745792;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33286;i:4;a:0:{}i:5;i:7746912;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332984;i:4;a:0:{}i:5;i:7748488;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333098;i:4;a:0:{}i:5;i:7750728;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333245;i:4;a:0:{}i:5;i:7753192;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333334;i:4;a:0:{}i:5;i:7752936;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333457;i:4;a:0:{}i:5;i:7755400;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333537;i:4;a:0:{}i:5;i:7755160;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333662;i:4;a:0:{}i:5;i:7757624;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333726;i:4;a:0:{}i:5;i:7757256;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333842;i:4;a:0:{}i:5;i:7759720;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33393;i:4;a:0:{}i:5;i:7760744;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33407;i:4;a:0:{}i:5;i:7762704;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334161;i:4;a:0:{}i:5;i:7763008;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334286;i:4;a:0:{}i:5;i:7764968;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334372;i:4;a:0:{}i:5;i:7765608;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334493;i:4;a:0:{}i:5;i:7767568;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334554;i:4;a:0:{}i:5;i:7767336;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334674;i:4;a:0:{}i:5;i:7769296;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334745;i:4;a:0:{}i:5;i:7769456;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33487;i:4;a:0:{}i:5;i:7771288;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334961;i:4;a:0:{}i:5;i:7772112;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335083;i:4;a:0:{}i:5;i:7774072;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335171;i:4;a:0:{}i:5;i:7774344;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335294;i:4;a:0:{}i:5;i:7776304;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335405;i:4;a:0:{}i:5;i:7776952;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335529;i:4;a:0:{}i:5;i:7778912;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335606;i:4;a:0:{}i:5;i:7778680;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335718;i:4;a:0:{}i:5;i:7780640;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335816;i:4;a:0:{}i:5;i:7797176;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335957;i:4;a:0:{}i:5;i:7799000;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336057;i:4;a:0:{}i:5;i:7799848;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336184;i:4;a:0:{}i:5;i:7801680;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336277;i:4;a:0:{}i:5;i:7802144;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336399;i:4;a:0:{}i:5;i:7803976;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336471;i:4;a:0:{}i:5;i:7804264;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336594;i:4;a:0:{}i:5;i:7806096;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336661;i:4;a:0:{}i:5;i:7806344;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336776;i:4;a:0:{}i:5;i:7807912;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336868;i:4;a:0:{}i:5;i:7809288;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336999;i:4;a:0:{}i:5;i:7811240;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337106;i:4;a:0:{}i:5;i:7811496;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337241;i:4;a:0:{}i:5;i:7813448;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337349;i:4;a:0:{}i:5;i:7813752;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337487;i:4;a:0:{}i:5;i:7815704;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337582;i:4;a:0:{}i:5;i:7815472;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337723;i:4;a:0:{}i:5;i:7817424;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337814;i:4;a:0:{}i:5;i:7817576;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337938;i:4;a:0:{}i:5;i:7819272;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338027;i:4;a:0:{}i:5;i:7819784;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338151;i:4;a:0:{}i:5;i:7821480;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338213;i:4;a:0:{}i:5;i:7821864;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338327;i:4;a:0:{}i:5;i:7823560;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338399;i:4;a:0:{}i:5;i:7823976;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338537;i:4;a:0:{}i:5;i:7826192;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338624;i:4;a:0:{}i:5;i:7826232;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338745;i:4;a:0:{}i:5;i:7828448;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338828;i:4;a:0:{}i:5;i:7830104;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338948;i:4;a:0:{}i:5;i:7832320;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339008;i:4;a:0:{}i:5;i:7831824;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339124;i:4;a:0:{}i:5;i:7834040;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339199;i:4;a:0:{}i:5;i:7833984;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339382;i:4;a:0:{}i:5;i:7838000;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33947;i:4;a:0:{}i:5;i:7836312;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339638;i:4;a:0:{}i:5;i:7840328;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339727;i:4;a:0:{}i:5;i:7838544;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339893;i:4;a:0:{}i:5;i:7842560;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339974;i:4;a:0:{}i:5;i:7841184;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340126;i:4;a:0:{}i:5;i:7845200;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34021;i:4;a:0:{}i:5;i:7843520;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340376;i:4;a:0:{}i:5;i:7847536;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340501;i:4;a:0:{}i:5;i:7845872;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340675;i:4;a:0:{}i:5;i:7849888;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340767;i:4;a:0:{}i:5;i:7848232;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340914;i:4;a:0:{}i:5;i:7852248;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341001;i:4;a:0:{}i:5;i:7850592;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341172;i:4;a:0:{}i:5;i:7854608;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341264;i:4;a:0:{}i:5;i:7853264;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341433;i:4;a:0:{}i:5;i:7857280;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341519;i:4;a:0:{}i:5;i:7855592;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341666;i:4;a:0:{}i:5;i:7859608;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341726;i:4;a:0:{}i:5;i:7857392;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341912;i:4;a:0:{}i:5;i:7861408;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342039;i:4;a:0:{}i:5;i:7859008;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.3422;i:4;a:0:{}i:5;i:7861208;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342362;i:4;a:0:{}i:5;i:7861648;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34251;i:4;a:0:{}i:5;i:7864120;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342636;i:4;a:0:{}i:5;i:7863880;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342785;i:4;a:0:{}i:5;i:7866352;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342894;i:4;a:0:{}i:5;i:7866112;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343025;i:4;a:0:{}i:5;i:7868584;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343089;i:4;a:0:{}i:5;i:7867840;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343223;i:4;a:0:{}i:5;i:7870312;}i:609;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343365;i:4;a:0:{}i:5;i:7876432;}i:610;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343503;i:4;a:0:{}i:5;i:7878896;}}}";s:2:"db";s:125636:"a:1:{s:8:"messages";a:398:{i:15;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.127261;i:4;a:0:{}i:5;i:6983328;}i:16;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149422;i:4;a:0:{}i:5;i:7000032;}i:18;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149502;i:4;a:0:{}i:5;i:7001896;}i:19;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.149696;i:4;a:0:{}i:5;i:7004392;}i:21;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.150467;i:4;a:0:{}i:5;i:7021752;}i:22;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15081;i:4;a:0:{}i:5;i:7025280;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.152356;i:4;a:0:{}i:5;i:7114456;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.152974;i:4;a:0:{}i:5;i:7119408;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.153017;i:4;a:0:{}i:5;i:7121496;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.155842;i:4;a:0:{}i:5;i:7135984;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1559;i:4;a:0:{}i:5;i:7137224;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156047;i:4;a:0:{}i:5;i:7139040;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1562;i:4;a:0:{}i:5;i:7135648;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.156472;i:4;a:0:{}i:5;i:7137280;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15651;i:4;a:0:{}i:5;i:7134824;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159598;i:4;a:0:{}i:5;i:7146352;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.15967;i:4;a:0:{}i:5;i:7147576;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.159835;i:4;a:0:{}i:5;i:7149536;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160005;i:4;a:0:{}i:5;i:7146400;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160315;i:4;a:0:{}i:5;i:7148888;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.160358;i:4;a:0:{}i:5;i:7146072;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163362;i:4;a:0:{}i:5;i:7153344;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163433;i:4;a:0:{}i:5;i:7154568;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163586;i:4;a:0:{}i:5;i:7155880;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163701;i:4;a:0:{}i:5;i:7155608;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.163978;i:4;a:0:{}i:5;i:7157240;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.164018;i:4;a:0:{}i:5;i:7154408;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166773;i:4;a:0:{}i:5;i:7167824;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166828;i:4;a:0:{}i:5;i:7169088;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.166977;i:4;a:0:{}i:5;i:7171560;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.16714;i:4;a:0:{}i:5;i:7166888;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167438;i:4;a:0:{}i:5;i:7171096;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.167486;i:4;a:0:{}i:5;i:7168280;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170261;i:4;a:0:{}i:5;i:7178752;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170318;i:4;a:0:{}i:5;i:7185608;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170487;i:4;a:0:{}i:5;i:7187304;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170615;i:4;a:0:{}i:5;i:7185016;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170879;i:4;a:0:{}i:5;i:7187496;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.170917;i:4;a:0:{}i:5;i:7184720;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173705;i:4;a:0:{}i:5;i:7189040;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173764;i:4;a:0:{}i:5;i:7190264;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173899;i:4;a:0:{}i:5;i:7191640;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.173992;i:4;a:0:{}i:5;i:7192944;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.174279;i:4;a:0:{}i:5;i:7195432;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.17432;i:4;a:0:{}i:5;i:7192616;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.176914;i:4;a:0:{}i:5;i:7200776;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.176968;i:4;a:0:{}i:5;i:7202000;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.17714;i:4;a:0:{}i:5;i:7203568;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177271;i:4;a:0:{}i:5;i:7202544;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177571;i:4;a:0:{}i:5;i:7205032;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.177609;i:4;a:0:{}i:5;i:7202576;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18076;i:4;a:0:{}i:5;i:7205936;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.180837;i:4;a:0:{}i:5;i:7207160;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18101;i:4;a:0:{}i:5;i:7208600;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.181101;i:4;a:0:{}i:5;i:7210368;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.18141;i:4;a:0:{}i:5;i:7213376;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.181451;i:4;a:0:{}i:5;i:7210560;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184285;i:4;a:0:{}i:5;i:7215776;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184368;i:4;a:0:{}i:5;i:7217000;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184501;i:4;a:0:{}i:5;i:7218248;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184601;i:4;a:0:{}i:5;i:7219208;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184855;i:4;a:0:{}i:5;i:7220840;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.184893;i:4;a:0:{}i:5;i:7218264;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187627;i:4;a:0:{}i:5;i:7227408;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187698;i:4;a:0:{}i:5;i:7228648;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.187846;i:4;a:0:{}i:5;i:7230208;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188013;i:4;a:0:{}i:5;i:7228696;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188295;i:4;a:0:{}i:5;i:7230328;}i:117;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.188338;i:4;a:0:{}i:5;i:7227456;}i:118;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191405;i:4;a:0:{}i:5;i:7235592;}i:120;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191458;i:4;a:0:{}i:5;i:7236840;}i:121;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191608;i:4;a:0:{}i:5;i:7238408;}i:123;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.191724;i:4;a:0:{}i:5;i:7237400;}i:124;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.192022;i:4;a:0:{}i:5;i:7239032;}i:126;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.192071;i:4;a:0:{}i:5;i:7236160;}i:127;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195186;i:4;a:0:{}i:5;i:7249584;}i:129;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195234;i:4;a:0:{}i:5;i:7254904;}i:130;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195396;i:4;a:0:{}i:5;i:7256720;}i:132;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195552;i:4;a:0:{}i:5;i:7252656;}i:133;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195858;i:4;a:0:{}i:5;i:7254288;}i:135;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.195893;i:4;a:0:{}i:5;i:7251392;}i:136;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.1992;i:4;a:0:{}i:5;i:7259560;}i:138;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199262;i:4;a:0:{}i:5;i:7260800;}i:139;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199433;i:4;a:0:{}i:5;i:7262360;}i:141;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199567;i:4;a:0:{}i:5;i:7261384;}i:142;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199904;i:4;a:0:{}i:5;i:7263848;}i:144;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.199949;i:4;a:0:{}i:5;i:7261032;}i:145;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203143;i:4;a:0:{}i:5;i:7273496;}i:147;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203193;i:4;a:0:{}i:5;i:7274720;}i:148;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203358;i:4;a:0:{}i:5;i:7276544;}i:150;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203535;i:4;a:0:{}i:5;i:7272992;}i:151;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203874;i:4;a:0:{}i:5;i:7275472;}i:153;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.203916;i:4;a:0:{}i:5;i:7272696;}i:154;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207295;i:4;a:0:{}i:5;i:7289112;}i:156;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207377;i:4;a:0:{}i:5;i:7290336;}i:157;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207581;i:4;a:0:{}i:5;i:7292168;}i:159;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.207743;i:4;a:0:{}i:5;i:7286488;}i:160;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.208073;i:4;a:0:{}i:5;i:7288120;}i:162;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.208118;i:4;a:0:{}i:5;i:7285928;}i:163;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211202;i:4;a:0:{}i:5;i:7295072;}i:165;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211263;i:4;a:0:{}i:5;i:7296336;}i:166;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211413;i:4;a:0:{}i:5;i:7297784;}i:168;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211547;i:4;a:0:{}i:5;i:7296312;}i:169;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211896;i:4;a:0:{}i:5;i:7297944;}i:171;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.211937;i:4;a:0:{}i:5;i:7295712;}i:172;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215425;i:4;a:0:{}i:5;i:7303928;}i:174;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215503;i:4;a:0:{}i:5;i:7305152;}i:175;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215685;i:4;a:0:{}i:5;i:7306720;}i:177;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.215841;i:4;a:0:{}i:5;i:7305656;}i:178;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.216191;i:4;a:0:{}i:5;i:7307288;}i:180;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.216228;i:4;a:0:{}i:5;i:7304416;}i:181;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219559;i:4;a:0:{}i:5;i:7307736;}i:183;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219614;i:4;a:0:{}i:5;i:7308960;}i:184;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219753;i:4;a:0:{}i:5;i:7310112;}i:186;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.219829;i:4;a:0:{}i:5;i:7312224;}i:187;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.220132;i:4;a:0:{}i:5;i:7313856;}i:189;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.220167;i:4;a:0:{}i:5;i:7310984;}i:190;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223463;i:4;a:0:{}i:5;i:7322456;}i:192;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22352;i:4;a:0:{}i:5;i:7323704;}i:193;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22368;i:4;a:0:{}i:5;i:7325272;}i:195;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.223841;i:4;a:0:{}i:5;i:7322576;}i:196;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.224158;i:4;a:0:{}i:5;i:7324208;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.224198;i:4;a:0:{}i:5;i:7321336;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227425;i:4;a:0:{}i:5;i:7330472;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227481;i:4;a:0:{}i:5;i:7331720;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.22763;i:4;a:0:{}i:5;i:7333288;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.227765;i:4;a:0:{}i:5;i:7331760;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.228034;i:4;a:0:{}i:5;i:7333392;}i:207;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.228071;i:4;a:0:{}i:5;i:7330560;}i:208;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23138;i:4;a:0:{}i:5;i:7339720;}i:210;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23145;i:4;a:0:{}i:5;i:7340984;}i:211;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.231615;i:4;a:0:{}i:5;i:7342560;}i:213;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23174;i:4;a:0:{}i:5;i:7340960;}i:214;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23203;i:4;a:0:{}i:5;i:7342592;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.232068;i:4;a:0:{}i:5;i:7339760;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235553;i:4;a:0:{}i:5;i:7348000;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235627;i:4;a:0:{}i:5;i:7349264;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23582;i:4;a:0:{}i:5;i:7350712;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.235945;i:4;a:0:{}i:5;i:7349728;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.236298;i:4;a:0:{}i:5;i:7351360;}i:225;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.236338;i:4;a:0:{}i:5;i:7348528;}i:226;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.239846;i:4;a:0:{}i:5;i:7356680;}i:228;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.23991;i:4;a:0:{}i:5;i:7357904;}i:229;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240078;i:4;a:0:{}i:5;i:7359480;}i:231;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240208;i:4;a:0:{}i:5;i:7358472;}i:232;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.24055;i:4;a:0:{}i:5;i:7360960;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.240602;i:4;a:0:{}i:5;i:7358184;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.243841;i:4;a:0:{}i:5;i:7370632;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.243897;i:4;a:0:{}i:5;i:7371856;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244061;i:4;a:0:{}i:5;i:7373560;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244194;i:4;a:0:{}i:5;i:7370192;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244504;i:4;a:0:{}i:5;i:7371824;}i:243;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.244548;i:4;a:0:{}i:5;i:7368992;}i:244;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248122;i:4;a:0:{}i:5;i:7382496;}i:246;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.24819;i:4;a:0:{}i:5;i:7383720;}i:247;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248356;i:4;a:0:{}i:5;i:7385424;}i:249;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248496;i:4;a:0:{}i:5;i:7381408;}i:250;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248826;i:4;a:0:{}i:5;i:7383040;}i:252;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.248862;i:4;a:0:{}i:5;i:7380168;}i:253;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252446;i:4;a:0:{}i:5;i:7391672;}i:255;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252517;i:4;a:0:{}i:5;i:7392896;}i:256;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.252683;i:4;a:0:{}i:5;i:7394464;}i:258;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25284;i:4;a:0:{}i:5;i:7399936;}i:259;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.253157;i:4;a:0:{}i:5;i:7401568;}i:261;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.253195;i:4;a:0:{}i:5;i:7398672;}i:262;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256055;i:4;a:0:{}i:5;i:7410096;}i:264;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25611;i:4;a:0:{}i:5;i:7411336;}i:265;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256259;i:4;a:0:{}i:5;i:7413024;}i:267;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256389;i:4;a:0:{}i:5;i:7410280;}i:268;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256653;i:4;a:0:{}i:5;i:7411912;}i:270;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.256696;i:4;a:0:{}i:5;i:7409040;}i:271;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.25968;i:4;a:0:{}i:5;i:7424432;}i:273;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.259755;i:4;a:0:{}i:5;i:7425680;}i:274;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.259917;i:4;a:0:{}i:5;i:7428144;}i:276;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260091;i:4;a:0:{}i:5;i:7422448;}i:277;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.26038;i:4;a:0:{}i:5;i:7425976;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.260427;i:4;a:0:{}i:5;i:7423160;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263396;i:4;a:0:{}i:5;i:7430384;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263495;i:4;a:0:{}i:5;i:7431632;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263687;i:4;a:0:{}i:5;i:7433072;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.263819;i:4;a:0:{}i:5;i:7432688;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.264187;i:4;a:0:{}i:5;i:7434320;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.264227;i:4;a:0:{}i:5;i:7431488;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267524;i:4;a:0:{}i:5;i:7443008;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267591;i:4;a:0:{}i:5;i:7444232;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267761;i:4;a:0:{}i:5;i:7445936;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.267892;i:4;a:0:{}i:5;i:7443016;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.268207;i:4;a:0:{}i:5;i:7444648;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.268243;i:4;a:0:{}i:5;i:7441752;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271552;i:4;a:0:{}i:5;i:7470024;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271614;i:4;a:0:{}i:5;i:7471264;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.271837;i:4;a:0:{}i:5;i:7474232;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.272049;i:4;a:0:{}i:5;i:7461008;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27236;i:4;a:0:{}i:5;i:7462640;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.272398;i:4;a:0:{}i:5;i:7461088;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27578;i:4;a:0:{}i:5;i:7470176;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.275822;i:4;a:0:{}i:5;i:7471400;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.275987;i:4;a:0:{}i:5;i:7473360;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276119;i:4;a:0:{}i:5;i:7471448;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276444;i:4;a:0:{}i:5;i:7475024;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.276499;i:4;a:0:{}i:5;i:7473528;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279325;i:4;a:0:{}i:5;i:7481704;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279389;i:4;a:0:{}i:5;i:7482928;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.27955;i:4;a:0:{}i:5;i:7484760;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279678;i:4;a:0:{}i:5;i:7483456;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.279976;i:4;a:0:{}i:5;i:7485936;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.280023;i:4;a:0:{}i:5;i:7483160;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.285783;i:4;a:0:{}i:5;i:7492240;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.285852;i:4;a:0:{}i:5;i:7493464;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286049;i:4;a:0:{}i:5;i:7495424;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286193;i:4;a:0:{}i:5;i:7493512;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286555;i:4;a:0:{}i:5;i:7497072;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.286597;i:4;a:0:{}i:5;i:7494256;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290578;i:4;a:0:{}i:5;i:7504712;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290633;i:4;a:0:{}i:5;i:7505960;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290805;i:4;a:0:{}i:5;i:7507784;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.290931;i:4;a:0:{}i:5;i:7505392;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.291246;i:4;a:0:{}i:5;i:7507864;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.291284;i:4;a:0:{}i:5;i:7505088;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.294797;i:4;a:0:{}i:5;i:7513224;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.294852;i:4;a:0:{}i:5;i:7514488;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295013;i:4;a:0:{}i:5;i:7516320;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295122;i:4;a:0:{}i:5;i:7515040;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.29545;i:4;a:0:{}i:5;i:7518080;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.295493;i:4;a:0:{}i:5;i:7515304;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.298767;i:4;a:0:{}i:5;i:7523440;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.298834;i:4;a:0:{}i:5;i:7524664;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299;i:4;a:0:{}i:5;i:7526232;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299112;i:4;a:0:{}i:5;i:7525240;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299463;i:4;a:0:{}i:5;i:7527712;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.299517;i:4;a:0:{}i:5;i:7524896;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302754;i:4;a:0:{}i:5;i:7533968;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302806;i:4;a:0:{}i:5;i:7535216;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.302972;i:4;a:0:{}i:5;i:7537168;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303083;i:4;a:0:{}i:5;i:7535264;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.303417;i:4;a:0:{}i:5;i:7538816;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.30346;i:4;a:0:{}i:5;i:7536000;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.306756;i:4;a:0:{}i:5;i:7544112;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.306818;i:4;a:0:{}i:5;i:7545360;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.30699;i:4;a:0:{}i:5;i:7547056;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307103;i:4;a:0:{}i:5;i:7545936;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307445;i:4;a:0:{}i:5;i:7548944;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.307488;i:4;a:0:{}i:5;i:7546168;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.310661;i:4;a:0:{}i:5;i:7556512;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.310712;i:4;a:0:{}i:5;i:7557736;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31089;i:4;a:0:{}i:5;i:7559952;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311011;i:4;a:0:{}i:5;i:7557256;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311337;i:4;a:0:{}i:5;i:7560800;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.311378;i:4;a:0:{}i:5;i:7558032;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315081;i:4;a:0:{}i:5;i:7576840;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315161;i:4;a:0:{}i:5;i:7578096;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315412;i:4;a:0:{}i:5;i:7582112;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315584;i:4;a:0:{}i:5;i:7573304;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.315995;i:4;a:0:{}i:5;i:7581224;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31605;i:4;a:0:{}i:5;i:7578384;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319312;i:4;a:0:{}i:5;i:7592896;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319369;i:4;a:0:{}i:5;i:7594136;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.319534;i:4;a:0:{}i:5;i:7596336;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31967;i:4;a:0:{}i:5;i:7591352;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.31999;i:4;a:0:{}i:5;i:7593816;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.320028;i:4;a:0:{}i:5;i:7591040;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323162;i:4;a:0:{}i:5;i:7608304;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323206;i:4;a:0:{}i:5;i:7609528;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323366;i:4;a:0:{}i:5;i:7612000;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323526;i:4;a:0:{}i:5;i:7605592;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.323847;i:4;a:0:{}i:5;i:7609144;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.324928;i:4;a:0:{}i:5;i:7692976;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328383;i:4;a:0:{}i:5;i:7733880;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328635;i:4;a:0:{}i:5;i:7700824;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328811;i:4;a:0:{}i:5;i:7702736;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.328973;i:4;a:0:{}i:5;i:7703912;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329129;i:4;a:0:{}i:5;i:7706384;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329253;i:4;a:0:{}i:5;i:7706600;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329377;i:4;a:0:{}i:5;i:7709072;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329494;i:4;a:0:{}i:5;i:7708912;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329617;i:4;a:0:{}i:5;i:7711384;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.32972;i:4;a:0:{}i:5;i:7711224;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329835;i:4;a:0:{}i:5;i:7713696;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.329905;i:4;a:0:{}i:5;i:7712984;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330009;i:4;a:0:{}i:5;i:7715456;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330089;i:4;a:0:{}i:5;i:7715040;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330225;i:4;a:0:{}i:5;i:7716736;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330339;i:4;a:0:{}i:5;i:7718048;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330469;i:4;a:0:{}i:5;i:7719424;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330563;i:4;a:0:{}i:5;i:7720672;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330682;i:4;a:0:{}i:5;i:7722240;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330775;i:4;a:0:{}i:5;i:7724008;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330897;i:4;a:0:{}i:5;i:7725448;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.330977;i:4;a:0:{}i:5;i:7726240;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331081;i:4;a:0:{}i:5;i:7727680;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331144;i:4;a:0:{}i:5;i:7727952;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331256;i:4;a:0:{}i:5;i:7729392;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331392;i:4;a:0:{}i:5;i:7731232;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331532;i:4;a:0:{}i:5;i:7733696;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331638;i:4;a:0:{}i:5;i:7733864;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331771;i:4;a:0:{}i:5;i:7736328;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33186;i:4;a:0:{}i:5;i:7736464;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.331996;i:4;a:0:{}i:5;i:7738928;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332085;i:4;a:0:{}i:5;i:7738560;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332226;i:4;a:0:{}i:5;i:7741024;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33232;i:4;a:0:{}i:5;i:7741352;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33246;i:4;a:0:{}i:5;i:7742912;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332578;i:4;a:0:{}i:5;i:7743968;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332709;i:4;a:0:{}i:5;i:7745792;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33286;i:4;a:0:{}i:5;i:7746912;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.332984;i:4;a:0:{}i:5;i:7748488;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333098;i:4;a:0:{}i:5;i:7750728;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333245;i:4;a:0:{}i:5;i:7753192;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333334;i:4;a:0:{}i:5;i:7752936;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333457;i:4;a:0:{}i:5;i:7755400;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333537;i:4;a:0:{}i:5;i:7755160;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333662;i:4;a:0:{}i:5;i:7757624;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333726;i:4;a:0:{}i:5;i:7757256;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.333842;i:4;a:0:{}i:5;i:7759720;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33393;i:4;a:0:{}i:5;i:7760744;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33407;i:4;a:0:{}i:5;i:7762704;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334161;i:4;a:0:{}i:5;i:7763008;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334286;i:4;a:0:{}i:5;i:7764968;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334372;i:4;a:0:{}i:5;i:7765608;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334493;i:4;a:0:{}i:5;i:7767568;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334554;i:4;a:0:{}i:5;i:7767336;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334674;i:4;a:0:{}i:5;i:7769296;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334745;i:4;a:0:{}i:5;i:7769456;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33487;i:4;a:0:{}i:5;i:7771288;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.334961;i:4;a:0:{}i:5;i:7772112;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335083;i:4;a:0:{}i:5;i:7774072;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335171;i:4;a:0:{}i:5;i:7774344;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335294;i:4;a:0:{}i:5;i:7776304;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335405;i:4;a:0:{}i:5;i:7776952;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335529;i:4;a:0:{}i:5;i:7778912;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335606;i:4;a:0:{}i:5;i:7778680;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335718;i:4;a:0:{}i:5;i:7780640;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335816;i:4;a:0:{}i:5;i:7797176;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.335957;i:4;a:0:{}i:5;i:7799000;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336057;i:4;a:0:{}i:5;i:7799848;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336184;i:4;a:0:{}i:5;i:7801680;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336277;i:4;a:0:{}i:5;i:7802144;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336399;i:4;a:0:{}i:5;i:7803976;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336471;i:4;a:0:{}i:5;i:7804264;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336594;i:4;a:0:{}i:5;i:7806096;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336661;i:4;a:0:{}i:5;i:7806344;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336776;i:4;a:0:{}i:5;i:7807912;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336868;i:4;a:0:{}i:5;i:7809288;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.336999;i:4;a:0:{}i:5;i:7811240;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337106;i:4;a:0:{}i:5;i:7811496;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337241;i:4;a:0:{}i:5;i:7813448;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337349;i:4;a:0:{}i:5;i:7813752;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337487;i:4;a:0:{}i:5;i:7815704;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337582;i:4;a:0:{}i:5;i:7815472;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337723;i:4;a:0:{}i:5;i:7817424;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337814;i:4;a:0:{}i:5;i:7817576;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.337938;i:4;a:0:{}i:5;i:7819272;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338027;i:4;a:0:{}i:5;i:7819784;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338151;i:4;a:0:{}i:5;i:7821480;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338213;i:4;a:0:{}i:5;i:7821864;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338327;i:4;a:0:{}i:5;i:7823560;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338399;i:4;a:0:{}i:5;i:7823976;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338537;i:4;a:0:{}i:5;i:7826192;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338624;i:4;a:0:{}i:5;i:7826232;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338745;i:4;a:0:{}i:5;i:7828448;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338828;i:4;a:0:{}i:5;i:7830104;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.338948;i:4;a:0:{}i:5;i:7832320;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339008;i:4;a:0:{}i:5;i:7831824;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339124;i:4;a:0:{}i:5;i:7834040;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339199;i:4;a:0:{}i:5;i:7833984;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339382;i:4;a:0:{}i:5;i:7838000;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.33947;i:4;a:0:{}i:5;i:7836312;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339638;i:4;a:0:{}i:5;i:7840328;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339727;i:4;a:0:{}i:5;i:7838544;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339893;i:4;a:0:{}i:5;i:7842560;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.339974;i:4;a:0:{}i:5;i:7841184;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340126;i:4;a:0:{}i:5;i:7845200;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34021;i:4;a:0:{}i:5;i:7843520;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340376;i:4;a:0:{}i:5;i:7847536;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340501;i:4;a:0:{}i:5;i:7845872;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340675;i:4;a:0:{}i:5;i:7849888;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340767;i:4;a:0:{}i:5;i:7848232;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.340914;i:4;a:0:{}i:5;i:7852248;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341001;i:4;a:0:{}i:5;i:7850592;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341172;i:4;a:0:{}i:5;i:7854608;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341264;i:4;a:0:{}i:5;i:7853264;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341433;i:4;a:0:{}i:5;i:7857280;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341519;i:4;a:0:{}i:5;i:7855592;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341666;i:4;a:0:{}i:5;i:7859608;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341726;i:4;a:0:{}i:5;i:7857392;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.341912;i:4;a:0:{}i:5;i:7861408;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342039;i:4;a:0:{}i:5;i:7859008;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.3422;i:4;a:0:{}i:5;i:7861208;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342362;i:4;a:0:{}i:5;i:7861648;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.34251;i:4;a:0:{}i:5;i:7864120;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342636;i:4;a:0:{}i:5;i:7863880;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342785;i:4;a:0:{}i:5;i:7866352;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.342894;i:4;a:0:{}i:5;i:7866112;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343025;i:4;a:0:{}i:5;i:7868584;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343089;i:4;a:0:{}i:5;i:7867840;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343223;i:4;a:0:{}i:5;i:7870312;}i:609;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343365;i:4;a:0:{}i:5;i:7876432;}i:610;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532792.343503;i:4;a:0:{}i:5;i:7878896;}}}";s:5:"event";s:7022:"a:40:{i:0;a:5:{s:4:"time";d:1761532792.08307;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1761532792.086688;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1761532792.086697;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:3;a:5:{s:4:"time";d:1761532792.091989;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:4;a:5:{s:4:"time";d:1761532792.100977;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:5;a:5:{s:4:"time";d:1761532792.127223;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1761532792.151834;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:7;a:5:{s:4:"time";d:1761532792.32483;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\db\ActiveRecord";}i:8;a:5:{s:4:"time";d:1761532792.345109;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:9;a:5:{s:4:"time";d:1761532792.345341;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:10;a:5:{s:4:"time";d:1761532792.346445;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:11;a:5:{s:4:"time";d:1761532792.350138;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:12;a:5:{s:4:"time";d:1761532792.350281;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:13;a:5:{s:4:"time";d:1761532792.356083;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:14;a:5:{s:4:"time";d:1761532792.356965;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:15;a:5:{s:4:"time";d:1761532792.379932;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:16;a:5:{s:4:"time";d:1761532792.380126;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:17;a:5:{s:4:"time";d:1761532792.380454;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:18;a:5:{s:4:"time";d:1761532792.380485;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1761532792.380572;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1761532792.380903;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:21;a:5:{s:4:"time";d:1761532792.381026;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:22;a:5:{s:4:"time";d:1761532792.381108;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:1761532792.381735;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:1761532792.381783;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:1761532792.382311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:26;a:5:{s:4:"time";d:1761532792.382319;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:27;a:5:{s:4:"time";d:1761532792.382391;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:28;a:5:{s:4:"time";d:1761532792.382876;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1761532792.382981;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:1761532792.383202;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:1761532792.383214;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:32;a:5:{s:4:"time";d:1761532792.383247;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1761532792.383259;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:34;a:5:{s:4:"time";d:1761532792.383267;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:35;a:5:{s:4:"time";d:1761532792.383273;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:36;a:5:{s:4:"time";d:1761532792.383281;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:37;a:5:{s:4:"time";d:1761532792.383289;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:38;a:5:{s:4:"time";d:1761532792.383666;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:39;a:5:{s:4:"time";d:1761532792.383854;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1761532792.062595;s:3:"end";d:1761532792.386867;s:6:"memory";i:9021160;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:650:"a:3:{s:8:"messages";a:3:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532792.085186;i:4;a:0:{}i:5;i:4745208;}i:6;a:6:{i:0;s:42:"Request parsed with URL rule: gii/<id:\w+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1761532792.085198;i:4;a:0:{}i:5;i:4746896;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532792.085201;i:4;a:0:{}i:5;i:4747200;}}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";}";s:7:"request";s:9311:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:4:"host";s:9:"localhost";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"899";s:13:"cache-control";s:9:"max-age=0";s:9:"sec-ch-ua";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:6:"origin";s:16:"http://localhost";s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:14:"sec-fetch-site";s:11:"same-origin";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-dest";s:8:"document";s:7:"referer";s:39:"http://localhost/aleko-bo/web/gii/model";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:6:"cookie";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/7.4.33";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68fedb7813259";s:16:"X-Debug-Duration";s:3:"322";s:12:"X-Debug-Link";s:50:"/aleko-bo/web/debug/default/view?tag=68fedb7813259";}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";s:12:"actionParams";a:1:{s:2:"id";s:5:"model";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:899:"_csrf=LuClEDJx3SiKKZEPQljwCt7IHG0gpiFrq3BEKlw2xe1YrsFEZB6La8ROwVYbAZ1Z6v5dAnfcflnZQD1TK3ummw%3D%3D&Generator%5BtableName%5D=bundle_purchase&Generator%5BmodelClass%5D=BundlePurchase&Generator%5BstandardizeCapitals%5D=0&Generator%5Bsingularize%5D=0&Generator%5Bns%5D=app%5Cmodels&Generator%5BbaseClass%5D=yii%5Cdb%5CActiveRecord&Generator%5Bdb%5D=db&Generator%5BuseTablePrefix%5D=0&Generator%5BgenerateRelations%5D=all&Generator%5BgenerateRelationsFromCurrentSchema%5D=0&Generator%5BgenerateRelationsFromCurrentSchema%5D=1&Generator%5BgenerateLabelsFromComments%5D=0&Generator%5BgenerateQuery%5D=0&Generator%5BqueryNs%5D=app%5Cmodels&Generator%5BqueryBaseClass%5D=yii%5Cdb%5CActiveQuery&Generator%5BenableI18N%5D=0&Generator%5BmessageCategory%5D=app&Generator%5BuseSchemaName%5D=0&Generator%5BuseSchemaName%5D=1&Generator%5Btemplate%5D=default&answers%5Baf5500b2dfeacde686e4a752a091b90e%5D=1&generate=";s:7:"Decoded";a:4:{s:5:"_csrf";s:88:"LuClEDJx3SiKKZEPQljwCt7IHG0gpiFrq3BEKlw2xe1YrsFEZB6La8ROwVYbAZ1Z6v5dAnfcflnZQD1TK3ummw==";s:9:"Generator";a:18:{s:9:"tableName";s:15:"bundle_purchase";s:10:"modelClass";s:14:"BundlePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"answers";a:1:{s:32:"af5500b2dfeacde686e4a752a091b90e";s:1:"1";}s:8:"generate";s:0:"";}}s:6:"SERVER";a:61:{s:16:"REDIRECT_MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:19:"REDIRECT_MYSQL_HOME";s:16:"\xampp\mysql\bin";s:21:"REDIRECT_OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:29:"REDIRECT_PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:14:"REDIRECT_PHPRC";s:10:"\xampp\php";s:12:"REDIRECT_TMP";s:10:"\xampp\tmp";s:15:"REDIRECT_STATUS";s:3:"200";s:7:"MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:9:"localhost";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"899";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:14:"HTTP_SEC_CH_UA";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:11:"HTTP_ORIGIN";s:16:"http://localhost";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:12:"HTTP_REFERER";s:39:"http://localhost/aleko-bo/web/gii/model";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:11:"HTTP_COOKIE";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";s:4:"PATH";s:1305:"c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\System32;C:\Windows;C:\Windows\System32\wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files (x86)\cloudflared;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files (x;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files (x86)\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\Zed\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:95:"<address>Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33 Server at localhost Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:38:"C:/xampp/htdocs/aleko-bo/web/index.php";s:11:"REMOTE_PORT";s:5:"55681";s:12:"REDIRECT_URL";s:23:"/aleko-bo/web/gii/model";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:23:"/aleko-bo/web/gii/model";s:11:"SCRIPT_NAME";s:23:"/aleko-bo/web/index.php";s:8:"PHP_SELF";s:23:"/aleko-bo/web/index.php";s:18:"REQUEST_TIME_FLOAT";d:1761532792.053406;s:12:"REQUEST_TIME";i:1761532792;}s:3:"GET";a:1:{s:2:"id";s:5:"model";}s:4:"POST";a:4:{s:5:"_csrf";s:88:"LuClEDJx3SiKKZEPQljwCt7IHG0gpiFrq3BEKlw2xe1YrsFEZB6La8ROwVYbAZ1Z6v5dAnfcflnZQD1TK3ummw==";s:9:"Generator";a:18:{s:9:"tableName";s:15:"bundle_purchase";s:10:"modelClass";s:14:"BundlePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"answers";a:1:{s:32:"af5500b2dfeacde686e4a752a091b90e";s:1:"1";}s:8:"generate";s:0:"";}s:6:"COOKIE";a:1:{s:5:"_csrf";s:130:"5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a:2:{i:0;s:5:"_csrf";i:1;s:32:"vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:2082:"a:5:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:6:"yii.js";}s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:55:"C:\xampp\htdocs\aleko-bo\vendor/bower-asset/jquery/dist";s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\62d52359";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/62d52359";s:7:"depends";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\gii\GiiAsset";a:9:{s:10:"sourcePath";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/assets";s:3:"css";a:1:{i:0;s:12:"css/main.css";}s:2:"js";a:2:{i:0;s:20:"js/bs4-native.min.js";i:1;s:9:"js/gii.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\82e5729c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/82e5729c";s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68fedb7813259";s:3:"url";s:39:"http://localhost/aleko-bo/web/gii/model";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:1761532792.053406;s:10:"statusCode";i:200;s:8:"sqlCount";i:199;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9021160;s:14:"processingTime";d:0.32273006439208984;}s:10:"exceptions";a:0:{}}