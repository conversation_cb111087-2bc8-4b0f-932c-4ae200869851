<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "shop_card_lookup".
 *
 * @property int $id
 * @property int $rarity_id
 * @property string $card_type
 * @property float $card_price
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property RarityLookup $rarity
 */
class ShopCardLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shop_card_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rarity_id', 'card_type', 'card_price', 'created_at'], 'required'],
            [['rarity_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['card_price'], 'number'],
            [['card_type'], 'string', 'max' => 255],
            [['rarity_id'], 'exist', 'skipOnError' => true, 'targetClass' => RarityLookup::className(), 'targetAttribute' => ['rarity_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rarity_id' => 'Rarity ID',
            'card_type' => 'Card Type',
            'card_price' => 'Card Price',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Rarity]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRarity()
    {
        return $this->hasOne(RarityLookup::className(), ['id' => 'rarity_id']);
    }
}
