<?php

use yii\db\Migration;

class m251008_093818_create_song_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%song_lookup}}', [
            'id' => $this->primaryKey(),
            'song_name' => $this->string()->notNull(),
            'song_cover_image' => $this->text(),
            'song_description' => $this->text(),
            'tapping_count' => $this->integer()->notNull(),
            'song_type' => $this->string()->notNull(),
            'is_active' => 'tinyint(1) DEFAULT 1 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%song_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093818_create_song_lookup cannot be reverted.\n";

        return false;
    }
    */
}
