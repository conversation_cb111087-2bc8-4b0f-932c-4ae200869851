export default function (sequelize, DataTypes) {
    return sequelize.define('shop_card_items_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        item_one: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        item_two: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        item_three: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'shop_card_items_lookup',
        timestamps: false,
    })
}