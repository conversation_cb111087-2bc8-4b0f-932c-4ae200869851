export default function (sequelize, DataTypes) {
    return sequelize.define('shop_item_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        item_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        item_icon_image: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        item_category: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        item_type: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        item_boost_percentage: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },
        item_bundle_amount: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },
        item_price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'shop_item_lookup',
        timestamps: false,
    })
}