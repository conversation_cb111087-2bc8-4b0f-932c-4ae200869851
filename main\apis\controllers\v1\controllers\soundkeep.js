import server from "../../../../shared/imports/server.js";
import common from "../../../../shared/imports/common.js";
import query from "../../../../shared/imports/query.js"
import validator from "../../../../shared/imports/validator.js"
import helper from "../../../../shared/imports/helper.js";
const router = server.express.Router();

const RARITY_SEQUENCE = ['normal', 'rare', 'legendary', 'mythic'];
const groupAndSortInstruments = (raw_instruments) => {
    const grouped = raw_instruments.reduce((acc, instrument_instance) => {
        const { instrument_lookup, rarity_lookup } = instrument_instance;
        const instrument_name = instrument_lookup.instrument_name;
        const rarity_name = rarity_lookup.rarity_name;
        const group_key = `${instrument_name}|${rarity_name}`;
        if (!acc[group_key]) {
            acc[group_key] = {
                instrument_id: instrument_lookup.id,
                instrument_name: instrument_name,
                rarity_id: rarity_lookup.id,
                rarity_name: rarity_name,
                quantity: 0,
                lookup_data: instrument_lookup
            };
        }
        acc[group_key].quantity++;
        return acc;
    }, {});

    const flattened_list = Object.values(grouped).map(item => {
        const lookup = item.lookup_data;
        const rarity = item.rarity_name;
        let image_url = null;

        const specific_image_key = `instrument_image_${rarity}`;
        if (lookup[specific_image_key] && lookup[specific_image_key] !== "-") {
            image_url = lookup[specific_image_key];
        }

        return {
            instrument_id: item.instrument_id,
            instrument_name: item.instrument_name,
            rarity_id: item.rarity_id,
            rarity_name: item.rarity_name,
            quantity: item.quantity,
            image_url: image_url ?? "-"
        };
    });

    flattened_list.sort((a, b) => {
        if (a.instrument_name < b.instrument_name) return -1;
        if (a.instrument_name > b.instrument_name) return 1;
        const rarity_a_index = RARITY_SEQUENCE.indexOf(a.rarity_name);
        const rarity_b_index = RARITY_SEQUENCE.indexOf(b.rarity_name);
        return rarity_a_index - rarity_b_index;
    });

    return flattened_list;
};
router.get("/instruments", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const instruments = await query.models.user_shop_card.findAll({
            where: {
                user_id: user_id,
            },
            include: [
                {
                    model: query.models.instrument_lookup,
                    as: "instrument_lookup",
                },
                {
                    model: query.models.rarity_lookup,
                    as: "rarity_lookup",
                },
            ],
            transaction,
        })

        const processed_instruments = groupAndSortInstruments(instruments);

        await transaction.commit();
        return res.status(200).json({
            data: {
                instruments: processed_instruments,
            },
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

const CATEGORY_SEQUENCE = ['bundle', 'booster'];
const ITEM_TYPE_SEQUENCE = ['exp', 'tap', 'token'];
const processShopItems = (raw_items) => {
    const grouped = raw_items.reduce((acc, item_instance) => {
        const lookup = item_instance.shop_item_lookup;
        let group_key;
        let display_data = {};
        if (lookup.item_category === 'bundle') {
            group_key = `bundle|${lookup.item_type}|${lookup.item_bundle_amount}`;
            display_data = {
                item_bundle_amount: lookup.item_bundle_amount,
            };
        } else if (lookup.item_category === 'booster') {
            group_key = `booster|${lookup.item_type}|${lookup.item_boost_percentage}`;
            display_data = {
                item_boost_percentage: lookup.item_boost_percentage,
            };
        } else {
            group_key = `other|${lookup.item_name}`;
        }

        if (!acc[group_key]) {
            acc[group_key] = {
                item_id: lookup.id,
                item_name: lookup.item_name,
                item_icon_image: lookup.item_icon_image,
                item_category: lookup.item_category,
                item_type: lookup.item_type,
                total_quantity: 0,
                ...display_data
            };
        }
        acc[group_key].total_quantity++;
        return acc;
    }, {});
    const flattened_list = Object.values(grouped);

    flattened_list.sort((a, b) => {
        const category_a_index = CATEGORY_SEQUENCE.indexOf(a.item_category);
        const category_b_index = CATEGORY_SEQUENCE.indexOf(b.item_category);
        if (category_a_index !== category_b_index) {
            if (category_a_index === -1) return 1;
            if (category_b_index === -1) return -1;
            return category_a_index - category_b_index;
        }

        const type_a_index = ITEM_TYPE_SEQUENCE.indexOf(a.item_type);
        const type_b_index = ITEM_TYPE_SEQUENCE.indexOf(b.item_type);
        if (type_a_index !== type_b_index) {
            if (type_a_index === -1) return 1;
            if (type_b_index === -1) return -1;
            return type_a_index - type_b_index;
        }

        if (a.item_category === 'bundle') {
            return parseFloat(a.item_bundle_amount) - parseFloat(b.item_bundle_amount);
        } else if (a.item_category === 'booster') {
            return parseFloat(a.item_boost_percentage) - parseFloat(b.item_boost_percentage);
        }

        return a.item_name.localeCompare(b.item_name);
    });

    return flattened_list;
};
router.get("/item", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const items = await query.models.user_shop_item.findAll({
            where: {
                user_id: user_id,
                is_used: false,
                is_delete: false,
            },
            include: [
                {
                    model: query.models.shop_item_lookup,
                    as: "shop_item_lookup",
                },
            ],
            transaction,
        })

        const processed_items = processShopItems(items);
        await transaction.commit();
        return res.status(200).json({
            data: {
                items: processed_items,
            },
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

router.post("/use-booster", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, validator.exp_validator(validator.items_validator.soundkeepValidator.use_booster_validator), async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    const { booster_item_id } = req.body;
    try {
        //Find compatible booster
        const oldest_booster_item = await query.models.user_shop_item.findOne({
            where: {
                user_id: user_id,
                item_id: booster_item_id,
                is_used: false,
                is_delete: false,
            },
            include: [
                {
                    model: query.models.shop_item_lookup,
                    required: true,
                    as: "shop_item_lookup",
                    where: {
                        item_category: "booster",
                    },
                },
            ],
            order: [
                ["created_at", "ASC"],
            ],
            limit: 1,
            lock: transaction.LOCK.UPDATE,
            transaction,
        })
        if (!oldest_booster_item) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: "Booster not found. Please try again.",
                error: true
            });
        }

        //Current Playing Song
        const current_playing_song = await query.models.user_song_selected_lookup.findOne({
            where: {
                user_id: user_id,
                is_active: true,
                is_delete: false,
            },
            lock: transaction.LOCK.UPDATE,
            transaction,
        })
        if (!current_playing_song) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: "No song is currently playing. Please try again.",
                error: true
            });
        }

        //Dynamically check different booster_user_shop_item_id based on item_type
        const booster_user_shop_item_id_key = `${oldest_booster_item.shop_item_lookup.item_type}_booster_user_shop_item_id`;
        const current_playing_song_booster_user_shop_item_id = current_playing_song[booster_user_shop_item_id_key];
        if (!!current_playing_song_booster_user_shop_item_id) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: `You have already used a ${oldest_booster_item.shop_item_lookup.item_name} on this song. Please try again.`,
                error: true
            });
        }

        //Update Current Playing Song
        await query.models.user_song_selected_lookup.update({
            [booster_user_shop_item_id_key]: oldest_booster_item.id,
            updated_at: common.util_helper.getCurrentEpochTime(),
        }, {
            where: {
                user_id: user_id,
                is_active: true,
                is_delete: false,
            },
            lock: transaction.LOCK.UPDATE,
            transaction,
        })

        //Update User Shop Item
        await query.models.user_shop_item.update({
            is_used: true,
            updated_at: common.util_helper.getCurrentEpochTime(),
        }, {
            where: {
                id: oldest_booster_item.id,
            },
            lock: transaction.LOCK.UPDATE,
            transaction,
        })

        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

export default router;