import { Menu } from "@grammyjs/menu";
import common from "../../shared/imports/common.js";
import helper from "../../shared/imports/helper.js";

export default function (menus, bot, api) {

    const url = `${common.config.game_config.game_app_url}`;
    const Join<PERSON>han<PERSON> = {
        text: "Join TG channel",
        action: common.config.game_config.announcement_channel
    };

    const HowTo = {
        text: "How to play the game 🕹️",
        action: async (ctx) => {
            return await ctx.reply(howToMessage(ctx), {
                reply_markup: howTo,
                parse_mode: "HTML",
                disable_web_page_preview: true,
            })
        },
    };

    const home = new Menu("home")
        .webApp("🚀 ONBOARDING", url).row()
        .url(JoinChannel.text, JoinChannel.action).row()
    // .text(HowTo.text, HowTo.action);

    const howTo = new Menu("howTo")
        .webApp("Join the Game 🪙", url).row()
        .url(JoinChannel.text, JoinChannel.action).row();

    function welcome(ctx) {
        return `
"🎶 Welcome to Tales of Aleko! 🎶

<span class="tg-spoiler">📜 Complete Quests | 💰 Earn Rewards | 🪂 Catch AIRDROPS</span>

🔗 Bring your friends, the more you invite, the more you earn 💰

<span class="tg-spoiler">🤔 WHAT ARE YOU WAITING FOR!</span>
    `;
    }

    function howToMessage(ctx) {
        return `
<a href="${common.config.game_config.game_notion_guide}">Full version of the guide</a>

👥 Invite
Invite your friends to earn $ALEKO! The more you invite, the more you earn!

🎁 Treasure Chest
.....................

🎟️ Ticket
.....................

💵 USD
.....................

💰 Additional Earn
.....................

👾 Play (COMING SOON)
.....................

/start for brief intro of Aleko
`;
    }

    async function showMenu(ctx) {
        if (!helper.bot_helper.user_context[ctx.from.id]) {
            await helper.bot_helper.initializeUser(ctx);
        }

        const menu_button = {
            type: 'web_app',
            text: '🚀 ONBOARDING',
            web_app: {
                url: url,
            }
        };
        await api.setChatMenuButton({
            chat_id: ctx.chat.id,
            menu_button: menu_button
        })
        return await ctx.replyWithPhoto(common.config.telegram.welcome_image, {
            caption: welcome(ctx),
            reply_markup: home,
            parse_mode: "HTML",
            disable_web_page_preview: true,
        });
    }

    bot.use(home)
    home.register(howTo);
    return { home, showMenu };
}