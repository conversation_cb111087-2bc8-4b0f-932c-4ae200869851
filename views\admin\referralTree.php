<?php

use yii\web\View;
use yii\helpers\Html;
use yii\widgets\Breadcrumbs;
use yii\widgets\Menu;

$this->title = Yii::t('app', 'View Tree ');

function renderTreeView($items) {
  $html = '<ul class="tree-view">';
  foreach ($items as $item) {
    $html .= '<li>';
    if (!empty($item['items'])) {
      // If there are children, make the label a link for toggling
      $html .= Html::a(Html::decode($item['label']), '#', [
        'class'   => 'tree-toggler',
        'onclick' => 'toggleTree(this); return false;',
      ]);
      // Add a class to child ul for CSS targeting
      $html .= '<ul class="tree-child" style="display: none;">'; // Hide children by default
      $html .= renderTreeView($item['items']); // Render the children
      $html .= '</ul>';
    } else {
      // No children, just display the label
      $html .= Html::decode($item['label']);
    }
    $html .= '</li>';
  }
  $html .= '</ul>';
  return $html;
}

$this->registerJs("
  function toggleTree(element) {
    $(element).parent().children('ul').toggle();
  }
", View::POS_END);

$this->registerCss("
  .tree-view-container {
    max-width: 100%;
    overflow: auto;
  }

  .tree-view {
    white-space: nowrap;
  }

  .tree-view, .tree-view ul {
    padding-left: 20px;
  }

  .tree-child {
    display: none;
  }
");

$username = !empty($data->user->username) 
    ? $data->user->username 
    : $data->user->first_last_name;

?>

<div class="card card-default">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i>{link} / </i> </li>\n",
    'links' => [
      ['label' => 'Referral Management', 'url' => ['admin/referral-mgmt']],
      $this->title,
    ],
  ]);
  ?>
  <div class ="card-body">
    <h3><?= Yii::t('app', 'User - ') . $username . ' (' . $data->user->wallet_address . ')' ?></h3>
    <div class="tree-view-container">
      <?= renderTreeView($json) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::a(Yii::t('app', 'Back'), 'referral-mgmt', ['class' => 'btn btn-default']); ?>
  </div>
</div>
