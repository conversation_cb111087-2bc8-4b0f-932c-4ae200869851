<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\helpers\BaseUrl;
use yii\helpers\Url;
use yii\httpclient\Client;

class TonHelper extends Component
{
  /**
  * Get the TON balance for a given address.
  *
  * @param string $address The TON address to query.
  * @return float The balance of the given TON address.
  */
  public function getTonBalance($address)
  {
    $client = new Client();

    $apiUrl = "https://toncenter.com/api/v2/getAddressBalance";

    try {
      $response = $client->createRequest()
      ->setMethod('GET')
      ->setUrl($apiUrl)
      ->setData(['address' => $address])
      ->send();

      if ($response->isOk) {
        $data = $response->data;
        return isset($data['result']) ? (float)$data['result'] * 1000000000 : 0;
      }
    } catch (\Exception $e) {
      throw new \Exception($e->getMessage());
      return 0;
    }

    return 0;
  }
}