<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_social_task".
 *
 * @property int $id
 * @property int $user_id
 * @property int $social_task_id
 * @property int|null $wallet_id
 * @property int|null $level_id
 * @property int $is_done
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property SocialTaskLookup $socialTask
 * @property User $user
 * @property Wallet $wallet
 */
class UserSocialTask extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_social_task';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wallet_id', 'level_id', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'social_task_id', 'created_at'], 'required'],
            [['user_id', 'social_task_id', 'wallet_id', 'level_id', 'is_done', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
            [['social_task_id'], 'exist', 'skipOnError' => true, 'targetClass' => SocialTaskLookup::class, 'targetAttribute' => ['social_task_id' => 'id']],
            [['wallet_id'], 'exist', 'skipOnError' => true, 'targetClass' => Wallet::class, 'targetAttribute' => ['wallet_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'social_task_id' => 'Social Task ID',
            'wallet_id' => 'Wallet ID',
            'level_id' => 'Level ID',
            'is_done' => 'Is Done',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[SocialTask]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSocialTask()
    {
        return $this->hasOne(SocialTaskLookup::class, ['id' => 'social_task_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Wallet]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::class, ['id' => 'wallet_id']);
    }

}
