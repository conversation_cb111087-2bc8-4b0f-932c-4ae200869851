import fs from "fs";
import path from "path";
import { fileURLToPath } from 'url';
import csv from "csv-parser";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";


function sanitizeRow(row) {
    const cleanedData = {
        id: parseInt(row.id, 10),
        user_id: parseInt(row.user_id, 10),
        is_verified: row.verified.toUpperCase() === 'TRUE',
        referred_by: row.referred_by ? parseInt(row.referred_by, 10) : null,
        rewards_earned: parseInt(row.rewards_earned, 10),
        referrals: parseInt(row.referrals, 10),
        username: row.user_name,
    };
    return cleanedData;
}

async function processPiecesOfShit(csvFilePath) {
    if (!fs.existsSync(csvFilePath)) {
        console.error(`Error: File not found at ${csvFilePath}`);
        return;
    }

    console.log(`Starting to process file: ${csvFilePath}`);
    const all_users_telegram_id = [];
    await new Promise((resolve, reject) => {
        fs.createReadStream(csvFilePath)
            .pipe(csv())
            .on('data', (row) => {
                const cleaned_row = sanitizeRow(row);
                const user_telegram_id = cleaned_row.user_id;
                all_users_telegram_id.push(user_telegram_id);
            })
            .on('end', resolve)
            .on('error', reject);
    });


    for (const user_telegram_id of all_users_telegram_id) {
        const find_user = await query.models.user.findOne({
            where: {
                user_telegram_id: user_telegram_id
            }
        })

        if (!find_user) {
            console.log("User not exist. User_telegram_id: ", user_telegram_id);
            continue;
        }

        console.log("Process user_telegram_id: ", user_telegram_id, "User_id: ", find_user.id);
        const invite_count = await query.models.user_referral.count({
            where: {
                referral_user_id: find_user.id,
                is_delete: 0
            },
        })
        if (invite_count > 0) {
            const unlocked_tiers = await helper.referral_helper.findNewlyUnlockedTiers(find_user.id, invite_count);
            console.log("User contribute to upline part (to awarded): ", unlocked_tiers);

            if (unlocked_tiers && unlocked_tiers.length > 0) {
                const records_to_create = unlocked_tiers.map(tier => {
                    return {
                        user_id: find_user.id,
                        referral_reward_id: tier.id,
                        is_claim: false,
                        created_at: common.util_helper.getCurrentEpochTime(),
                        is_delete: 0,
                    };
                });
                await query.models.user_referral_reward.bulkCreate(records_to_create, {
                    // transaction: transaction,
                });
                console.log(`Successfully created ${records_to_create.length} new reward records for UserID ${find_user.id}`);
                unlocked_tiers.sort((a, b) => b.invite_tier_count - a.invite_tier_count);
                let highest_unlocked_tier = unlocked_tiers[0];
                console.log(`The highest new title for the upline is: ${highest_unlocked_tier.special_tier_title}`);

                if (highest_unlocked_tier.special_tier_title !== null) {
                    await query.models.user.update({
                        current_referral_ranking: highest_unlocked_tier.special_tier_title,
                        updated_at: common.util_helper.getCurrentEpochTime()
                    }, {
                        where: {
                            id: find_user.id
                        },
                        // transaction: transaction
                    })
                }
            }
        }
    }
}


const csvFilePath = path.join(__dirname, '..', '..', 'shared', 'assets', 'user_list_sep10_v2.csv');
processPiecesOfShit(csvFilePath)