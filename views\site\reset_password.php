<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;

$this->title = 'Reset Password';
?>

<div class="site-login">
  <div class="card card-outline card-primary">
    <div class="card-header text-center">
      <?= $this->render('./_alert_flash', []) ?>
      <h5><?= Yii::t('app', 'Reset Your Password') ?></h5>
    </div>

    <?php $form = ActiveForm::begin([
      'id' => 'resetpassword-form',
    ]); ?>
    <div class="card-body p-3">
      <div class="form-group">
        <?= $form->field($model, 'email', [
          'options' => ['class' => 'form-group '],
          'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-envelope"></i></div></div>',
          'template' => '{beginWrapper}{input}{error}{endWrapper}',
          'wrapperOptions' => ['class' => 'input-group'],
        ])->textInput(['placeholder' => $model->getAttributeLabel('email')]) ?>
      </div>
      <div class="form-group">
        <?= $form->field($model, 'otp_code', [
          'options' => ['class' => 'form-group '],
          'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-code"></i></div></div>',
          'template' => '{beginWrapper}{input}{error}{endWrapper}',
          'wrapperOptions' => ['class' => 'input-group'],
        ])->textInput(['placeholder' => 'OTP CODE']) ?>
      </div>
      <div class="form-group">
        <?= $form->field($model, 'password', [
          'options' => ['class' => 'form-group '],
          'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-lock"></i></div></div>',
          'template' => '{beginWrapper}{input}{error}{endWrapper}',
          'wrapperOptions' => ['class' => 'input-group'],
        ])->passwordInput(['placeholder' => $model->getAttributeLabel('password')]) ?>
      </div>
      <div class="form-group">
        <?= $form->field($model, 'retype_password', [
          'options' => ['class' => 'form-group '],
          'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-lock"></i></div></div>',
          'template' => '{beginWrapper}{input}{error}{endWrapper}',
          'wrapperOptions' => ['class' => 'input-group'],
        ])->passwordInput(['placeholder' => $model->getAttributeLabel('retype_password')]) ?>
      </div>
    </div>
    <div class="card-footer">
      <a href="<?= \yii\helpers\Url::home() ?>" class="btn btn-secondary">Back</a>
      <?= Html::submitButton('Reset Password', [
        'class' => 'btn btn-primary float-right',
        'name'  => 'Reset',
        'value' => 1,
      ]) ?>
    </div>
    <?php ActiveForm::end(); ?>
  </div>
</div>