<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "wallet".
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $wallet_address
 * @property string|null $hash_id
 * @property string|null $group_tag
 * @property int|null $referral_wallet_id
 * @property string $referral_code
 * @property string $network_type
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Credit[] $credits
 * @property User $user
 * @property UserInviteReward[] $userInviteRewards
 * @property UserNoticeReward[] $userNoticeRewards
 * @property UserSocialTask[] $userSocialTasks
 * @property WalletTransaction[] $walletTransactions
 */
class Wallet extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'wallet';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wallet_address', 'hash_id', 'group_tag', 'referral_wallet_id', 'updated_at'], 'default', 'value' => null],
            [['network_type'], 'default', 'value' => 'ton'],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'created_at'], 'required'],
            [['user_id', 'referral_wallet_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['wallet_address'], 'string'],
            [['hash_id', 'group_tag', 'referral_code', 'network_type'], 'string', 'max' => 255],
            [['referral_code'], 'unique'],
            [['wallet_address'], 'unique'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'wallet_address' => 'Wallet Address',
            'hash_id' => 'Hash ID',
            'group_tag' => 'Group Tag',
            'referral_wallet_id' => 'Referral Wallet ID',
            'referral_code' => 'Referral Code',
            'network_type' => 'Network Type',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Credits]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCredits()
    {
        return $this->hasMany(Credit::class, ['wallet_id' => 'id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[UserInviteRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserInviteRewards()
    {
        return $this->hasMany(UserInviteReward::class, ['wallet_id' => 'id']);
    }

    /**
     * Gets query for [[UserNoticeRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserNoticeRewards()
    {
        return $this->hasMany(UserNoticeReward::class, ['wallet_id' => 'id']);
    }

    /**
     * Gets query for [[UserSocialTasks]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSocialTasks()
    {
        return $this->hasMany(UserSocialTask::class, ['wallet_id' => 'id']);
    }

    /**
     * Gets query for [[WalletTransactions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWalletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, ['wallet_id' => 'id']);
    }

}
