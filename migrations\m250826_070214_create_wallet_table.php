<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%wallet}}`.
 */
class m250826_070214_create_wallet_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%wallet}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'wallet_address' => $this->string()->unique(),
            'hash_id' => $this->string(),
            'group_tag' => $this->string(),
            'referral_wallet_id' => $this->integer(),
            'referral_code' => $this->string()->unique()->notNull(),
            'network_type' => $this->string()->notNull()->defaultValue('ton'),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByWalletAddress` (`wallet_address`)',
            'key `findByNetworkType` ( `network_type`, `is_delete`)',
            'key `findByReferralWalletId` (`referral_wallet_id`, `is_delete`)',
            'key `findByReferralCode` (`referral_code`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%wallet}}');
    }
}
