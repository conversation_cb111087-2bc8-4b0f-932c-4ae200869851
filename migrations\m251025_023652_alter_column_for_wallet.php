<?php

use yii\db\Migration;

class m251025_023652_alter_column_for_wallet extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('wallet', 'referral_code', $this->string());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('wallet', 'referral_code', $this->string()->unique()->notNull());
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251025_023652_alter_column_for_wallet cannot be reverted.\n";

        return false;
    }
    */
}
