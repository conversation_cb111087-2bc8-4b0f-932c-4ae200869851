import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"

function referralCodeGenerator() {
    return (
        Math.random().toString(36).substring(2) +
        Math.random().toString(36).substring(2)
    ).substring(0, 12);
}
const buildReferralTag = function (hashId, referralTag = null) {
    if (!referralTag) {
        return `${hashId}:`;
    }
    return `${referralTag}${hashId}:`;
};

const generateHashId = function (id) {
    // convert string id to base 36
    return id.toString(36);
};

async function findUnlockedReferralTiers(total_user_invites) {
    const unlocked_tiers = await query.models.referral_reward_lookup.findAll({
        where: {
            invite_tier_count: {
                [query.Op.lte]: total_user_invites
            },
            is_delete: 0
        },
        order: [
            ['invite_tier_count', 'ASC']
        ],
        raw: true,
    });

    return unlocked_tiers;
}

async function findNewlyUnlockedTiers(upline_user_id, totol_user_invites) {
    const eligible_tier = await findUnlockedReferralTiers(totol_user_invites);

    if (eligible_tier.length === 0) {
        return [];
    }
    const awarded_rewards = await query.models.user_referral_reward.findAll({
        where: {
            user_id: upline_user_id,
            is_delete: false
        }
    })

    const awarded_tier_ids = new Set(awarded_rewards.map(reward => reward.referral_reward_id));
    const new_tiers_to_award = eligible_tier.filter(tier => !awarded_tier_ids.has(tier.id));
    return new_tiers_to_award;
}

export default { referralCodeGenerator, buildReferralTag, generateHashId, findUnlockedReferralTiers, findNewlyUnlockedTiers };
