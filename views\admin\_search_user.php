<?php

use yii\web\View;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;
use app\components\UUID;
use app\models\ReferralRewardLookup;

$uuid = UUID::v4();

$script = <<< JS
  $("#export-btn").click(function () {
    var form  = $("#general-search"),
    input = $("#general-search :input"),
    arr   = [];
    _.each(input, function (i) {
      if (!i.name) {
        return true;
      }
      var name = encodeURIComponent(i.name);
      var val  = encodeURIComponent(i.value);
      arr.push([name, val].join("="));
    });

    var uuid = '$uuid';
    arr.push("export=1");
    arr.push("uuid="+uuid);

    var param  = arr.join("&"),
    url    = form.attr("action"),
    conn   = "&";

    if (url.indexOf("?") === -1) {
      conn   = "?";
    }
    url = [url, conn, param].join("");
    console.log(uuid);

    window.open(url);
  });
JS;

$this->registerJs($script, View::POS_END);

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'username')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Username')) ?>
      <?= $form->field($model, 'user_telegram_id')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Telegram ID')) ?>
      <?= $form->field($model, 'user_x_id')->textInput(['placeholder' => ''])->label(Yii::t('app', 'X ID')) ?>
      <?= $form->field($model, 'user_instagram_id')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Instagram ID')) ?>
      <?= $form->field($model, 'wallet_address')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Wallet Address')) ?>
      <?= $form->field($model, 'has_joined')->dropDownList([
        '' => Yii::t('app', 'All'),
        1  => Yii::t('app', 'Yes'),
        0  => Yii::t('app', 'No'),
      ])->label(Yii::t('app', 'Has Joined Channel')) ?>
      <?= $form->field($model, 'current_referral_ranking')->dropDownList(ReferralRewardLookup::getAllReferralTierTitleList(), [
          'prompt' => Yii::t('app', 'All'),
          ])->label(Yii::t('app', 'Referral Tier Title')) ?>
      <?= $form->field($model, 'date_range')->widget(DateRangePicker::className(), [
        'model'         => $model,
        'attribute'     => 'date_range',
        'convertFormat' => true,
        'pluginOptions' => [
          'timePicker'  => false,
          'locale' => [
            'format' => 'Y-m-d'
          ]
        ],
        'options' => [],
      ])->label(Yii::t('app', 'Date')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-success float-right']) ?>
    <?= Html::a(Yii::t('app', 'Export'), "javascript:void(0)", [
      'id'    => 'export-btn',
      'class' => 'btn btn-warning float-right mr-2',
    ]) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>
