<?php

use yii\db\Migration;

class m250902_021129_alter_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->execute('ALTER TABLE `user` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
       $this->execute('ALTER TABLE `user` CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci;'); 
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250902_021129_alter_user_table cannot be reverted.\n";

        return false;
    }
    */
}
