<?php

use yii\db\Migration;

class m251025_035024_alter_column_wallet_transaction extends Migration
{
    public function safeUp()
    {
        $this->alterColumn('{{%wallet_transaction}}', 'amount', $this->decimal(36, 18)->notNull());
        $this->alterColumn('{{%wallet_transaction}}', 'before_balance', $this->decimal(36, 18)->notNull());
        $this->alterColumn('{{%wallet_transaction}}', 'after_balance', $this->decimal(36, 18)->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('{{%wallet_transaction}}', 'amount', $this->decimal(36, 8)->notNull());
        $this->alterColumn('{{%wallet_transaction}}', 'before_balance', $this->decimal(36, 8)->notNull());
        $this->alterColumn('{{%wallet_transaction}}', 'after_balance', $this->decimal(36, 8)->notNull());
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251025_035024_alter_column_wallet_transaction cannot be reverted.\n";

        return false;
    }
    */
}
