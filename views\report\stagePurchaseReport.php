<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use kartik\grid\GridView;
use supplyhog\ClipboardJs\ClipboardJsWidget;

$this->title = Yii::t('app', 'Stage Purchase History');

$this->registerCss('
  .overflow-bg-danger {
    background-color: #e8b4b8 !important;
  }
');
?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_stage_purchase', [
      'model'   => $model,
      'page'    => 'stage-purchase-report',
      'pjax_id' => "#pjax-stage-purchase-report",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="row mb-2">
      <div class="col-12">
        <div class="card" style="border: 1px solid #dee2e6; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div class="card-header py-3" style="background-color: #f1f3f4; border-bottom: 2px solid #dee2e6;">
            <h6 class="mb-0" style="font-size: 0.9rem; font-weight: 500; color: #495057;">
              <i class="fas fa-chart-bar mr-2" style="font-size: 0.9rem; color: #007bff;"></i>
              Purchase Summary
            </h6>
          </div>
          <div class="card-body p-0" style="background-color: #ffffff;">
            <div class="table-responsive">
              <table class="table mb-0" style="border-collapse: separate; border-spacing: 0;">
                <thead>
                  <tr style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th class="px-4 py-3" style="color: #495057; font-weight: 600; font-size: 0.85rem; border-right: 1px solid #e9ecef;">Stage</th>
                    <th class="px-4 py-3 text-center" style="color: #495057; font-weight: 600; font-size: 0.85rem; border-right: 1px solid #e9ecef;">Total Token</th>
                    <th class="px-4 py-3 text-center" style="color: #495057; font-weight: 600; font-size: 0.85rem;">Total USD</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($summary as $stage): ?>
                    <tr style="border-bottom: 1px solid #e9ecef;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='transparent'">
                      <td class="px-4 py-3" style="font-size: 0.85rem; color: #495057; font-weight: 500; border-right: 1px solid #f1f3f4;">
                        <span style="display: inline-block; width: 6px; height: 6px; background-color: #007bff; border-radius: 50%; margin-right: 8px;"></span>
                        <?= Html::encode($stage['stage_name']) ?>
                      </td>
                      <td class="px-4 py-3 text-center" style="font-weight: 500; font-size: 0.85rem; color: #495057; border-right: 1px solid #f1f3f4;">
                        <span style="color: #28a745; font-weight: 600;"><?= number_format($stage['total_token'] * 1) ?></span>
                        <span style="color: #6c757d;"> / </span>
                        <span style="color: #6c757d;"><?= number_format($stage['token_available'] * 1) ?></span>
                      </td>
                      <td class="px-4 py-3 text-center" style="font-weight: 500; font-size: 0.85rem; color: #495057;">
                        <span style="color: #28a745; font-weight: 600;"><?= number_format($stage['total_usd'] * 1) ?></span>
                        <span style="color: #6c757d;"> / </span>
                        <span style="color: #6c757d;"><?= number_format($stage['total_fund'] * 1) ?></span>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
