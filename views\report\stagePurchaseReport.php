<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use kartik\grid\GridView;
use supplyhog\ClipboardJs\ClipboardJsWidget;

$this->title = Yii::t('app', 'Stage Purchase History');

$this->registerCss('
  .overflow-bg-danger {
    background-color: #e8b4b8 !important;
  }
');
?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_stage_purchase', [
      'model'   => $model,
      'page'    => 'stage-purchase-report',
      'pjax_id' => "#pjax-stage-purchase-report",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="row mb-2">
      <div class="col-12">
        <div class="card">
          <div class="card-header py-2 border-bottom-0" style="background-color: #f8f9fa; border-bottom: 1px solid #e9ecef;">
            <h6 class="mb-0 text-muted" style="font-size: 0.875rem; font-weight: normal;">
              <i class="fas fa-chart-bar mr-2" style="font-size: 0.875rem; color: #6c757d;"></i>
              Purchase Summary
            </h6>
          </div>
          <div class="card-body p-0" style="background-color: #ffffff;">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                    <th class="border-0" style="color: #495057; font-weight: normal;">Stage</th>
                    <th class="border-0" style="color: #495057; font-weight: normal;">Total Token</th>
                    <th class="border-0" style="color: #495057; font-weight: normal;">Total USD</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($summary as $stage): ?>
                    <tr style="border-bottom: 1px solid #e9ecef;">
                      <td class="border-0 px-3 py-2" style="font-size: 0.8125rem; color: #495057; font-weight: normal;">
                        <span style="display: inline-block; width: 4px; height: 4px; background-color: #6c757d; border-radius: 50%; margin-right: 6px;"></span>
                        <?= Html::encode($stage['stage_name']) ?>
                      </td>
                      <td class="border-0 px-3 py-2 text-right" style="font-weight: normal; font-size: 0.8125rem; color: #6c757d;">
                        <?= number_format($stage['total_token'] * 1) ?> / <?= number_format($stage['token_available'] * 1) ?>
                      </td>
                      <td class="border-0 px-3 py-2 text-right" style="font-weight: normal; font-size: 0.8125rem; color: #6c757d;">
                        <?= number_format($stage['total_usd'] * 1) ?> / <?= number_format($stage['total_fund'] * 1) ?>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
