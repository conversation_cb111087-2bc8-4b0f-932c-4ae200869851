<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use kartik\grid\GridView;
use supplyhog\ClipboardJs\ClipboardJsWidget;

$this->title = Yii::t('app', 'Stage Purchase History');

$this->registerCss('
  .overflow-bg-danger {
    background-color: #dc3545 !important;
    animation: pulse 2s infinite;
  }
');
?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_stage_purchase', [
      'model'   => $model,
      'page'    => 'stage-purchase-report',
      'pjax_id' => "#pjax-stage-purchase-report",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="row mb-2">
      <div class="col-12">
        <div class="card">
          <div class="card-header py-2 border-bottom-0" style="background: linear-gradient(135deg, rgba(52, 152, 219, 0.9) 0%, rgba(26, 188, 156, 0.9) 100%); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); position: relative; overflow: hidden;">
            <div style="position: absolute; top: -50%; right: -10%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; filter: blur(35px);"></div>
            <div style="position: absolute; bottom: -30%; left: -5%; width: 120px; height: 120px; background: rgba(255, 255, 255, 0.08); border-radius: 50%; filter: blur(25px);"></div>
            <h6 class="mb-0 font-weight-bold text-white" style="position: relative; z-index: 1; font-size: 0.875rem;">
              <i class="fas fa-chart-bar mr-2" style="font-size: 0.875rem;"></i>
              Purchase Summary
            </h6>
          </div>
          <div class="card-body p-0" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);">
            <div class="table-responsive">
              <table class="table mb-0" style="border-collapse: separate; border-spacing: 0; background: transparent;">
                <thead>
                  <tr style="background: linear-gradient(135deg, rgba(52, 152, 219, 0.15) 0%, rgba(26, 188, 156, 0.15) 100%); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-bottom: 2px solid rgba(52, 152, 219, 0.2);">
                    <th class="border-0" style="color: #fff;">Stage</th>
                    <th class="border-0" style="color: #fff;">Total Token</th>
                    <th class="border-0" style="color: #fff;">Total USD</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($summary as $stage): ?>
                    <tr style="border-bottom: 1px solid rgba(52, 152, 219, 0.1);">
                      <td class="border-0 px-3 py-2" style="font-size: 0.8125rem; color: #212529; font-weight: 500;">
                        <span style="display: inline-block; width: 6px; height: 6px; background: linear-gradient(135deg, #3498db 0%, #1abc9c 100%); border-radius: 50%; margin-right: 6px; box-shadow: 0 0 6px rgba(52, 152, 219, 0.5);"></span>
                        <?= Html::encode($stage['stage_name']) ?>
                      </td>
                      <td class="border-0 px-3 py-2 text-right" style="font-weight: 700; font-size: 0.8125rem; color: #28a745; text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);">
                        <?= number_format($stage['total_token'] * 1) ?> / <?= number_format($stage['token_available'] * 1) ?>
                      </td>
                      <td class="border-0 px-3 py-2 text-right" style="font-weight: 700; font-size: 0.8125rem; color: #28a745; text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);">
                        <?= number_format($stage['total_usd'] * 1) ?> / <?= number_format($stage['total_fund'] * 1) ?>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
