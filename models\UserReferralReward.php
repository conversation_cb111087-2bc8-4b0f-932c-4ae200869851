<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_referral_reward".
 *
 * @property int $id
 * @property int $user_id
 * @property int $referral_reward_id
 * @property int $is_claim
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property ReferralRewardLookup $referralReward
 */
class UserReferralReward extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_referral_reward';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'referral_reward_id', 'created_at'], 'required'],
            [['user_id', 'referral_reward_id', 'is_claim', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['user_id' => 'id']],
            [['referral_reward_id'], 'exist', 'skipOnError' => true, 'targetClass' => ReferralRewardLookup::className(), 'targetAttribute' => ['referral_reward_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'referral_reward_id' => 'Referral Reward ID',
            'is_claim' => 'Is Claim',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }

    /**
     * Gets query for [[ReferralReward]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getReferralReward()
    {
        return $this->hasOne(ReferralRewardLookup::className(), ['id' => 'referral_reward_id']);
    }
}
