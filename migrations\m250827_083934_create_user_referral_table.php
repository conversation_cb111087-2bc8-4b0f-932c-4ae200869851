<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_referral}}`.
 */
class m250827_083934_create_user_referral_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_referral}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'hash_id' => $this->string(),
            'group_tag' => $this->string(),
            'referral_user_id' => $this->integer(),
            'referral_code' => $this->string()->notNull()->unique(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByReferralCode` (`referral_code`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_referral}}');
    }
}
