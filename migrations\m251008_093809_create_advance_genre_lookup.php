<?php

use yii\db\Migration;

class m251008_093809_create_advance_genre_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%advance_genre_lookup}}', [
            'id' => $this->primaryKey(),
            'genre_name' => $this->string()->notNull(),
            'boost_instrument_one_id' => $this->integer(),
            'boost_instrument_two_id' => $this->integer(),
            'boost_instrument_three_id' => $this->integer(),
            'boost_instrument_four_id' => $this->integer(),
            'is_active' => 'tinyint(1) DEFAULT 1 NOT NULL',
            'is_instrument_boost_allow' => 'tinyint(1) DEFAULT 1 NOT NULL',
            'genre_type' => $this->string()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`boost_instrument_one_id`) references `instrument_lookup` (`id`)',
            'foreign key (`boost_instrument_two_id`) references `instrument_lookup` (`id`)',
            'foreign key (`boost_instrument_three_id`) references `instrument_lookup` (`id`)',
            'foreign key (`boost_instrument_four_id`) references `instrument_lookup` (`id`)',
            'key `findAll` (`is_delete`)',
        ]);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%advance_genre_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093809_create_advance_genre_lookup cannot be reverted.\n";

        return false;
    }
    */
}
