export default function (sequelize, DataTypes) {
    return sequelize.define('referraL_reward_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        invite_tier_count: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        reward_type: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        reward_amount: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        special_tier_title: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'referral_reward_lookup',
        timestamps: false,
    })
}