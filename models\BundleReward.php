<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "bundle_reward".
 *
 * @property int $id
 * @property int $bundle_id
 * @property string $type
 * @property int $amount
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class BundleReward extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'bundle_reward';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['bundle_id', 'type', 'amount', 'created_at'], 'required'],
            [['bundle_id', 'amount', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['type'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'bundle_id' => 'Bundle ID',
            'type' => 'Type',
            'amount' => 'Amount',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }
}
