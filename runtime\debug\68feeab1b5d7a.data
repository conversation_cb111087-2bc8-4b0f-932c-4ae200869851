a:14:{s:6:"config";s:6366:"a:5:{s:10:"phpVersion";s:6:"7.4.33";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:5:"Aleko";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"7.4.33";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:26:{s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:64:"C:\xampp\htdocs\aleko-bo\vendor/2amigos/yii2-ckeditor-widget/src";}}s:25:"alexantr/yii2-colorpicker";a:3:{s:4:"name";s:25:"alexantr/yii2-colorpicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@alexantr/colorpicker";s:57:"C:\xampp\htdocs\aleko-bo\vendor/alexantr/yii2-colorpicker";}}s:24:"asmoday74/yii2-ckeditor5";a:3:{s:4:"name";s:24:"asmoday74/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:20:"@asmoday74/ckeditor5";s:56:"C:\xampp\htdocs\aleko-bo\vendor/asmoday74/yii2-ckeditor5";}}s:29:"cetver/yii2-language-selector";a:3:{s:4:"name";s:29:"cetver/yii2-language-selector";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:24:"@cetver/LanguageSelector";s:61:"C:\xampp\htdocs\aleko-bo\vendor/cetver/yii2-language-selector";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:65:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:58:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte3/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:61:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:56:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-dialog/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-fileinput/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/date";s:67:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datepicker/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:60:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-date-range/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:71:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-typeahead/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:54:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-grid/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:64:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-select2/src";}}s:20:"nickcv/yii2-mandrill";a:3:{s:4:"name";s:20:"nickcv/yii2-mandrill";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@nickcv/mandrill";s:56:"C:\xampp\htdocs\aleko-bo\vendor/nickcv/yii2-mandrill/src";}}s:23:"sangroya/yii2-ckeditor5";a:3:{s:4:"name";s:23:"sangroya/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@sangroya/ckeditor5";s:55:"C:\xampp\htdocs\aleko-bo\vendor/sangroya/yii2-ckeditor5";}}s:27:"unclead/yii2-multiple-input";a:3:{s:4:"name";s:27:"unclead/yii2-multiple-input";s:7:"version";s:8:"********";s:5:"alias";a:3:{s:22:"@unclead/multipleinput";s:63:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/src";s:28:"@unclead/multipleinput/tests";s:65:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/tests";s:31:"@unclead/multipleinput/examples";s:68:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/examples";}}s:21:"yiier/yii2-aliyun-oss";a:3:{s:4:"name";s:21:"yiier/yii2-aliyun-oss";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@yiier/AliyunOSS";s:53:"C:\xampp\htdocs\aleko-bo\vendor/yiier/yii2-aliyun-oss";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-faker/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-httpclient/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-jui/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:10:"@yii/debug";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-debug/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.1.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:60:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-swiftmailer/src";}}}}";s:3:"log";s:10974:"a:1:{s:8:"messages";a:23:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.736667;i:4;a:0:{}i:5;i:2701312;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.736679;i:4;a:0:{}i:5;i:2702488;}i:2;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.742612;i:4;a:0:{}i:5;i:3436272;}i:3;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.744445;i:4;a:0:{}i:5;i:3881872;}i:4;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.746971;i:4;a:0:{}i:5;i:4201824;}i:5;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.747234;i:4;a:0:{}i:5;i:4225960;}i:12;a:6:{i:0;s:29:"Route requested: 'site/login'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.75081;i:4;a:0:{}i:5;i:4745424;}i:13;a:6:{i:0;s:24:"Route to run: site/login";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.752842;i:4;a:0:{}i:5;i:4929664;}i:14;a:6:{i:0;s:61:"Running action: app\controllers\SiteController::actionLogin()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.757804;i:4;a:0:{}i:5;i:5434256;}i:15;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.759771;i:4;a:1:{i:0;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:109;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:5579208;}i:18;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.761177;i:4;a:1:{i:0;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:109;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:5654544;}i:19;a:6:{i:0;s:101:"SELECT * FROM `admin` WHERE (`email`='<EMAIL>') AND (`role` <> 'user') AND (`is_delete`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.543822;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7310616;}i:22;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.635288;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7354440;}i:25;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.646041;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7374984;}i:28;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.671849;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7398216;}i:31;a:6:{i:0;s:50:"User '1' logged in from ::1 with duration 2592000.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1761536690.794809;i:4;a:2:{i:0;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:62;s:8:"function";s:5:"login";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:124;s:8:"function";s:5:"login";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7396616;}i:32;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.85033;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7679688;}i:35;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.853766;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7697128;}i:38;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.854123;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7697840;}i:41;a:6:{i:0;s:91:"GET http://geoip.fun1881.com/api/::1?&lang=en
Content-Type: application/json; charset=UTF-8";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1761536691.06841;i:4;a:3:{i:0;a:5:{s:4:"file";s:43:"C:\xampp\htdocs\aleko-bo\components\API.php";s:4:"line";i:68;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:123;s:8:"function";s:7:"request";s:5:"class";s:18:"app\components\API";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:165;s:8:"function";s:16:"getCountryIpAddr";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7956656;}i:44;a:6:{i:0;s:69:"SELECT EXISTS(SELECT * FROM `admin` WHERE `admin`.`id`=1) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761536691.898127;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8540736;}i:47;a:6:{i:0;s:336:"INSERT INTO `admin_login_session` (`admin_id`, `action`, `ip_address`, `country_name`, `user_agent`, `device_type`, `created_at`, `updated_at`, `is_delete`) VALUES (1, 'login', '::1', NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Browser', 1761536691, NULL, 0)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1761536691.899943;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8541096;}i:50;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1761536691.919526;i:4;a:1:{i:0;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:131;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}}i:5;i:8539992;}}}";s:9:"profiling";s:16934:"a:3:{s:6:"memory";i:8777848;s:4:"time";d:2.1952829360961914;s:8:"messages";a:22:{i:16;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.759787;i:4;a:1:{i:0;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:109;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:5580336;}i:17;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.760814;i:4;a:1:{i:0;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:109;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:5630424;}i:20;a:6:{i:0;s:101:"SELECT * FROM `admin` WHERE (`email`='<EMAIL>') AND (`role` <> 'user') AND (`is_delete`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.543936;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7312456;}i:21;a:6:{i:0;s:101:"SELECT * FROM `admin` WHERE (`email`='<EMAIL>') AND (`role` <> 'user') AND (`is_delete`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.547336;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7316496;}i:23;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.635318;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7356544;}i:24;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.645974;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7372840;}i:26;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.646057;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7377088;}i:27;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.646298;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7380440;}i:29;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.671879;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7401832;}i:30;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.672333;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7404968;}i:33;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.850355;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7682736;}i:34;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.853719;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7695392;}i:36;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.853784;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7698872;}i:37;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.854007;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7701960;}i:39;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.854136;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7701080;}i:40;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.85451;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7704696;}i:42;a:6:{i:0;s:91:"GET http://geoip.fun1881.com/api/::1?&lang=en
Content-Type: application/json; charset=UTF-8";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1761536691.068428;i:4;a:3:{i:0;a:5:{s:4:"file";s:43:"C:\xampp\htdocs\aleko-bo\components\API.php";s:4:"line";i:68;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:123;s:8:"function";s:7:"request";s:5:"class";s:18:"app\components\API";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:165;s:8:"function";s:16:"getCountryIpAddr";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7958536;}i:43;a:6:{i:0;s:91:"GET http://geoip.fun1881.com/api/::1?&lang=en
Content-Type: application/json; charset=UTF-8";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1761536691.700881;i:4;a:3:{i:0;a:5:{s:4:"file";s:43:"C:\xampp\htdocs\aleko-bo\components\API.php";s:4:"line";i:68;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:123;s:8:"function";s:7:"request";s:5:"class";s:18:"app\components\API";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:165;s:8:"function";s:16:"getCountryIpAddr";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7964880;}i:45;a:6:{i:0;s:69:"SELECT EXISTS(SELECT * FROM `admin` WHERE `admin`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536691.898152;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8542624;}i:46;a:6:{i:0;s:69:"SELECT EXISTS(SELECT * FROM `admin` WHERE `admin`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536691.899813;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8544304;}i:48;a:6:{i:0;s:336:"INSERT INTO `admin_login_session` (`admin_id`, `action`, `ip_address`, `country_name`, `user_agent`, `device_type`, `created_at`, `updated_at`, `is_delete`) VALUES (1, 'login', '::1', NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Browser', 1761536691, NULL, 0)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761536691.899958;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8540840;}i:49;a:6:{i:0;s:336:"INSERT INTO `admin_login_session` (`admin_id`, `action`, `ip_address`, `country_name`, `user_agent`, `device_type`, `created_at`, `updated_at`, `is_delete`) VALUES (1, 'login', '::1', NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Browser', 1761536691, NULL, 0)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761536691.903159;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8542736;}}}";s:2:"db";s:14569:"a:1:{s:8:"messages";a:18:{i:20;a:6:{i:0;s:101:"SELECT * FROM `admin` WHERE (`email`='<EMAIL>') AND (`role` <> 'user') AND (`is_delete`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.543936;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7312456;}i:21;a:6:{i:0;s:101:"SELECT * FROM `admin` WHERE (`email`='<EMAIL>') AND (`role` <> 'user') AND (`is_delete`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.547336;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7316496;}i:23;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.635318;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7356544;}i:24;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.645974;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7372840;}i:26;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.646057;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7377088;}i:27;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.646298;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7380440;}i:29;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.671879;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7401832;}i:30;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.672333;i:4;a:3:{i:0;a:5:{s:4:"file";s:41:"C:\xampp\htdocs\aleko-bo\models\Admin.php";s:4:"line";i:124;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:75;s:8:"function";s:14:"findByUsername";s:5:"class";s:16:"app\models\Admin";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"C:\xampp\htdocs\aleko-bo\forms\LoginForm.php";s:4:"line";i:43;s:8:"function";s:7:"getUser";s:5:"class";s:19:"app\forms\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7404968;}i:33;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.850355;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7682736;}i:34;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.853719;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7695392;}i:36;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.853784;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7698872;}i:37;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.854007;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7701960;}i:39;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.854136;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7701080;}i:40;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536690.85451;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:158;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:7704696;}i:45;a:6:{i:0;s:69:"SELECT EXISTS(SELECT * FROM `admin` WHERE `admin`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761536691.898152;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8542624;}i:46;a:6:{i:0;s:69:"SELECT EXISTS(SELECT * FROM `admin` WHERE `admin`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761536691.899813;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8544304;}i:48;a:6:{i:0;s:336:"INSERT INTO `admin_login_session` (`admin_id`, `action`, `ip_address`, `country_name`, `user_agent`, `device_type`, `created_at`, `updated_at`, `is_delete`) VALUES (1, 'login', '::1', NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Browser', 1761536691, NULL, 0)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761536691.899958;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8540840;}i:49;a:6:{i:0;s:336:"INSERT INTO `admin_login_session` (`admin_id`, `action`, `ip_address`, `country_name`, `user_agent`, `device_type`, `created_at`, `updated_at`, `is_delete`) VALUES (1, 'login', '::1', NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Browser', 1761536691, NULL, 0)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761536691.903159;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"C:\xampp\htdocs\aleko-bo\models\AdminLoginSession.php";s:4:"line";i:170;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\xampp\htdocs\aleko-bo\controllers\SiteController.php";s:4:"line";i:130;s:8:"function";s:6:"record";s:5:"class";s:28:"app\models\AdminLoginSession";s:4:"type";s:2:"::";}}i:5;i:8542736;}}}";s:5:"event";s:5356:"a:30:{i:0;a:5:{s:4:"time";d:**********.748708;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.753004;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.754632;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"app\controllers\SiteController";}i:3;a:5:{s:4:"time";d:**********.760806;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:4;a:5:{s:4:"time";d:**********.761191;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.764274;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\forms\LoginForm";}i:6;a:5:{s:4:"time";d:1761536690.165857;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:7;a:5:{s:4:"time";d:1761536690.617398;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"app\models\Admin";}i:8;a:5:{s:4:"time";d:1761536690.672385;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"app\models\Admin";}i:9;a:5:{s:4:"time";d:1761536690.773017;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\forms\LoginForm";}i:10;a:5:{s:4:"time";d:1761536690.793971;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:11;a:5:{s:4:"time";d:1761536690.794828;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:12;a:5:{s:4:"time";d:1761536690.796082;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\models\AdminLoginSession";}i:13;a:5:{s:4:"time";d:1761536691.012375;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:14;a:5:{s:4:"time";d:1761536691.012385;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:15;a:5:{s:4:"time";d:1761536691.725415;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:16;a:5:{s:4:"time";d:1761536691.725424;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:17;a:5:{s:4:"time";d:1761536691.804337;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\models\AdminLoginSession";}i:18;a:5:{s:4:"time";d:1761536691.897928;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:1761536691.898041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:20;a:5:{s:4:"time";d:1761536691.899838;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\models\AdminLoginSession";}i:21;a:5:{s:4:"time";d:1761536691.89985;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\models\AdminLoginSession";}i:22;a:5:{s:4:"time";d:1761536691.91951;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\models\AdminLoginSession";}i:23;a:5:{s:4:"time";d:1761536691.921789;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:24;a:5:{s:4:"time";d:1761536691.922498;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"app\controllers\SiteController";}i:25;a:5:{s:4:"time";d:1761536691.922507;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:26;a:5:{s:4:"time";d:1761536691.922515;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:27;a:5:{s:4:"time";d:1761536691.922521;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:28;a:5:{s:4:"time";d:1761536691.92293;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:29;a:5:{s:4:"time";d:1761536691.922982;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.729259;s:3:"end";d:1761536691.924705;s:6:"memory";i:8777848;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1254:"a:3:{s:8:"messages";a:6:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750783;i:4;a:0:{}i:5;i:4741984;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750791;i:4;a:0:{}i:5;i:4742736;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750795;i:4;a:0:{}i:5;i:4743488;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750799;i:4;a:0:{}i:5;i:4744560;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750802;i:4;a:0:{}i:5;i:4745312;}i:11;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.750804;i:4;a:0:{}i:5;i:4745688;}}s:5:"route";s:10:"site/login";s:6:"action";s:45:"app\controllers\SiteController::actionLogin()";}";s:7:"request";s:8094:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:302;s:14:"requestHeaders";a:20:{s:4:"host";s:9:"localhost";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"181";s:13:"cache-control";s:9:"max-age=0";s:9:"sec-ch-ua";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:6:"origin";s:16:"http://localhost";s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:14:"sec-fetch-site";s:11:"same-origin";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-dest";s:8:"document";s:7:"referer";s:40:"http://localhost/aleko-bo/web/site/login";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:6:"cookie";s:210:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D; PHPSESSID=snr8tjs1cl0hihvr7tu6rboums";}s:15:"responseHeaders";a:10:{s:12:"X-Powered-By";s:10:"PHP/7.4.33";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:10:"Set-Cookie";a:2:{i:0;s:107:"PHPSESSID=2euaonptdfjr7tnf5og3tuq4k3; expires=Mon, 27-Oct-2025 04:14:50 GMT; Max-Age=1800; path=/; HttpOnly";i:1;s:204:"_csrf=489f280715d6012a31496fad5e5ecffd120c84d5952364dabce50560d7fdd8e3a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22hmEvoXMfDAmt4z4fS0K8vEG5a-uqgTUu%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:8:"Location";s:45:"http://localhost/aleko-bo/web/admin/user-mgmt";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68feeab1b5d7a";s:16:"X-Debug-Duration";s:5:"2,195";s:12:"X-Debug-Link";s:50:"/aleko-bo/web/debug/default/view?tag=68feeab1b5d7a";}s:5:"route";s:10:"site/login";s:6:"action";s:45:"app\controllers\SiteController::actionLogin()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:181:"_csrf=ATl1D6dztvGHlhN20j4H-mirqsbmkrQ63Pfg-HcpTBx3dxFb8RzgssnxQy-LZ2qpXJ3rqbHo6wiux5mBAGQvag%3D%3D&LoginForm%5Bemail%5D=admin%40pentajeu.com&LoginForm%5Bpassword%5D=abcd1234&Login=1";s:7:"Decoded";a:3:{s:5:"_csrf";s:88:"ATl1D6dztvGHlhN20j4H-mirqsbmkrQ63Pfg-HcpTBx3dxFb8RzgssnxQy-LZ2qpXJ3rqbHo6wiux5mBAGQvag==";s:9:"LoginForm";a:2:{s:5:"email";s:18:"<EMAIL>";s:8:"password";s:8:"abcd1234";}s:5:"Login";s:1:"1";}}s:6:"SERVER";a:61:{s:16:"REDIRECT_MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:19:"REDIRECT_MYSQL_HOME";s:16:"\xampp\mysql\bin";s:21:"REDIRECT_OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:29:"REDIRECT_PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:14:"REDIRECT_PHPRC";s:10:"\xampp\php";s:12:"REDIRECT_TMP";s:10:"\xampp\tmp";s:15:"REDIRECT_STATUS";s:3:"200";s:7:"MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:9:"localhost";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"181";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:14:"HTTP_SEC_CH_UA";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:11:"HTTP_ORIGIN";s:16:"http://localhost";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:12:"HTTP_REFERER";s:40:"http://localhost/aleko-bo/web/site/login";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:11:"HTTP_COOKIE";s:210:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D; PHPSESSID=snr8tjs1cl0hihvr7tu6rboums";s:4:"PATH";s:1305:"c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\System32;C:\Windows;C:\Windows\System32\wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files (x86)\cloudflared;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files (x;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files (x86)\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\Zed\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:95:"<address>Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33 Server at localhost Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:38:"C:/xampp/htdocs/aleko-bo/web/index.php";s:11:"REMOTE_PORT";s:5:"63723";s:12:"REDIRECT_URL";s:24:"/aleko-bo/web/site/login";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:24:"/aleko-bo/web/site/login";s:11:"SCRIPT_NAME";s:23:"/aleko-bo/web/index.php";s:8:"PHP_SELF";s:23:"/aleko-bo/web/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.71906;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:3:{s:5:"_csrf";s:88:"ATl1D6dztvGHlhN20j4H-mirqsbmkrQ63Pfg-HcpTBx3dxFb8RzgssnxQy-LZ2qpXJ3rqbHo6wiux5mBAGQvag==";s:9:"LoginForm";a:2:{s:5:"email";s:18:"<EMAIL>";s:8:"password";s:8:"abcd1234";}s:5:"Login";s:1:"1";}s:6:"COOKIE";a:2:{s:5:"_csrf";s:130:"5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a:2:{i:0;s:5:"_csrf";i:1;s:32:"vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv";}";s:9:"PHPSESSID";s:26:"snr8tjs1cl0hihvr7tu6rboums";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1761623090;}}";s:4:"user";s:4712:"a:5:{s:2:"id";i:1;s:8:"identity";a:13:{s:2:"id";s:1:"1";s:8:"username";s:4:"null";s:5:"email";s:20:"'<EMAIL>'";s:8:"otp_code";s:4:"null";s:10:"valid_time";s:4:"null";s:8:"password";s:62:"'$2y$10$KfLBUUuoZTsmjToSmlTTrOL8waVh2cXdQShabB1ntEIqycI2ilfyS'";s:12:"password_key";s:15:"'68PuKdN0TdPVU'";s:12:"access_token";s:4:"null";s:4:"role";s:7:"'admin'";s:10:"is_suspend";s:1:"0";s:10:"created_at";s:10:"1756355697";s:10:"updated_at";s:10:"1756355697";s:9:"is_delete";s:1:"0";}s:10:"attributes";a:13:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:3;a:2:{s:9:"attribute";s:8:"otp_code";s:5:"label";s:8:"Otp Code";}i:4;a:2:{s:9:"attribute";s:10:"valid_time";s:5:"label";s:10:"Valid Time";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:8:"Password";}i:6;a:2:{s:9:"attribute";s:12:"password_key";s:5:"label";s:12:"Password Key";}i:7;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:8;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:9;a:2:{s:9:"attribute";s:10:"is_suspend";s:5:"label";s:10:"Is Suspend";}i:10;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:11;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:12;a:2:{s:9:"attribute";s:9:"is_delete";s:5:"label";s:9:"Is Delete";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:3:"key";N;s:9:"allModels";a:10:{s:17:"bundle-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:17:"bundle-management";s:11:"description";s:13:"Manage bundle";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:17:"notice-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:17:"notice-management";s:11:"description";s:13:"Manage notice";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:19:"referral-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:19:"referral-management";s:11:"description";s:15:"Manage referral";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:6:"report";a:7:{s:4:"type";s:1:"2";s:4:"name";s:6:"report";s:11:"description";s:11:"View report";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:22:"social-task-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:22:"social-task-management";s:11:"description";s:18:"Manage social task";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:16:"stage-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:16:"stage-management";s:11:"description";s:12:"Manage stage";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"1760611918";s:9:"updatedAt";s:10:"1760611918";}s:9:"sub-admin";a:7:{s:4:"type";s:1:"2";s:4:"name";s:9:"sub-admin";s:11:"description";s:15:"Manage subadmin";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:14:"system-setting";a:7:{s:4:"type";s:1:"2";s:4:"name";s:14:"system-setting";s:11:"description";s:21:"Manage system setting";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:20:"tier-list-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:20:"tier-list-management";s:11:"description";s:16:"Manage tier list";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}s:15:"user-management";a:7:{s:4:"type";s:1:"2";s:4:"name";s:15:"user-management";s:11:"description";s:11:"Manage user";s:8:"ruleName";N;s:4:"data";s:5:"admin";s:9:"createdAt";s:10:"**********";s:9:"updatedAt";s:10:"**********";}}s:10:"modelClass";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68feeab1b5d7a";s:3:"url";s:40:"http://localhost/aleko-bo/web/site/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.71906;s:10:"statusCode";i:302;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8777848;s:14:"processingTime";d:2.1952829360961914;}s:10:"exceptions";a:0:{}}