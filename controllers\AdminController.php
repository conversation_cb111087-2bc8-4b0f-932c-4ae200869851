<?php

namespace app\controllers;

use Yii;
use yii\filters\AccessControl;
use yii\web\UploadedFile;
use yii\web\Response;
use app\models\AuditLog;
use app\models\Admin;
use app\models\PermissionQuery;
use app\models\UserReferral;
use app\models\Wallet;
use app\models\User;
use app\models\Rank;
use app\forms\ExportJobForm;
use app\forms\ResetPasswordForm;
use app\forms\SubAdminMgmtForm;
use app\forms\UserMgmtForm;
use app\forms\CreditMgmtForm;
use app\forms\ReferralMgmtForm;
use app\forms\SettingsForm;
use app\forms\TranslationMgmtForm;
use app\forms\UserNetworkForm;
use app\forms\SocialTaskMgmtForm;
use app\forms\AlertEmailMgmtForm;
use app\forms\ReferralRewardMgmtForm;
use app\forms\NoticeMgmtForm;
use app\components\Sheet;
use app\components\CheckSumHelper;
use app\forms\StageMgmtForm;
use app\forms\BundleMgmtForm;

class AdminController extends Controller
{
  public function behaviors()
  {
    return [
      'access' => [
        'class' => AccessControl::class,
        'rules' => [
          [
            'allow'   => true,
            'roles'   => ['@'],
            'actions' => ['change-password'],
          ],
          [
            'allow'   => true,
            'roles'   => ['system-setting'],
            'actions' => ['manage-setting'],
          ],
          [
            'allow'   => true,
            'roles'   => ['sub-admin'],
            'actions' => [
              'sub-admin-mgmt',
              'create-sub-admin',
              'manage-permission',
              'suspend-sub-admin',
              'sub-admin-profile',
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['user-management'],
            'actions' => [
              'user-mgmt',
              'user-credit',
              //'user-network',
              //'create-nodeholder',
              //'user-adjust-rank',
              //'user-adjust-nodeholder',         
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['referral-management'],
            'actions' => [
              'referral-mgmt',
              'view-referral',
              'user-network'
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['social-task-management'],
            'actions' => [
              'social-task-mgmt',
              'create-social-task',
              'update-social-task',
              'delete-social-task',
              'refresh-social-task',
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['notice-management'],
            'actions' => [
              'notice-mgmt',
              'create-notice',
              'update-notice',
              'delete-notice',
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['tier-list-management'],
            'actions' => [
              'tier-list-mgmt',
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['translation-management'],
            'actions' => [
              'manage-translation',
              'update-translation'
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['alert-email-management'],
            'actions' => [
              'alert-email-mgmt',
              'create-alert-email',
              'update-alert-email',
              'delete-alert-email'
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['stage-management'],
            'actions' => [
              'stage-mgmt',
              'update-stage'
            ],
          ],
          [
            'allow'   => true,
            'roles'   => ['bundle-management'],
            'actions' => [
              'bundle-mgmt',
              'manage-bundle-price',
              'create-bundle-price',
              'update-bundle-price',
              'delete-bundle-price',
            ],
          ],
        ],
      ],
    ];
  }

  /**
   * Declares external actions for the controller.
   *
   * @return array the external action configurations. Normal properties
   *              specified in [[\yii\base\Controller]] will be overwritten.
   */
  public function actions()
  {
    return [
      'error' => [
        'class' => 'yii\web\ErrorAction',
      ],
      'captcha' => [
        'class' => 'yii\captcha\CaptchaAction',
        'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
      ],
    ];
  }

  /**
   * Update current user's password
   * 
   * get user info > declare model > set scenario as update-password 
   * > load data > validate > update password > set flash > commit to db
   *
   * @return string|array
   */
  public function actionChangePassword()
  {
    $user  = Yii::$app->user->identity;
    $model = new ResetPasswordForm;
    $model->scenario = 'update-password';

    $db    = Yii::$app->db->beginTransaction();

    try {
      if (!empty($model->load(Yii::$app->request->post()))) {
        if (!$model->validate()) {
          throw new \Exception(current($model->getFirstErrors()));
        }

        $model->updateBoPassword($user);

        AuditLog::create([
          'function'  => AuditLog::FUNC_CHANGE_PASSWORD,
          'value'     => "Update Password for " . $user->email,
          'remark'    => "Update Password For " . $user->email,
          'action_by' => $user->id,
        ]);

        Yii::$app->session->setFlash('success', Yii::t('app', 'You have updated new password successfully.'));
        $db->commit();

        return $this->redirect(['admin/change-password']);
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['admin/change-password']);
    }

    return $this->render('change_password', compact('model'));
  }

  /**
   * Manage Setting
   * 
   * get user info > declare model > Update in post load data > save > commit to db
   * > set flash > refresh 
   *
   * @return string|array
   */
  public function actionManageSetting()
  {
    $model = new SettingsForm();
    $user  = Yii::$app->user->identity;

    $request = Yii::$app->request;

    if (!empty($request->post())) {
      $db = Yii::$app->db->beginTransaction();
      try {
        if ($request->post('Update', false)) {
          $model->load($request->post());

          if (!$model->save($user)) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          $db->commit();
          Yii::$app->session->setFlash('success', $model->getSuccessMessage());
        }

        return $this->refresh();
      } catch (\Exception $e) {
        $db->rollback();
        Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      }
    }

    return $this->render('manageSetting', compact('model'));
  }

  /**
   * Manage Sub Admin
   * 
   * declare model > get admin id > load data
   *
   * @return string|array
   */
  public function actionSubAdminMgmt()
  {
    $model           = new SubAdminMgmtForm;
    $model->admin_id = Yii::$app->user->identity->id;
    $model->load(Yii::$app->request->get());

    return $this->render('subAdminMgmt', compact('model'));
  }

  /**
   * Create Sub Admin
   * 
   * declare model > set scenario as create_user > load data > validate 
   * > create sub admin > set flash > commit to db
   *
   * @return string|array
   */
  public function actionCreateSubAdmin()
  {
    $model           = new SubAdminMgmtForm;
    $model->scenario = 'create_user';

    $db = Yii::$app->db->beginTransaction();

    try {
      if ($model->load(Yii::$app->request->post())) {

        if (!$model->validate()) {
          throw new \Exception(current($model->getFirstErrors()));
        }
        $user = $model->createSubAdmin();

        Yii::$app->session->setFlash('success', Yii::t('app', 'You had registered {user} successfully', ['user' => "<b>" . $user->email . "</b>"]));
        $db->commit();
        return $this->redirect(['manage-permission', 'id' => $user->id]);
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', $e->getMessage());
      return $this->redirect(['create-sub-admin']);
    }

    return $this->render('createSubAdmin', compact('model'));
  }

  /**
   * Manage Permission of the sub admin
   * 
   * declare model > find admin > load permission > get sub admin permission > load data
   * > update access control > set flash > commit to db > refresh
   *
   * @param int $id Sub Admin ID
   *
   * @return string|array
   */
  public function actionManagePermission($id)
  {
    try {
      $db = Yii::$app->db->beginTransaction();

      $model = new SubAdminMgmtForm;

      $user = Admin::findOne(['id' => $id, 'is_delete' => 0]);

      if (empty($user)) {
        throw new \Exception('User not found.', 1);
      }

      $model->loadPermissions($id);
      $perm = PermissionQuery::getSubAdminPermission();

      if (!empty(Yii::$app->request->post())) {
        $model->load(Yii::$app->request->post());
        $model->updateAccessControl($user->id);

        $db->commit();
        Yii::$app->session->setFlash('success', Yii::t('app', 'Update permission successfully'));

        return $this->refresh();
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', $e->getMessage());
    }

    return $this->render('managePermission', compact('model', 'user', 'perm'));
  }

  /**
   * Suspend/Unsuspend sub admin
   * 
   * declare model > suspend account > set flash
   *
   * @param int $id Sub Admin ID
   *
   * @return string|array
   */
  public function actionSuspendSubAdmin($id)
  {
    if (empty($id)) {
      return $this->redirect(['sub-admin-mgmt']);
    }

    $form = new SubAdminMgmtForm;
    $form->suspendAccount($id);

    $user = Yii::$app->user->identity;
    AuditLog::create([
      'function'  => AuditLog::SUSPEND_ADMIN,
      'value'     => "Suspend/Unsuspend subadmin for ID " . $id,
      'remark'    => "Suspend/Unsuspend subadmin for ID " . $id,
      'action_by' => $user->id,
    ]);
    Yii::$app->session->setFlash('success', Yii::t('app', 'Subadmin has been suspend successfully.'));
    return $this->redirect(['sub-admin-mgmt']);
  }

  /**
   * Update Sub Admin Profile
   * 
   * get user info > declare model > set scenario as update > get profile
   * > load data > validate > update profile > set flash > commit to db
   *
   * @param int $id Sub Admin ID
   *
   * @return string|array
   */
  public function actionSubAdminProfile($id)
  {
    $admin = Yii::$app->user->identity;

    $model           = new SubAdminMgmtForm;
    $model->scenario = "update";
    $model->getProfile($id);

    $request = Yii::$app->request;
    $db      = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())) {
        if ($request->post('Update', false)) {
          $model->load($request->post());
          $model->updateProfile($id);
          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Profile update successfully.'));

          return $this->redirect(['sub-admin-profile', 'id' => $id]);
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['sub-admin-profile', 'id' => $id]);
    }

    return $this->render('subAdminProfile', compact('model'));
  }

  /**
   * User Management
   * 
   * declare model > load > validate > get user summary > export
   *
   * @return string|array
   */
  public function actionUserMgmt()
  {
    $model = new UserMgmtForm;
    $data = $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    $summary = $model->getUserSummaryInfo();

    if (!empty(Yii::$app->request->get('export', 0))) {
      $uuid = Yii::$app->request->get('uuid', null);
      if (empty($uuid)) {
        return;
      }

      $export           = new ExportJobForm;
      $export->user_id  = Yii::$app->user->identity->id;
      $export->ref_no   = $uuid;
      $export->data     = Yii::$app->request->get();
      $export->function = "user-management";
      $export->create();

      return $this->redirect(['report/export-list', 'ref_no' => $uuid]);
    }

    return $this->render('userMgmt', compact('model', 'summary'));
  }

  /**
   * Referral Management
   * 
   * declare model > load > validate > render referralMgmt
   *
   * @return string|array
   */
  public function actionReferralMgmt()
  {
    $model = new ReferralMgmtForm;
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('referralMgmt', compact('model'));
  }

  /**
   * View Referral Tree
   * 
   * declare model > load > validate > get referral tree > render referralTree
   * 
   * @param int $user_id user id
   * @return string|array
   */
  public function actionViewReferral($user_id)
  {
    $model = new ReferralMgmtForm;
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    $data = UserReferral::findOne(['id' => $user_id, 'is_delete' => 0]);

    $json = $model->getReferralTree($user_id);

    return $this->render('referralTree', compact('model', 'data', 'json'));
  }

/**
 * Social Task Management
 * 
 * declare model > load data > render socialTaskMgmt
 *
 * @return string|array
 */
  public function actionSocialTaskMgmt()
  {
    $model = new SocialTaskMgmtForm;
    $model->load(Yii::$app->request->get());

    return $this->render('socialTaskMgmt', compact('model'));
  }

  /**
   * Create Social Task
   * 
   * declare model > set scenario & type as create > load data > validate 
   * > load icon > create social task > commit to db > set flash > redirect
   * 
   * @return string|array
   */
  public function actionCreateSocialTask()
  {
    $model = new SocialTaskMgmtForm;
    $model->scenario = 'create';

    $type    = 'create';
    $request = Yii::$app->request;
    $db      = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())) {
        if ($request->post('Create', false)) {
          $icon_file = UploadedFile::getInstance($model, 'icon_url');

          $model->load($request->post());

          if (!empty($icon_file)) {
            $model->icon_url = $icon_file;
          }

          if (!$model->validate()) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          $model->createOrUpdateSocialTask();
          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Create task successfully'));
          return $this->redirect(['social-task-mgmt']);
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['create-social-task']);
    }

    return $this->render('createUpdateSocialTask', compact('model', 'type'));
  }

  /**
   * Update Social Task
   * 
   * declare model > set scenario & type as update > load data > validate 
   * > load icon > update social task > commit to db > set flash > redirect
   * 
   * @param int $id
   * 
   * @return string|array
   */
  public function actionUpdateSocialTask($id)
  {
    $model = new SocialTaskMgmtForm;
    $model->scenario = 'update';
    $model->getSocialTask($id);

    $type    = 'update';
    $request = Yii::$app->request;
    $db      = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())) {
        if ($request->post('Update', false)) {
          $icon_file = UploadedFile::getInstance($model, 'icon_url');

          $model->load($request->post());

          if (!empty($icon_file)) {
            $model->icon_url = $icon_file;
          }

          if (!$model->validate()) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          $model->createOrUpdateSocialTask($id);
          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Update task successfully'));
          return $this->redirect(['update-social-task', 'id' => $id]);
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['update-social-task', 'id' => $id]);
    }

    return $this->render('createUpdateSocialTask', compact('model', 'type'));
  }

  /**
   * Delete Social Task
   * 
   * declare model > delete social task > set flash > redirect
   *
   * @param int $id
   *
   * @return string|array
   */
  public function actionDeleteSocialTask($id)
  {
    $model = new SocialTaskMgmtForm;
    $model->deleteSocialTask($id);

    Yii::$app->session->setFlash('success', Yii::t('app', 'Delete task successfully'));
    return $this->redirect(['social-task-mgmt']);
  }

  /**
   * Refresh Social Task Cache
   * 
   * declare model > refresh social task cache > set flash > redirect
   * 
   * @return string|array
   */
  public function actionRefreshSocialTask()
  {
    $model = new SocialTaskMgmtForm;
    $result = $model->clearSocialCache();

    if ($result != true) {
      Yii::$app->session->setFlash('error', Yii::t('app', 'Refresh cache failed'));
      return $this->redirect(['social-task-mgmt']);
    }

    Yii::$app->session->setFlash('success', Yii::t('app', 'Refresh cache successfully'));
    return $this->redirect(['social-task-mgmt']);
  }

  /**
   * Notice Management
   * 
   * declare model > load data > validate > render noticeMgmt
   * 
   * @return string|array
   */
  public function actionNoticeMgmt() {
    $model = new NoticeMgmtForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('noticeMgmt', compact('model'));
  }

  /**
   * Create Notice
   * 
   * declare model > set scenario as create > load data > validate 
   * > create notice > commit to db > set flash > redirect
   * 
   * @return string|array
   */
  public function actionCreateNotice() {
    $model = new NoticeMgmtForm();
    $model->scenario = 'create';

    $request = Yii::$app->request;
    $db      = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())) {
        if ($request->post('Create', false)) {
          $model->load($request->post());

          if (!$model->validate()) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          // currently invalid as notice have default icon in dapp site
          $icon_file = UploadedFile::getInstance($model, 'icon_url');
          if (!empty($icon_file)) {
            $model->icon_url = $icon_file;
          }

          $model->createOrUpdateNotice();

          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Create notice successfully'));
          return $this->redirect(['notice-mgmt']);
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['create-notice']);
    }

    return $this->render('createUpdateNotice', compact('model'));
  }

  /**
   * Update notice
   * 
   * declare model > set scenario as update > load notice data > validate 
   * > update notice > commit to db > set flash > redirect
   *
   * @param int $id
   * 
   * @return string|array
   */
  public function actionUpdateNotice($id) {
    $model = new NoticeMgmtForm();
    $model->id = $id;
    $model->scenario = 'Update';
    $model->getNotice($id);

    $request = Yii::$app->request;
    $db = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())) {
        if ($request->post('Update', false)) {
          $model->load($request->post());

          if (!$model->validate()) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          // currently invalid as notice have default icon in dapp site
          $icon_file = UploadedFile::getInstance($model, 'icon_url');
          if (!empty($icon_file)) {
            $model->icon_url = $icon_file;
          }

          $model->createOrUpdateNotice($id);

          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Update notice successfully'));
          return $this->redirect(['notice-mgmt']);
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['update-notice', 'id' => $id]);
    }

    return $this->render('createUpdateNotice', compact('model', 'id'));
  }

  /**
   * Delete Notice
   * 
   * declare model > delete notice > set flash > redirect
   * 
   * @param int $id
   * 
   * @return string|array
   */
  public function actionDeleteNotice($id) {
    $model = new NoticeMgmtForm();
    $model->deleteNotice($id);

    Yii::$app->session->setFlash('success', Yii::t('app', 'Delete notice successfully'));
    return $this->redirect(['notice-mgmt']);
  }

/**
 * Tier List Management
 * 
 * declare model > load > validate > render tierListMgmt
 * 
 * @return string|array
 */
  public function actionTierListMgmt()
  {
    $model = new ReferralRewardMgmtForm();

    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('tierListMgmt', compact('model'));
  }

  public function actionStageMgmt()
  {
    $model = new StageMgmtForm();

    $model->load(Yii::$app->request->get());

    // init stage
    $model->initStage();

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('stageMgmt', compact('model'));
  }

  public function actionUpdateStage($id)
  {
    $model = new StageMgmtForm();
    $model->scenario = 'update';
    $model->getStageInfo($id);

    $request = Yii::$app->request;
    $db      = Yii::$app->db->beginTransaction();

    try {
      if (!empty($request->post())){
        if ($request->post('Update', false)) {
          $model->load($request->post());

          if (!$model->validate()) {
            throw new \Exception(current($model->getFirstErrors()));
          }

          $model->updateStage($id);

          $db->commit();

          Yii::$app->session->setFlash('success', Yii::t('app', 'Update stage successfully'));
          return $this->refresh();
        }
      }
    } catch (\Exception $e) {
      $db->rollback();
      Yii::$app->session->setFlash('error', Yii::t('app', $e->getMessage()));
      return $this->redirect(['update-stage', 'id' => $id]);
    }

    return $this->render('updateStage', compact('model'));
  }

  public function actionBundleMgmt()
  {
    $model = new BundleMgmtForm();

    $model->load(Yii::$app->request->get());

    // init bundle
    $model->initBundle();

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('bundleMgmt', compact('model'));
  }
}
