export default function (sequelize, DataTypes) {
    return sequelize.define('social_task_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        task_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        task_label_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false
        },
        redirect_url: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        icon_image_url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        reward_point: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'social_task_lookup',
        timestamps: false,
    })
}