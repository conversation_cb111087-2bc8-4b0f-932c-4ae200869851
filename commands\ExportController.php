<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\helpers\FileHelper;
use app\components\Util;
use app\forms\ExportJobForm;
use app\models\ExportJob;

class ExportController extends Controller
{
    /**
     * Process export job in queue
     *
     * This command run export job in queue. If there is an export job in process, it will exit.
     * It will update status of export job to "done" or "fail" depend on result of export job.
     *
     * @throws \Exception if there is an error when run export job
     */
  public function actionRun()
  {
    $process_job = ExportJob::find()
      ->andWhere(['status'    => 'processing'])
      ->andWhere(['is_delete' => 0])
      ->count();

    if ($process_job >= 1) {
      echo "\nAn export job is proccessing. Exit\n";
      return true;
    }

    $job = ExportJob::find()
      ->andWhere(['status'    => 'queue'])
      ->andWhere(['is_delete' => 0])
      ->orderBy(['id' => SORT_ASC])
      ->one();

    if (empty($job)) {
      echo "\nNo export job in queue.\n";
      return true;
    }

    $db = Yii::$app->db->beginTransaction();

    try {
      echo "\nProcess export job. ID: {$job->id}\n";
      $export = new ExportJobForm;
      $export->function = $job->function;
      $export->data     = $job->data;
      $export->user_id  = $job->action_by;
      $export->job_id   = $job->id;
      $export->run();
      echo "> Done Process export job.\n";
      $db->commit();
    } catch (\Exception $e) {
      $db->rollBack();
      throw $e;
    }
  }
}
