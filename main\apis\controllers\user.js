import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

router.get("/generate-token", async (req, res) => {
    const { user_id } = req.query
    console.log(user_id)
    const user = await query.models.user.findOne({
        where: {
            id: user_id,
            is_delete: false
        },
        raw: true
    })
    const signed_jwt = helper.auth_helper.signJWT(user, null, null, "LOGIN")
    return res.status(200).json({
        "data": { "jwt_token": signed_jwt },
        "status": 200,
        "msg": "Account login successfully",
        "error": false
    });
})

router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const get_user_data = await query.models.user.findOne({
            include: [
                {
                    model: query.models.user_referral,
                    required: false,
                },
                {
                    model: query.models.credit,
                    required: false,
                },
            ],
            where: {
                id: user_id,
                is_delete: false
            },
            // raw: true,
            nest: true,
            transaction: transaction,
        })

        common.util_helper.jsonLongLogging(get_user_data)
        const data = {
            user_telegram_id: get_user_data.user_telegram_id,
            username: get_user_data.username,
            first_last_name: get_user_data.first_last_name,
            profile_pic: get_user_data.profile_pic,
            account_type: get_user_data.account_type,
            current_referral_ranking: get_user_data.current_referral_ranking,
            user_x_id: get_user_data.user_x_id,
            user_instagram_id: get_user_data.user_instagram_id,
            wallet_address_bind: get_user_data.wallet_address,
            is_verified: get_user_data.is_verified,
            credits: get_user_data.credits.map(element => {
                return {
                    credit_id: element.id,
                    type: element.type,
                    balance: element.balance,
                }
            })
        };

        const current_time = common.util_helper.getCurrentEpochTime();
        const moment_from_unix = common.moment.unix(current_time);
        const time_24_hour = moment_from_unix.format('HH:mm:ss');
        const date_day_month_year = moment_from_unix.format('DD-MM-YYYY');
        let find_login_session = await query.models.user_login_session.findOne({ where: { user_id: user_id, day_month_year: date_day_month_year.toString(), is_delete: 0 }, transaction: transaction })
        if (!find_login_session) {
            await query.models.user_login_session.create({
                user_id: user_id,
                time: time_24_hour.toString(),
                day_month_year: date_day_month_year.toString(),
                created_at: current_time,
                updated_at: current_time,
                is_delete: 0,
            }, {
                transaction: transaction,
            })
        }

        //If user_song_selected_lookup is not available, then assign the basic song for user
        let find_user_song_selected_lookup = await query.models.user_song_selected_lookup.findOne({
            where: {
                user_id: user_id,
                is_active: true,
                is_delete: 0
            },
            order: [
                ['created_at', 'DESC'],
            ],
            transaction: transaction,
        })
        if (!find_user_song_selected_lookup) {
            //find basic genre & basic song
            let find_basic_genre = await query.models.advance_genre_lookup.findOne({
                where: {
                    genre_type: "basic",
                    is_active: true,
                    is_delete: 0
                },
                transaction: transaction,
            })
            if (!find_basic_genre) {
                throw new Error("Basic genre not found")
            }
            let find_basic_song = await query.models.song_lookup.findOne({
                where: {
                    song_type: "basic",
                    is_active: true,
                    is_delete: 0
                },
                transaction: transaction,
            })
            if (!find_basic_song) {
                throw new Error("Basic song not found")
            }

            await query.models.user_song_selected_lookup.create({
                user_id: user_id,
                song_id: find_basic_song.id,
                genre_id: find_basic_genre.id,
                is_active: true,
                is_delete: 0,
                created_at: common.util_helper.getCurrentEpochTime(),
            }, {
                transaction: transaction,
            })
        }

        await transaction.commit();
        return res.status(200).json({
            "data": data,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id} --> wallet_id: ${wallet_id} --> wallet_address: ${wallet_address}`, error),
            error: true
        });
    }
})

router.post("/refresh-profile-pic", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        await query.models.user.update({
            profile_pic: null,
            updated_at: common.util_helper.getCurrentEpochTime()
        }, {
            where: {
                id: user_id
            },
            transaction: transaction
        })

        await transaction.commit();
        return res.status(200).json({
            "data": {},
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

router.post("/refresh-username", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        await query.models.user.update({
            is_username_checked: 0,
            updated_at: common.util_helper.getCurrentEpochTime()
        }, {
            where: {
                id: user_id
            },
            transaction: transaction
        })

        await transaction.commit();
        return res.status(200).json({
            "data": {},
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

router.post("/update-x-id", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, validator.exp_validator(validator.items_validator.authValidator.x_instagram_validator), async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const { account_id } = req.body;
    try {
        await query.models.user.update({
            user_x_id: account_id,
            updated_at: common.util_helper.getCurrentEpochTime()
        }, {
            where: {
                id: user_id
            },
            transaction: transaction
        })
        await transaction.commit();
        return res.status(200).json({
            "data": {},
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

router.post("/update-instagram-id", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, validator.exp_validator(validator.items_validator.authValidator.x_instagram_validator), async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const { account_id } = req.body;
    try {
        await query.models.user.update({
            user_instagram_id: account_id,
            updated_at: common.util_helper.getCurrentEpochTime()
        }, {
            where: {
                id: user_id
            },
            transaction: transaction
        })
        await transaction.commit();
        return res.status(200).json({
            "data": {},
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

router.post("/bind-wallet-address", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, validator.exp_validator(validator.items_validator.authValidator.wallet_validator), async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    const { wallet_address } = req.body;

    try {
        const user = await query.models.user.findOne({
            where: {
                id: user_id
            }
        })
        if (user.wallet_address) {
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Wallet address already bind.",
                "error": true
            });
        }

        await query.models.user.update({
            wallet_address: wallet_address,
            updated_at: common.util_helper.getCurrentEpochTime()
        }, {
            where: {
                id: user_id
            },
            transaction: transaction
        })
        await transaction.commit();
        return res.status(200).json({
            "data": {},
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

export default router;