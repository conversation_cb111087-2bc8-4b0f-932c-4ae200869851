<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_referral".
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $hash_id
 * @property string|null $group_tag
 * @property int|null $referral_user_id
 * @property string $referral_code
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 */
class UserReferral extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_referral';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['hash_id', 'group_tag', 'referral_user_id', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'referral_code', 'created_at'], 'required'],
            [['user_id', 'referral_user_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['hash_id', 'group_tag', 'referral_code'], 'string', 'max' => 255],
            [['referral_code'], 'unique'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'hash_id' => 'Hash ID',
            'group_tag' => 'Group Tag',
            'referral_user_id' => 'Referral User ID',
            'referral_code' => 'Referral Code',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[ReferralUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getReferralUser()
    {
        return $this->hasOne(User::class, ['id' => 'referral_user_id']);
    }

    /**
     * Gets referral info, including total referral and today referral.
     *
     * @return array
     */
    public function getReferralInfo()
    {
        $query = self::find();
        $query->andWhere(['referral_user_id' => $this->id]);
        $query->andWhere(['is_delete' => 0]);

        $totalReferral = $query->count();

        // get today referral
        $query->andWhere(['>=', 'created_at', strtotime(date('Y-m-d 00:00:00'))]);
        $todayReferral = $query->count();

        return [
            'total' => $totalReferral,
            'today' => $todayReferral
        ];
    }

}
