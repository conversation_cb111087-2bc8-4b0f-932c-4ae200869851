<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "advance_genre_lookup".
 *
 * @property int $id
 * @property string $genre_name
 * @property int|null $boost_instrument_one_id
 * @property int|null $boost_instrument_two_id
 * @property int|null $boost_instrument_three_id
 * @property int|null $boost_instrument_four_id
 * @property int $is_active
 * @property int $is_instrument_boost_allow
 * @property string $genre_type
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property InstrumentLookup $boostInstrumentOne
 * @property InstrumentLookup $boostInstrumentTwo
 * @property InstrumentLookup $boostInstrumentThree
 * @property InstrumentLookup $boostInstrumentFour
 * @property UserSongSelectedLookup[] $userSongSelectedLookups
 */
class AdvanceGenreLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'advance_genre_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['genre_name', 'genre_type', 'created_at'], 'required'],
            [['boost_instrument_one_id', 'boost_instrument_two_id', 'boost_instrument_three_id', 'boost_instrument_four_id', 'is_active', 'is_instrument_boost_allow', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['genre_name', 'genre_type'], 'string', 'max' => 255],
            [['boost_instrument_one_id'], 'exist', 'skipOnError' => true, 'targetClass' => InstrumentLookup::className(), 'targetAttribute' => ['boost_instrument_one_id' => 'id']],
            [['boost_instrument_two_id'], 'exist', 'skipOnError' => true, 'targetClass' => InstrumentLookup::className(), 'targetAttribute' => ['boost_instrument_two_id' => 'id']],
            [['boost_instrument_three_id'], 'exist', 'skipOnError' => true, 'targetClass' => InstrumentLookup::className(), 'targetAttribute' => ['boost_instrument_three_id' => 'id']],
            [['boost_instrument_four_id'], 'exist', 'skipOnError' => true, 'targetClass' => InstrumentLookup::className(), 'targetAttribute' => ['boost_instrument_four_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'genre_name' => 'Genre Name',
            'boost_instrument_one_id' => 'Boost Instrument One ID',
            'boost_instrument_two_id' => 'Boost Instrument Two ID',
            'boost_instrument_three_id' => 'Boost Instrument Three ID',
            'boost_instrument_four_id' => 'Boost Instrument Four ID',
            'is_active' => 'Is Active',
            'is_instrument_boost_allow' => 'Is Instrument Boost Allow',
            'genre_type' => 'Genre Type',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[BoostInstrumentOne]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentOne()
    {
        return $this->hasOne(InstrumentLookup::className(), ['id' => 'boost_instrument_one_id']);
    }

    /**
     * Gets query for [[BoostInstrumentTwo]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentTwo()
    {
        return $this->hasOne(InstrumentLookup::className(), ['id' => 'boost_instrument_two_id']);
    }

    /**
     * Gets query for [[BoostInstrumentThree]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentThree()
    {
        return $this->hasOne(InstrumentLookup::className(), ['id' => 'boost_instrument_three_id']);
    }

    /**
     * Gets query for [[BoostInstrumentFour]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentFour()
    {
        return $this->hasOne(InstrumentLookup::className(), ['id' => 'boost_instrument_four_id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['genre_id' => 'id']);
    }

    /**
     * Gets all genre names as an associative array.
     *
     * Select id, genre_name from advance_genre_lookup
     * where is_active = 1
     * and is_delete = 0
     * order by id ASC
     *
     * @return array
     */
    public static function getAllGenreName() {
        $query = self::find()
            ->select([
                'id',
                'genre_name'
            ])
            ->where([
                'is_active' => 1,
                'is_delete' => 0
                ])
            ->orderBy('id ASC')
            ->all();
            
        return ArrayHelper::map($query, 'genre_name', 'genre_name');
    }

    /**
     * Get all genre types with their corresponding ids.
     * 
     * select id, genre_type from advance_genre_lookup
     * where is_active = 1
     * and is_delete = 0
     * group by genre_type
     * order by id ASC
     * 
     * @return array
     */
    public static function getAllGenreType() {
        $query = self::find()
            ->select([
                'id',
                'genre_type'
            ])
            ->where([
                'is_active' => 1,
                'is_delete' => 0
                ])
            ->groupBy('genre_type')
            ->orderBy('id ASC')
            ->all();
        
        // capitalized first letter of every word
        foreach ($query as $item) {
            $item->genre_type = ucfirst($item->genre_type);
        }
            
        return ArrayHelper::map($query, 'genre_type', 'genre_type');
    }
}
