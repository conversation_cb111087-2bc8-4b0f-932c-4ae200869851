<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use yii\data\ActiveDataProvider;
use app\models\Stage;
use app\models\StagePurchase;

class StagePurchaseReportForm extends Model
{
  public $wallet_address;
  public $stage_id;
  public $tx_id;
  public $date_start;
  public $date_end;
  public $date_range;

  public function rules()
  {
    return [
      [['wallet_address', 'stage_id', 'tx_id', 'date_start', 'date_end', 'date_range'], 'safe'],
      [['wallet_address', 'tx_id', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  public function getStageList()
  {
    $query = Stage::find();
    $query->andWhere(['is_delete' => 0]);
    $query->orderBy('id ASC');
    $result = $query->all();

    return ArrayHelper::map($result, 'id', function ($model) {
      return $model->name . ' (' . ($model->status == 'live' ? 'Live' : $model->status == 'queuing' ? 'Queuing' : 'Ended') . ')';
    });
  }

  public function getQuery()
  {
    $query = StagePurchase::find();
    $query->alias('sp');
    $query->leftJoin('user u', 'u.id = sp.user_id');

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }

    if (!empty($this->stage_id)) {
      $query->andFilterWhere(['sp.stage_id' => $this->stage_id]);
    }

    if (!empty($this->tx_id)) {
      $query->andFilterWhere(['like','sp.tx_id',"%".$this->tx_id."%", false]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'sp.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['sp.is_delete' => 0]);
    $query->andWhere(['u.is_delete' => 0]);

    return $query;
  }

  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

  public function getSummary()
  {
    // get purchase by stage
    $purchase_by_stage = StagePurchase::find()
      ->select([
        'stage_id',
        'total_usd' => 'SUM(usd_amount)',
        'total_token' => 'SUM(token_amount)',
      ])
      ->groupBy('stage_id')
      ->indexBy('stage_id')
      ->asArray()
      ->all();

    $stage_list = Stage::find()
      ->andWhere(['is_delete' => 0])
      ->indexBy('id')
      ->asArray()
      ->all();

    $summary = [];

    foreach ($stage_list as $stage_id => $stage) {
      $summary[] = [
        'stage_name'      => $stage['name'],
        'stage_status'    => $stage['status'],
        'total_token' => isset($purchase_by_stage[$stage_id]) ? $purchase_by_stage[$stage_id]['total_token'] : 0,
        'token_available' => $stage['token_available'],
        'total_usd'   => isset($purchase_by_stage[$stage_id]) ? $purchase_by_stage[$stage_id]['total_usd'] : 0,
        'total_fund'    => $stage['total_fund'],
      ];
    }

    return $summary;
  }
}
