<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\UserLoginSession;
use app\models\SystemSetting;

class UserLoginLogForm extends Model
{
  public $first_last_name;
  public $username;
  public $address;
  public $date_range;
  public $date_start;
  public $date_end;

  public function rules()
  {
    return [
      [['first_last_name', 'username', 'address', 'date_range', 'date_start', 'date_end'], 'safe'],
      [['first_last_name', 'username', 'address', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Get ActiveQuery for user login log
   * 
   * Select * from user_login_session as uls
   * left join user u on u.id = uls.user_id
   * where u.username like %:username% or u.first_last_name like %:first_last_name%
   * and uls.created_at between :date_start and :date_end
   * and uls.id_delete = 0 and u.is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = UserLoginSession::find();
    $query->alias('uls');
    //$query->leftJoin('wallet w', 'w.id = uls.wallet_id');
    $query->leftJoin('user u', 'u.id = uls.user_id');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    // if (!empty($this->address)) {
    //   $query->andFilterWhere(['like','w.wallet_address',"%".$this->address."%", false]);
    // }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'uls.created_at', $this->date_start, $this->date_end]);
    }

    //$query->andWhere(['w.is_delete'   => 0]);
    $query->andWhere(['uls.is_delete' => 0]);
    $query->andWhere(['u.is_delete'   => 0]);

    // throw new \Exception($query->createCommand()->rawSql);

    return $query;
  }

  /**
   * Gets the data provider for the user login log based on the current form data.
   *
   * @return ActiveDataProvider The data provider for the user login log.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 30,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Gets the last log date for the user login log.
   *
   * @return string|null The last log date in format 'Y-m-d', or null if no record found.
   */
  public function getLastLogDate()
  {
    $date = SystemSetting::findOrCreate(SystemSetting::LAST_CHECKED_TIME);

    if ($date->value > 0) {
      return date('Y-m-d', $date->value);
    }

    return null;
  }
}
