<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%bundle}}`.
 */
class m251024_052625_create_bundle_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%bundle}}', [
            'id'               => $this->primaryKey(),
            'usd_price'        => $this->decimal(36, 18)->notNull(),
            'token_allocation' => $this->integer()->notNull(),
            'total_supply'     => $this->integer()->notNull(),
            'status'           => $this->string(255)->defaultValue('queuing'),
            'created_at'       => $this->bigInteger()->notNull(),
            'updated_at'       => $this->bigInteger(),
            'is_delete'        => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByStatus` (`status`)',
            'key `findByDate` (`created_at`)',
        ], "engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%bundle}}');
    }
}
