<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use yii\data\ActiveDataProvider;
use app\models\AuditLog;
use app\models\Bundle;
use app\models\BundleReward;

class BundleMgmtForm extends Model
{
  public $usd_price;
  public $token_allocation;
  public $total_supply;
  public $reward_type;
  public $reward_amount;

  public function rules()
  {
    return [
      [['usd_price', 'token_allocation', 'total_supply', 'reward_type', 'reward_amount'], 'safe'],
      [['usd_price', 'token_allocation', 'total_supply', 'reward_amount'], 'number', 'min' => 0],
      [['reward_type', 'reward_amount'], 'required', 'on' => ['create-reward', 'update-reward']],
    ];
  }

  public function attributeLabels()
  {
    return [
      'usd_price'        => 'USD Price',
      'token_allocation' => 'Token Allocation',
      'total_supply'     => 'Total Supply',
      'reward_type'      => 'Reward Type',
      'reward_amount'    => 'Reward Amount',
    ];
  }

  public function getQuery()
  {
    $query = Bundle::find();

    if (!empty($this->usd_price)) {
      $query->andFilterWhere(['like', 'usd_price', "%".$this->usd_price."%", false]);
    }

    $query->andWhere(['is_delete' => 0]);

    // throw new \Exception($query->createCommand()->getRawSql());

    return $query;
  }

  public function getProvider()
  {
    $dataProvider =  new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 10,
      ],
    ]);

    return $dataProvider;
  }

  public function getBundleInfo($id)
  {
    $model = Bundle::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Bundle not found');
    }

    $this->usd_price        = $model->usd_price * 1;
    $this->token_allocation = $model->token_allocation * 1;
    $this->total_supply     = $model->total_supply * 1;
  }

  // will only run once
  public function initBundle()
  {
    // check if there is any bundle in the database
    $model = Bundle::findOne([
      'is_delete' => 0,
    ]);

    if (!empty($model)) {
      return;
    }

    // batch create bundle
    $bundle_data = [
      [
        'usd_price'        => 500,
        'token_allocation' => 25000,
        'total_supply'     => 200,
      ],
      [
        'usd_price'        => 2000,
        'token_allocation' => 100000,
        'total_supply'     => 300,
      ],
      [
        'usd_price'        => 10000,
        'token_allocation' => 500000,
        'total_supply'     => 100,
      ],
      [
        'usd_price'        => 20000,
        'token_allocation' => 1000000,
        'total_supply'     => 20,
      ],
    ];

    $bundle_reward_data = [
      [
        'bundle_id' => 1,
        'type'      => 'silver_lootbox',
        'amount'    => 3,
      ],
      [
        'bundle_id' => 2,
        'type'      => 'silver_lootbox',
        'amount'    => 3,
      ],
      [
        'bundle_id' => 2,
        'type'      => 'gold_lootbox',
        'amount'    => 5,
      ],
      [
        'bundle_id' => 3,
        'type'      => 'silver_lootbox',
        'amount'    => 5,
      ],
      [
        'bundle_id' => 3,
        'type'      => 'gold_lootbox',
        'amount'    => 15,
      ],
      [
        'bundle_id' => 4,
        'type'      => 'silver_lootbox',
        'amount'    => 10,
      ],
      [
        'bundle_id' => 4,
        'type'      => 'gold_lootbox',
        'amount'    => 30,
      ],
    ];

    foreach ($bundle_data as $data) {
      $model                  = new Bundle();
      $model->usd_price        = $data['usd_price'];
      $model->token_allocation = $data['token_allocation'];
      $model->total_supply     = $data['total_supply'];
      $model->created_at       = time();

      if (!$model->save()) {
        throw new \Exception(current($model->getFirstErrors()));
      }
    }

    foreach ($bundle_reward_data as $data) {
      $model                  = new BundleReward();
      $model->bundle_id       = $data['bundle_id'];
      $model->type            = $data['type'];
      $model->amount          = $data['amount'];
      $model->created_at      = time();

      if (!$model->save()) {
        throw new \Exception(current($model->getFirstErrors()));
      }
    }

    return;
  }

  public function getBundleRewardProvider($bundle_id)
  {
    $model = Bundle::findOne([
      'bundle_id' => $bundle_id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Bundle not found');
    }

    $query = BundleReward::find();
    $query->andWhere(['bundle_id' => $bundle_id]);
    $query->andWhere(['is_delete' => 0]);

    $dataProvider =  new ActiveDataProvider([
      'query' => $query,
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 10,
      ],
    ]);

    return $dataProvider;
  }

  public function getBundleRewardInfo($id)
  {
    $model = BundleReward::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Bundle price not found');
    }

    $this->reward_type   = $model->type;
    $this->reward_amount = $model->amount * 1;
  }

  public function getRewardType()
  {
    return [
      'silver_lootbox' => 'Silver Lootbox',
      'gold_lootbox'   => 'Gold Lootbox',
    ];
  }

  private function getBundleRewardModel($id)
  {
    if ($id > 0) {
      $model = BundleReward::findOne([
        'id'        => $id,
        'is_delete' => 0,
      ]);

      if (empty($model)) {
        throw new \Exception('Bundle price not found');
      }
    } else {
      $model = new BundleReward();
    }

    return $model;
  }

  public function createOrUpdateBundleReward($bundle_id, $id = 0)
  {
    $base_model = $this->getBundleRewardModel($id);

    $bundle = Bundle::findOne([
      'bundle_id' => $bundle_id,
      'is_delete' => 0,
    ]);

    if (empty($bundle)) {
      throw new \Exception('Bundle not found');
    }

    $admin = Yii::$app->user->identity;

    if ($id > 0) {
      if ($base_model->amount != $this->reward_amount) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_BUNDLE,
          'old_value' => $base_model->amount * 1,
          'value'     => $this->reward_amount * 1,
          'remark'    => 'Update bundle price amount for Bundle ID ' . $bundle->id,
          'action_by' => $admin->id,
        ]);
      }
    }

    $base_model->bundle_id = $bundle->id;
    $base_model->type      = $this->reward_type;
    $base_model->amount    = $this->reward_amount;

    if ($id > 0) {
      $base_model->updated_at = time();
      $base_model->update(false, ['amount', 'updated_at']);
    } else {
      $base_model->created_at = time();

      if (!$base_model->save()) {
        throw new \Exception(current($base_model->getFirstErrors()));
      }

      AuditLog::create([
        'function'  => AuditLog::MANAGE_BUNDLE,
        'remark'    => 'Create new bundle reward for Bundle ID ' . $bundle->id,
        'action_by' => $admin->id,
      ]);
    }

    return $base_model;
  }

  public function deleteBundleReward($id)
  {
    $model = BundleReward::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      return false;
    }

    $model->is_delete = 1;
    $model->update(false, ['is_delete']);

    return true;
  }
}
