<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "export_job".
 *
 * @property int $id
 * @property string|null $ref_no
 * @property string|null $function
 * @property string|null $data
 * @property string|null $status
 * @property string|null $filename
 * @property string|null $filepath
 * @property int|null $action_by
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Admin $actionBy
 */
class ExportJob extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'export_job';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['data', 'filename', 'filepath'], 'string'],
            [['action_by', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['created_at'], 'required'],
            [['ref_no'], 'string', 'max' => 225],
            [['function'], 'string', 'max' => 64],
            [['status'], 'string', 'max' => 32],
            [['action_by'], 'exist', 'skipOnError' => true, 'targetClass' => Admin::className(), 'targetAttribute' => ['action_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'ref_no' => 'Ref No',
            'function' => 'Function',
            'data' => 'Data',
            'status' => 'Status',
            'filename' => 'Filename',
            'filepath' => 'Filepath',
            'action_by' => 'Action By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[ActionBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActionBy()
    {
        return $this->hasOne(Admin::className(), ['id' => 'action_by']);
    }
}
