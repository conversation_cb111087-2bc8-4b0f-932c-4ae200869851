<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "language".
 *
 * @property int $id
 * @property string|null $code
 * @property string|null $label
 * @property string|null $name
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class Language extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'language';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['code', 'label', 'name', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['created_at'], 'required'],
            [['created_at', 'updated_at', 'is_delete'], 'integer'],
            [['code', 'label', 'name'], 'string', 'max' => 255],
            [['code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'code' => 'Code',
            'label' => 'Label',
            'name' => 'Name',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

}
