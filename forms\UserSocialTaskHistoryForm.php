<?php

namespace app\forms;

use Yii;
use yii\helpers\ArrayHelper;
use yii\httpclient\Client;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\AuditLog;
use app\models\SocialTaskLookup;
use app\models\UserSocialTask;

class UserSocialTaskHistoryForm extends Model
{
  public $task_description;
  public $task_name;
  public $type;
  public $reward_point;
  public $username;
  public $user_telegram_id;
  public $first_last_name;
  public $wallet_address;
  public $date_range;
  public $date_start;
  public $date_end;

  public function rules()
  {
    return [
      [['task_description', 'task_name', 'type', 'reward_point', 'username', 'user_telegram_id', 'first_last_name', 'wallet_address', 
        'date_range', 'date_start', 'date_end'], 'safe'],
      [['task_description', 'task_name', 'username', 'user_telegram_id', 'first_last_name', 'wallet_address', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Gets query for [[UserSocialTask]] model.
   * 
   * Select * from user_social_task as ust
   * left join social_task st on st.id = ust.social_task_id
   * left join user u on u.id = ust.user_id
   * where username like %:username% 
   * or u.first_last_name like %:first_last_name%
   * and u.wallet_address like %:wallet_address%
   * and st.task_name like %:task_name%
   * and st.type = :type
   * and ust.created_at between :date_start and :date_end
   * and ust.is_delete = 0
   * @return \yii\db\ActiveQuery
   */
  public function getQuery()
  {
    $query = UserSocialTask::find()
        ->alias('ust')
        ->joinWith('socialTask st')
        ->joinWith('user u');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->user_telegram_id)) {
      $query->andFilterWhere(['u.user_telegram_id' => $this->user_telegram_id]);
    }

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }
    
    if (!empty($this->task_name)) {
      $query->andFilterWhere(['like','st.task_name',"%".$this->task_name."%", false]);
    }

    if (!empty($this->type)) {
      $query->andFilterWhere(['st.type' => $this->type]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'ust.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['ust.is_delete' => 0]);
    return $query;
  }

  /**
   * Gets the data provider for the user social task history.
   *
   * @return ActiveDataProvider The data provider for the user social task history.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 20,
      ],
    ]);

    return $dataProvider;
  }
}
