export default function (sequelize, DataTypes) {
    return sequelize.define('bundle', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        usd_price: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        token_allocation: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        total_supply: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: "queuing",
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'bundle',
        timestamps: false,
    })
}