<?php

use yii\helpers\Url;
use yii\web\View;
use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Stage Management');

$this->registerCss('
  ul {
    margin-bottom: 0;
  }
  .grid-view table tbody td {
    vertical-align: middle;
  }
  .value-bold {
    font-weight: 700;
  }
  .status-badge {
    padding: 4px 12px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.85rem;
    display: inline-block;
    background-color: #f8f9fa;
    color: #495057;
  }
');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_stage', [
      'model'   => $model,
      'page'    => 'stage-mgmt',
      'pjax_id' => "#pjax-stage-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{summary}{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','Stage'),
          'format' => 'raw',
          'value' => function($model) {
            return $model->name;
          },
        ],
        [
          'label'  => Yii::t('app','Price Per Token'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->price_per_token, 2);
          },
        ],
        [
          'label'  => Yii::t('app','Token Available'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->token_available, 0);
          },
        ],
        [
          'label'  => Yii::t('app','Total Fund (USD)'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->total_fund, 0);
          },
        ],
        [
          'label'  => Yii::t('app','Stage End Date (GMT + 8)'),
          'format' => 'raw',
          'value'  => function($model) {
            return '📅 ' . date('Y-m-d H:i:s', $model->should_end_date);
          },
        ],
        [
          'label'  => Yii::t('app','Status'),
          'format' => 'raw',
          'value'  => function($model) {
            $status = strtolower($model->status);
            $emoji = '';
            switch($status) {
              case 'live':
                $emoji = '✅';
                break;
              case 'fulfilled':
                $emoji = '🏁';
                break;
              case 'queuing':
                $emoji = '⏳';
                break;
              default:
                $emoji = '📋';
            }
            return $emoji . ' ' . ucfirst($model->status);
          },
        ],
        [
          'class'    => 'yii\grid\ActionColumn',
          'template' => '
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                Action <span class="dropdown-icon"></span>
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu" role="menu">
                {update}
              </div>
            </div>
          ',
          'buttons'  => [
            'update' => function ($url, $model) {
              return Html::a(Yii::t('app','Update Info'), ['update-stage', 'id' => $model->id], [
                'class' => 'dropdown-item',
              ]);
            },
          ],
        ]
      ]
    ]); ?>
  </div>
</div>
