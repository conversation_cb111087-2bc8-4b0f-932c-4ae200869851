<?php

namespace app\forms;

use Yii;
use app\models\AuditLog;
use app\models\SystemSetting;

class SettingsForm extends AttributeChangeForm
{
  private $_model = [];

  public $bsc_explorer;
  public $ton_explorer;

  public function rules()
  {
    return [
      [['bsc_explorer', 'ton_explorer'], 'safe'],
    ];
  }

  public function attributeLabels()
  {
    return [
        'bsc_explorer' => 'BSC Explorer',
        'ton_explorer' => 'Ton Explorer',
    ];
  }

  /**
   * Initializes the object.
   *
   * @return void
   */
  public function init()
  {
    parent::init();

    $_bsc_explorer = SystemSetting::findOrCreate(SystemSetting::BSC_EXPLORER, 'https://bscscan.com');
    $_ton_explorer    = SystemSetting::findOrCreate(SystemSetting::TON_EXPLORER, 'https://tonviewer.com');

    $this->bsc_explorer = $_bsc_explorer->value;
    $this->ton_explorer = $_ton_explorer->value;

    $this->initAttributes();
  }

  /**
   * Save the changed attributes.
   * 
   * @param \app\models\Admin $user The user who made the change.
   * 
   * @return bool Whether the save is successful.
   */
  public function save($user)
  {
    if (!$this->validate()) {
      return false;
    }

    $attributes = $this->getChangedAttributes();
    $new_remark = "";
    $old_remark = "";

    foreach ($attributes as $attr => $change) {
      $model             = $this->getModel($attr);
      $model->value      = $change['new'];
      $model->updated_at = time();
      $model->update(false,['value','updated_at']);

      AuditLog::create([
        'function'  => AuditLog::UPDATE_SYSTEM_SETTING,
        'old_value' => $change['old'],
        'value'     => $model->value,
        'remark'    => $this->getAttributeLabel($attr),
        'action_by' => $user->id,
      ]);
    }

    return true;
  }

  /**
   * Get a model from SystemSetting by attribute name.
   *
   * @param string $attr The attribute name.
   *
   * @return SystemSetting
   *
   * @throws \Exception
   */
  public function getModel($attr)
  {
    if (isset($this->_model[$attr])) {
      return $this->_model[$attr];
    }

    if ($attr == 'bsc_explorer') {
        $key = SystemSetting::BSC_EXPLORER;
    } else if ($attr == 'ton_explorer') {
        $key = SystemSetting::TON_EXPLORER;
    } else {
      throw new \Exception("Invalid attribute");
    }
    
    $model = SystemSetting::findOrCreate(
        $key
      );
  
      return $this->_model[$attr] = $model;
    }

    /**
     * Returns a success message that is displayed after the settings have been updated.
     *
     * @return string
     */
    public function getSuccessMessage()
    {
      return Yii::t('app', 'Settings have been updated.');
    }
}