<?php

use yii\db\Migration;

class m250904_073608_alter_notice_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('notice_lookup', 'icon_image_url', $this->text());

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('notice_lookup', 'icon_image_url', $this->text()->notNull());
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250904_073608_alter_referral_reward_lookup_table cannot be reverted.\n";

        return false;
    }
    */
}
