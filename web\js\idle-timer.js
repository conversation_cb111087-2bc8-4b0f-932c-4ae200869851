(function($) {
  var idleTime = 0;
  let lastActivityTime = new Date().getTime();
  var idleInterval = setInterval(timerIncrement, 60000); // 1 minute interval
  var alertShown = false;
  var warningTimeout;

  // Create a form for POST logout
  function postLogout() {
    var form = $('<form/>', {
      action: idleConfig.logoutUrl,
      method: 'post'
    });

    // Add CSRF token if needed
    form.append($('<input/>', {
      type: 'hidden',
      name: yii.getCsrfParam(),
      value: yii.getCsrfToken()
    }));

    form.appendTo('body').submit();
  }

  // Reset timer on user activity
  $(document).on('mousemove keypress scroll click', function() {
    if (!alertShown) {
      idleTime = 0;
      clearTimeout(warningTimeout); // Clear the warning timeout
      // Throttle the server ping to avoid too many requests
      if (!this.pingSent) {
        this.pingSent = true;
        setTimeout(() => {
          this.pingSent = false;
          // Send ping to server to update session
          $.get(idleConfig.pingUrl)
          .done(function(response) {
            if (!response.success) {
              window.location.reload();
            }
          });
        }, 5000);
      }
    }
  });

  function timerIncrement() {
    // Calculate idle time based on last activity
    const currentTime = new Date().getTime();
    idleTime = Math.floor((currentTime - lastActivityTime) / 60000);

    // Show warning at warning time
    if (idleTime >= idleConfig.logoutTime && !alertShown) {
      alertShown = true; // Set alertShown to true to avoid multiple alerts

      // Display the message to the user
      alert(idleConfig.warningMessage);

      // Perform the logout
      postLogout();
    }

    // if (idleTime >= idleConfig.warningTime && !alertShown) {
    //   alertShown = true;

    //   // Display confirmation dialog
    //   var continueSession = confirm(idleConfig.warningMessage);

    //   // Set a timeout to automatically log out after 10 seconds
    //   var confirmTimeout = setTimeout(function() {
    //     // User did not respond in time - perform logout
    //     if (continueSession == false) {
    //       postLogout();
    //     }
    //   }, 10000); // 10 seconds to respond

    //   // User responded - clear the timeout
    //   clearTimeout(confirmTimeout);

    //   if (continueSession) {
    //     // User clicked OK - reset timer
    //     alertShown = false;
    //     idleTime = 0;
    //     lastActivityTime = new Date().getTime();
    //   } else {
    //     // User clicked Cancel - perform logout
    //     postLogout();
    //   }
    // }
  }
})(jQuery);