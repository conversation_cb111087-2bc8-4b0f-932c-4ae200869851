export default function (sequelize, DataTypes) {
    return sequelize.define('wallet_transaction', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        wallet_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        credit_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        tx_id: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        type: {
            type: DataTypes.ENUM,
            values: ['in', 'out'],
            allowNull: false
        },
        amount: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        before_balance: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        after_balance: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        category: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        description: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        remark: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'wallet_transaction',
        timestamps: false,
    })
}