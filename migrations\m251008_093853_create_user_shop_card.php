<?php

use yii\db\Migration;

class m251008_093853_create_user_shop_card extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_shop_card}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'rarity_id' => $this->integer()->notNull(),
            'instrument_id' => $this->integer()->notNull(),
            'is_used' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`rarity_id`) references `rarity_lookup` (`id`)',
            'foreign key (`instrument_id`) references `instrument_lookup` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByRarityId` (`rarity_id`, `is_delete`)',
            'key `findByInstrumentId` (`instrument_id`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_shop_card}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093853_create_user_shop_card cannot be reverted.\n";

        return false;
    }
    */
}
