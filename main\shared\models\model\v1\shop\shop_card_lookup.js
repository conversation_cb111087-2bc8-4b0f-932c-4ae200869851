export default function (sequelize, DataTypes) {
    return sequelize.define('shop_card_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        rarity_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        card_type: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        card_price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'shop_card_lookup',
        timestamps: false,
    })
}