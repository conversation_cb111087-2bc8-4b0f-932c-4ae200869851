<?php

use yii\db\Migration;

class m250908_030307_update_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('user', 'wallet_address', $this->string()->unique()->after('user_instagram_id'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('user', 'wallet_address');

    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250908_030307_update_user_table cannot be reverted.\n";

        return false;
    }
    */
}
