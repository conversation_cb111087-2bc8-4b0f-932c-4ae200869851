<?php

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'User Card History');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_user_card_history', [
      'model'   => $model,
      'page'    => 'user-card-history',
      'pjax_id' => "#pjax-user-card-history",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getCardProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app','Username'),
          'format' => 'raw',
          'value' => function ($model) {
            $username = $model->user->username;
            $telegram_id = $model->user->user_telegram_id;
            if (empty($model->user->username)) {
              $username = $model->user->first_last_name;
            }

            if (empty($model->user->user_telegram_id)) {
              $telegram_id = '-';
            }

            $display = "Username : ". $username . "<br>" . "Telegram ID : " . $telegram_id;

            return $display;
          },
        ],
        [
          'label'  => Yii::t('app','Wallet Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->user->user->wallet_address)) {
              return '-';
            }

            $link = Yii::$app->formatHelper->formatTon('address', $model->user->wallet_address, 5);

            return $link;
          },
        ],
        [
          'label'  => Yii::t('app','Rarity Type'),
          'format' => 'raw',
          'value'  => function ($model) {
            return ucfirst($model->rarity->rarity_name);
          },
        ],
        [
          'label'  => Yii::t('app','Instrument Name'),
          'format' => 'raw',
          'value'  => function ($model) {
            return ucfirst($model->instrument->instrument_name);
          },
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
      ]
    ]); ?>
  </div>
</div>
