import { query, body } from "express-validator";

const shop_card_validator = [
    // body("shop_card_id").exists().withMessage("shop_card_id is required").bail()
    //     .isInt().withMessage("shop_card_id must be an integer").bail()
    //     .notEmpty().withMessage("shop_card_id cannot be empty."),
    body("rarity_id").exists().withMessage("rarity_id is required").bail()
        .isInt().withMessage("rarity_id must be an integer").bail()
        .notEmpty().withMessage("rarity_id cannot be empty."),
    body("instrument_id").exists().withMessage("instrument_id is required").bail()
        .isInt().withMessage("instrument_id must be an integer").bail()
        .notEmpty().withMessage("instrument_id cannot be empty."),
]

const shop_item_bundle_validator = [
    body("bundle_item_id").exists().withMessage("bundle_item_id is required").bail()
        .isInt().withMessage("bundle_item_id must be an integer").bail()
        .notEmpty().withMessage("bundle_item_id cannot be empty."),
    body("quantity")
        .exists().withMessage("quantity is required")
        .bail()
        .isInt({ gt: 0 }).withMessage("quantity must be a positive integer greater than 0")
]

const shop_item_booster_validator = [
    body("booster_item_id").exists().withMessage("booster_item_id is required").bail()
        .isInt().withMessage("booster_item_id must be an integer").bail()
        .notEmpty().withMessage("booster_item_id cannot be empty."),
    body("quantity")
        .exists().withMessage("quantity is required")
        .bail()
        .isInt({ gt: 0 }).withMessage("quantity must be a positive integer greater than 0")
]

export default {
    shop_card_validator,
    shop_item_bundle_validator,
    shop_item_booster_validator,
}