<?php

namespace app\components;

use yii\web\User;

class WebUser extends User
{
	/**
	 * Checks if the user can perform the specified action.
	 *
	 * The first parameter can be either a string representing a single permission
	 * or an array of strings representing multiple permissions. If an array is
	 * given, the method will return true if any of the permissions is granted.
	 *
	 * @param string|array $permissions the permission(s) to check
	 * @param array $params parameters to pass to the permission
	 * @param boolean $allowCaching whether to allow caching the result
	 * @return boolean whether the user can perform the action
	 */
	public function can($permissions, $params=[], $allowCaching=true)
	{
		if (is_array($permissions)) {
			foreach ($permissions as $permission) {
				if (parent::can($permission, $params, $allowCaching)) {
					return true;
				}
			}
			return false;
		}

		return parent::can($permissions, $params, $allowCaching);
	}
}
