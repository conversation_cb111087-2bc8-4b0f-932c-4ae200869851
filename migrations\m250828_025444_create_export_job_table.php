<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%export_job}}`.
 */
class m250828_025444_create_export_job_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%export_job}}', [
            'id' => $this->primaryKey(),
            'ref_no'          => $this->string(225),
            'function'        => $this->string(64),
            'data'            => $this->text(),
            'status'          => $this->string(32),
            'filename'        => $this->text()->defaultValue(null),
            'filepath'        => $this->text()->defaultValue(null),
            'action_by'       => $this->integer(),
            'created_at'      => $this->bigInteger()->notNull(),
            'updated_at'      => $this->bigInteger(),
            'is_delete'       => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`action_by`) references `admin` (`id`)',
            'key `exRef` (`ref_no`, `is_delete`)',
            'key `exStatus` (`status`, `is_delete`)',
            'key `exFunction` (`function`, `is_delete`)',
            'key `exCreatedAt` (`created_at`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%export_job}}');
    }
}
