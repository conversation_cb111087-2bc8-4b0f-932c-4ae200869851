<?php

use yii\db\Migration;

class m251008_093845_create_user_reset_card extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_reset_card}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'trigger_at' => $this->bigInteger()->notNull(),
            'is_active' => 'tinyint(1) DEFAULT 1 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_reset_card}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093845_create_user_reset_card cannot be reverted.\n";

        return false;
    }
    */
}
