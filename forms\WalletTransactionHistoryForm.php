<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\ArrayHelper;
use yii\data\ActiveDataProvider;
use app\models\WalletTransaction;

class WalletTransactionHistoryForm extends Model
{
  public $username;
  public $address;
  public $type;
  public $category;
  public $date_start;
  public $date_end;
  public $date_range;
  public $tx_id;

  public function rules()
  {
    return [
      [[
        'username', 'address', 'type', 'category', 'date_start',
        'date_end', 'date_range', 'tx_id'
      ], 'safe'],
      [['username', 'address', 'date_range', 'tx_id'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Gets query for data provider
   *
   * Select * from wallet_transaction as wt
   * left join user u on u.id = wt.user_id
   * where u.username like %:username% or u.first_last_name like %:username%
   * and u.wallet_address like %:address% and wt.type like :type
   * and wt.category like :category and wt.tx_id like %:tx_id%
   * and wt.created_at between :date_start and :date_end
   * and u.is_delete = 0 and wt.is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = WalletTransaction::find();
    $query->alias('wt');
    //$query->leftJoin('wallet w', 'w.id = wt.wallet_id');
    //$query->leftJoin('user u', 'u.wallet_id = w.id');
    $query->leftJoin('user u', 'u.id = wt.user_id');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->address."%", false]);
    }

    if (!empty($this->type)) {
      $query->andFilterWhere(['wt.type' => $this->type]);
    }

    if (!empty($this->category)) {
      $query->andFilterWhere(['wt.category' => $this->category]);
    }

    if (!empty($this->tx_id)) {
      $query->andFilterWhere(['like','wt.tx_id',"%".$this->tx_id."%", false]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'wt.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['u.is_delete' => 0]);
    $query->andWhere(['wt.is_delete' => 0]);

    return $query;
  }

  /**
   * Gets the data provider for the wallet transaction history based on the current form data.
   *
   * @return ActiveDataProvider The data provider for the wallet transaction history.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }
}
