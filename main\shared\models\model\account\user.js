export default function (sequelize, DataTypes) {
    return sequelize.define('user', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_telegram_id: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true
        },
        is_premium: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        username: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        first_last_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        profile_pic: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        email_address: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true,
        },
        password: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        jwt_token: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        temp_referral_user_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        has_joined_channel: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        is_username_checked: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        account_type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "telegram",
        },
        current_referral_ranking: {
            type: DataTypes.STRING,
            allowNull: true
        },
        is_verified: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        is_aleko_verified: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        verification_token: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        verification_token_expired_date: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        user_x_id: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        user_instagram_id: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        wallet_address: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'user',
        timestamps: false,
    })
}