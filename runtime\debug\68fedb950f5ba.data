a:14:{s:6:"config";s:6366:"a:5:{s:10:"phpVersion";s:6:"7.4.33";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:5:"Aleko";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"7.4.33";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:26:{s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:64:"C:\xampp\htdocs\aleko-bo\vendor/2amigos/yii2-ckeditor-widget/src";}}s:25:"alexantr/yii2-colorpicker";a:3:{s:4:"name";s:25:"alexantr/yii2-colorpicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@alexantr/colorpicker";s:57:"C:\xampp\htdocs\aleko-bo\vendor/alexantr/yii2-colorpicker";}}s:24:"asmoday74/yii2-ckeditor5";a:3:{s:4:"name";s:24:"asmoday74/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:20:"@asmoday74/ckeditor5";s:56:"C:\xampp\htdocs\aleko-bo\vendor/asmoday74/yii2-ckeditor5";}}s:29:"cetver/yii2-language-selector";a:3:{s:4:"name";s:29:"cetver/yii2-language-selector";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:24:"@cetver/LanguageSelector";s:61:"C:\xampp\htdocs\aleko-bo\vendor/cetver/yii2-language-selector";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:65:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:58:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte3/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:61:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:56:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-dialog/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-fileinput/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/date";s:67:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datepicker/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:60:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-date-range/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:71:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-typeahead/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:54:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-grid/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:64:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-select2/src";}}s:20:"nickcv/yii2-mandrill";a:3:{s:4:"name";s:20:"nickcv/yii2-mandrill";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@nickcv/mandrill";s:56:"C:\xampp\htdocs\aleko-bo\vendor/nickcv/yii2-mandrill/src";}}s:23:"sangroya/yii2-ckeditor5";a:3:{s:4:"name";s:23:"sangroya/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@sangroya/ckeditor5";s:55:"C:\xampp\htdocs\aleko-bo\vendor/sangroya/yii2-ckeditor5";}}s:27:"unclead/yii2-multiple-input";a:3:{s:4:"name";s:27:"unclead/yii2-multiple-input";s:7:"version";s:8:"********";s:5:"alias";a:3:{s:22:"@unclead/multipleinput";s:63:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/src";s:28:"@unclead/multipleinput/tests";s:65:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/tests";s:31:"@unclead/multipleinput/examples";s:68:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/examples";}}s:21:"yiier/yii2-aliyun-oss";a:3:{s:4:"name";s:21:"yiier/yii2-aliyun-oss";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@yiier/AliyunOSS";s:53:"C:\xampp\htdocs\aleko-bo\vendor/yiier/yii2-aliyun-oss";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-faker/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-httpclient/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-jui/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:10:"@yii/debug";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-debug/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.1.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:60:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-swiftmailer/src";}}}}";s:3:"log";s:65368:"a:1:{s:8:"messages";a:214:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532821.052666;i:4;a:0:{}i:5;i:2705424;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532821.052677;i:4;a:0:{}i:5;i:2706600;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532821.062338;i:4;a:0:{}i:5;i:3884064;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532821.066722;i:4;a:0:{}i:5;i:4204016;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532821.067248;i:4;a:0:{}i:5;i:4228152;}i:8;a:6:{i:0;s:35:"Route requested: 'gii/default/view'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1761532821.074381;i:4;a:0:{}i:5;i:4745432;}i:9;a:6:{i:0;s:30:"Route to run: gii/default/view";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1761532821.076608;i:4;a:0:{}i:5;i:4883256;}i:10;a:6:{i:0;s:67:"Running action: yii\gii\controllers\DefaultController::actionView()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1761532821.08481;i:4;a:0:{}i:5;i:5666344;}i:11;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.097866;i:4;a:0:{}i:5;i:6932000;}i:12;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532821.097896;i:4;a:0:{}i:5;i:6933688;}i:17;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131539;i:4;a:0:{}i:5;i:6999184;}i:20;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.13296;i:4;a:0:{}i:5;i:7018072;}i:23;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135152;i:4;a:0:{}i:5;i:7112320;}i:26;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135957;i:4;a:0:{}i:5;i:7119328;}i:29;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139773;i:4;a:0:{}i:5;i:7135056;}i:32;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140128;i:4;a:0:{}i:5;i:7130688;}i:35;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140534;i:4;a:0:{}i:5;i:7132616;}i:38;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.14372;i:4;a:0:{}i:5;i:7145392;}i:41;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144012;i:4;a:0:{}i:5;i:7142720;}i:44;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144357;i:4;a:0:{}i:5;i:7143888;}i:47;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147332;i:4;a:0:{}i:5;i:7152400;}i:50;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147605;i:4;a:0:{}i:5;i:7151928;}i:53;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.14794;i:4;a:0:{}i:5;i:7152200;}i:56;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151331;i:4;a:0:{}i:5;i:7166880;}i:59;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151908;i:4;a:0:{}i:5;i:7163208;}i:62;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.15236;i:4;a:0:{}i:5;i:7166096;}i:65;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.155994;i:4;a:0:{}i:5;i:7183440;}i:68;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.156634;i:4;a:0:{}i:5;i:7181336;}i:71;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.157124;i:4;a:0:{}i:5;i:7182512;}i:74;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160452;i:4;a:0:{}i:5;i:7188080;}i:77;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160704;i:4;a:0:{}i:5;i:7189264;}i:80;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.161073;i:4;a:0:{}i:5;i:7190432;}i:83;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.163862;i:4;a:0:{}i:5;i:7199832;}i:86;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164128;i:4;a:0:{}i:5;i:7198864;}i:89;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164465;i:4;a:0:{}i:5;i:7200368;}i:92;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168278;i:4;a:0:{}i:5;i:7204976;}i:95;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168527;i:4;a:0:{}i:5;i:7206688;}i:98;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.16891;i:4;a:0:{}i:5;i:7208376;}i:101;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172412;i:4;a:0:{}i:5;i:7214832;}i:104;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.17271;i:4;a:0:{}i:5;i:7215528;}i:107;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.173087;i:4;a:0:{}i:5;i:7216096;}i:110;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176264;i:4;a:0:{}i:5;i:7226480;}i:113;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176556;i:4;a:0:{}i:5;i:7225016;}i:116;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176901;i:4;a:0:{}i:5;i:7225288;}i:119;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180243;i:4;a:0:{}i:5;i:7242936;}i:122;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180575;i:4;a:0:{}i:5;i:7237680;}i:125;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180931;i:4;a:0:{}i:5;i:7239888;}i:128;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.184883;i:4;a:0:{}i:5;i:7249272;}i:131;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185196;i:4;a:0:{}i:5;i:7252432;}i:134;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185601;i:4;a:0:{}i:5;i:7252688;}i:137;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190069;i:4;a:0:{}i:5;i:7267352;}i:140;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190464;i:4;a:0:{}i:5;i:7263592;}i:143;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190897;i:4;a:0:{}i:5;i:7263840;}i:146;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194536;i:4;a:0:{}i:5;i:7273248;}i:149;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194898;i:4;a:0:{}i:5;i:7272320;}i:152;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.195311;i:4;a:0:{}i:5;i:7273464;}i:155;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198629;i:4;a:0:{}i:5;i:7287168;}i:158;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198924;i:4;a:0:{}i:5;i:7283928;}i:161;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.199274;i:4;a:0:{}i:5;i:7285744;}i:164;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202496;i:4;a:0:{}i:5;i:7303408;}i:167;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202858;i:4;a:0:{}i:5;i:7298064;}i:170;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.203259;i:4;a:0:{}i:5;i:7298336;}i:173;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206662;i:4;a:0:{}i:5;i:7308744;}i:176;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.20698;i:4;a:0:{}i:5;i:7307248;}i:179;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.207418;i:4;a:0:{}i:5;i:7308144;}i:182;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.2109;i:4;a:0:{}i:5;i:7317600;}i:185;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211306;i:4;a:0:{}i:5;i:7316592;}i:188;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211845;i:4;a:0:{}i:5;i:7316848;}i:191;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217413;i:4;a:0:{}i:5;i:7321408;}i:194;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217898;i:4;a:0:{}i:5;i:7323160;}i:197;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.21833;i:4;a:0:{}i:5;i:7323416;}i:200;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.223709;i:4;a:0:{}i:5;i:7336136;}i:203;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224215;i:4;a:0:{}i:5;i:7333512;}i:206;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224736;i:4;a:0:{}i:5;i:7333768;}i:209;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228712;i:4;a:0:{}i:5;i:7344152;}i:212;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229022;i:4;a:0:{}i:5;i:7342696;}i:215;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229407;i:4;a:0:{}i:5;i:7342968;}i:218;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.23368;i:4;a:0:{}i:5;i:7353392;}i:221;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233966;i:4;a:0:{}i:5;i:7351896;}i:224;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.234329;i:4;a:0:{}i:5;i:7352168;}i:227;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.237714;i:4;a:0:{}i:5;i:7361672;}i:230;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238146;i:4;a:0:{}i:5;i:7360664;}i:233;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238572;i:4;a:0:{}i:5;i:7360936;}i:236;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242371;i:4;a:0:{}i:5;i:7370336;}i:239;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242684;i:4;a:0:{}i:5;i:7369408;}i:242;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.243071;i:4;a:0:{}i:5;i:7370592;}i:245;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246532;i:4;a:0:{}i:5;i:7384288;}i:248;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246856;i:4;a:0:{}i:5;i:7381128;}i:251;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.247232;i:4;a:0:{}i:5;i:7381400;}i:254;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250619;i:4;a:0:{}i:5;i:7396152;}i:257;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250937;i:4;a:0:{}i:5;i:7400536;}i:260;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.251324;i:4;a:0:{}i:5;i:7400792;}i:263;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.254864;i:4;a:0:{}i:5;i:7413536;}i:266;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255206;i:4;a:0:{}i:5;i:7410872;}i:269;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255587;i:4;a:0:{}i:5;i:7411120;}i:272;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259191;i:4;a:0:{}i:5;i:7423784;}i:275;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259529;i:4;a:0:{}i:5;i:7421216;}i:278;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259909;i:4;a:0:{}i:5;i:7421472;}i:281;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263294;i:4;a:0:{}i:5;i:7429944;}i:284;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263584;i:4;a:0:{}i:5;i:7429504;}i:287;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263963;i:4;a:0:{}i:5;i:7429776;}i:290;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267538;i:4;a:0:{}i:5;i:7442544;}i:293;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267908;i:4;a:0:{}i:5;i:7439832;}i:296;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.268317;i:4;a:0:{}i:5;i:7440080;}i:299;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272338;i:4;a:0:{}i:5;i:7469592;}i:302;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272786;i:4;a:0:{}i:5;i:7457824;}i:305;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.273212;i:4;a:0:{}i:5;i:7459376;}i:308;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.276806;i:4;a:0:{}i:5;i:7469712;}i:311;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277148;i:4;a:0:{}i:5;i:7468264;}i:314;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277563;i:4;a:0:{}i:5;i:7471816;}i:317;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.280931;i:4;a:0:{}i:5;i:7481240;}i:320;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281217;i:4;a:0:{}i:5;i:7480272;}i:323;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281567;i:4;a:0:{}i:5;i:7481448;}i:326;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.284706;i:4;a:0:{}i:5;i:7491776;}i:329;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285025;i:4;a:0:{}i:5;i:7490328;}i:332;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285431;i:4;a:0:{}i:5;i:7492568;}i:335;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289005;i:4;a:0:{}i:5;i:7504272;}i:338;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289386;i:4;a:0:{}i:5;i:7502208;}i:341;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289824;i:4;a:0:{}i:5;i:7503376;}i:344;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293309;i:4;a:0:{}i:5;i:7512776;}i:347;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.29361;i:4;a:0:{}i:5;i:7511856;}i:350;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293992;i:4;a:0:{}i:5;i:7513592;}i:353;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297105;i:4;a:0:{}i:5;i:7522976;}i:356;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.29739;i:4;a:0:{}i:5;i:7522056;}i:359;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297767;i:4;a:0:{}i:5;i:7523208;}i:362;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.30114;i:4;a:0:{}i:5;i:7533528;}i:365;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301436;i:4;a:0:{}i:5;i:7532080;}i:368;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301805;i:4;a:0:{}i:5;i:7534312;}i:371;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305349;i:4;a:0:{}i:5;i:7543672;}i:374;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305671;i:4;a:0:{}i:5;i:7542752;}i:377;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.306077;i:4;a:0:{}i:5;i:7544456;}i:380;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309671;i:4;a:0:{}i:5;i:7556048;}i:383;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309986;i:4;a:0:{}i:5;i:7554072;}i:386;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.310393;i:4;a:0:{}i:5;i:7556312;}i:389;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313544;i:4;a:0:{}i:5;i:7576384;}i:392;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313945;i:4;a:0:{}i:5;i:7570120;}i:395;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.314411;i:4;a:0:{}i:5;i:7576712;}i:398;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.31782;i:4;a:0:{}i:5;i:7592464;}i:401;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318214;i:4;a:0:{}i:5;i:7588168;}i:404;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318625;i:4;a:0:{}i:5;i:7589328;}i:407;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3221;i:4;a:0:{}i:5;i:7607840;}i:410;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.32247;i:4;a:0:{}i:5;i:7602408;}i:413;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.323981;i:4;a:0:{}i:5;i:7691288;}i:416;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327786;i:4;a:0:{}i:5;i:7699776;}i:419;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328522;i:4;a:0:{}i:5;i:7702840;}i:422;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328769;i:4;a:0:{}i:5;i:7705528;}i:425;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328988;i:4;a:0:{}i:5;i:7707840;}i:428;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329193;i:4;a:0:{}i:5;i:7710152;}i:431;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329364;i:4;a:0:{}i:5;i:7711912;}i:434;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329538;i:4;a:0:{}i:5;i:7714008;}i:437;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329735;i:4;a:0:{}i:5;i:7717000;}i:440;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329918;i:4;a:0:{}i:5;i:7719640;}i:443;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330119;i:4;a:0:{}i:5;i:7722960;}i:446;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330329;i:4;a:0:{}i:5;i:7725192;}i:449;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330502;i:4;a:0:{}i:5;i:7726904;}i:452;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330755;i:4;a:0:{}i:5;i:7730224;}i:455;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331034;i:4;a:0:{}i:5;i:7732856;}i:458;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331242;i:4;a:0:{}i:5;i:7735456;}i:461;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331446;i:4;a:0:{}i:5;i:7737552;}i:464;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331677;i:4;a:0:{}i:5;i:7740360;}i:467;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33189;i:4;a:0:{}i:5;i:7742976;}i:470;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332152;i:4;a:0:{}i:5;i:7745904;}i:473;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332441;i:4;a:0:{}i:5;i:7749680;}i:476;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332677;i:4;a:0:{}i:5;i:7751888;}i:479;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332888;i:4;a:0:{}i:5;i:7754112;}i:482;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333106;i:4;a:0:{}i:5;i:7756208;}i:485;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33329;i:4;a:0:{}i:5;i:7759696;}i:488;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333499;i:4;a:0:{}i:5;i:7761960;}i:491;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33375;i:4;a:0:{}i:5;i:7764560;}i:494;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333943;i:4;a:0:{}i:5;i:7766288;}i:497;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334142;i:4;a:0:{}i:5;i:7768408;}i:500;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334366;i:4;a:0:{}i:5;i:7771064;}i:503;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334581;i:4;a:0:{}i:5;i:7773296;}i:506;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334767;i:4;a:0:{}i:5;i:7775904;}i:509;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33494;i:4;a:0:{}i:5;i:7777632;}i:512;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335104;i:4;a:0:{}i:5;i:7779744;}i:515;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335334;i:4;a:0:{}i:5;i:7798776;}i:518;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335522;i:4;a:0:{}i:5;i:7801072;}i:521;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335683;i:4;a:0:{}i:5;i:7803192;}i:524;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335845;i:4;a:0:{}i:5;i:7805296;}i:527;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336029;i:4;a:0:{}i:5;i:7808240;}i:530;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336251;i:4;a:0:{}i:5;i:7810448;}i:533;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336452;i:4;a:0:{}i:5;i:7812704;}i:536;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336654;i:4;a:0:{}i:5;i:7814424;}i:539;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336871;i:4;a:0:{}i:5;i:7816528;}i:542;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33709;i:4;a:0:{}i:5;i:7818736;}i:545;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337288;i:4;a:0:{}i:5;i:7820816;}i:548;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337464;i:4;a:0:{}i:5;i:7822928;}i:551;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337656;i:4;a:0:{}i:5;i:7825184;}i:554;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337859;i:4;a:0:{}i:5;i:7829056;}i:557;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33805;i:4;a:0:{}i:5;i:7830776;}i:560;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338231;i:4;a:0:{}i:5;i:7832912;}i:563;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338483;i:4;a:0:{}i:5;i:7835240;}i:566;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338732;i:4;a:0:{}i:5;i:7837472;}i:569;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338979;i:4;a:0:{}i:5;i:7840112;}i:572;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339213;i:4;a:0:{}i:5;i:7842448;}i:575;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339463;i:4;a:0:{}i:5;i:7844800;}i:578;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33972;i:4;a:0:{}i:5;i:7847160;}i:581;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339975;i:4;a:0:{}i:5;i:7849520;}i:584;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340223;i:4;a:0:{}i:5;i:7852192;}i:587;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340458;i:4;a:0:{}i:5;i:7854520;}i:590;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340663;i:4;a:0:{}i:5;i:7856320;}i:593;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340889;i:4;a:0:{}i:5;i:7857976;}i:596;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341103;i:4;a:0:{}i:5;i:7860600;}i:599;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341317;i:4;a:0:{}i:5;i:7862832;}i:602;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341517;i:4;a:0:{}i:5;i:7865064;}i:605;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341689;i:4;a:0:{}i:5;i:7866792;}i:608;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341954;i:4;a:0:{}i:5;i:7874272;}i:611;a:6:{i:0;s:108:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/default/model.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.344151;i:4;a:0:{}i:5;i:8030896;}i:612;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.344967;i:4;a:0:{}i:5;i:7955520;}i:613;a:6:{i:0;s:99:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.349211;i:4;a:0:{}i:5;i:8512920;}i:614;a:6:{i:0;s:102:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view/files.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.358952;i:4;a:0:{}i:5;i:8665272;}i:615;a:6:{i:0;s:101:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\layouts\generator.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.360297;i:4;a:0:{}i:5;i:8646952;}i:616;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/views/layouts/main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532821.361234;i:4;a:0:{}i:5;i:8702952;}}}";s:9:"profiling";s:126043:"a:3:{s:6:"memory";i:8974624;s:4:"time";d:0.32220888137817383;s:8:"messages";a:400:{i:13;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532821.097905;i:4;a:0:{}i:5;i:6934064;}i:14;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532821.119889;i:4;a:0:{}i:5;i:6982728;}i:15;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.119932;i:4;a:0:{}i:5;i:6982216;}i:16;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131446;i:4;a:0:{}i:5;i:6997912;}i:18;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131558;i:4;a:0:{}i:5;i:6999800;}i:19;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131768;i:4;a:0:{}i:5;i:7002296;}i:21;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.132978;i:4;a:0:{}i:5;i:7020184;}i:22;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.133343;i:4;a:0:{}i:5;i:7023712;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135172;i:4;a:0:{}i:5;i:7112888;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135913;i:4;a:0:{}i:5;i:7117840;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135969;i:4;a:0:{}i:5;i:7119928;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139709;i:4;a:0:{}i:5;i:7134416;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139789;i:4;a:0:{}i:5;i:7135656;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139978;i:4;a:0:{}i:5;i:7137472;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140141;i:4;a:0:{}i:5;i:7134080;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140499;i:4;a:0:{}i:5;i:7135712;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140543;i:4;a:0:{}i:5;i:7133256;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143676;i:4;a:0:{}i:5;i:7144784;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143733;i:4;a:0:{}i:5;i:7146008;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143891;i:4;a:0:{}i:5;i:7147968;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144024;i:4;a:0:{}i:5;i:7144832;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144322;i:4;a:0:{}i:5;i:7147320;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144366;i:4;a:0:{}i:5;i:7144504;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147276;i:4;a:0:{}i:5;i:7151776;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147348;i:4;a:0:{}i:5;i:7153000;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147502;i:4;a:0:{}i:5;i:7154312;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147617;i:4;a:0:{}i:5;i:7154040;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147909;i:4;a:0:{}i:5;i:7155672;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147948;i:4;a:0:{}i:5;i:7152840;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151199;i:4;a:0:{}i:5;i:7166256;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151367;i:4;a:0:{}i:5;i:7167520;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151777;i:4;a:0:{}i:5;i:7169992;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151919;i:4;a:0:{}i:5;i:7165320;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.152296;i:4;a:0:{}i:5;i:7169528;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.152374;i:4;a:0:{}i:5;i:7166712;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.155874;i:4;a:0:{}i:5;i:7177184;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.15604;i:4;a:0:{}i:5;i:7184040;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.156493;i:4;a:0:{}i:5;i:7185736;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.156648;i:4;a:0:{}i:5;i:7183448;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.157069;i:4;a:0:{}i:5;i:7185928;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.157136;i:4;a:0:{}i:5;i:7183152;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160391;i:4;a:0:{}i:5;i:7187472;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160466;i:4;a:0:{}i:5;i:7188696;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160621;i:4;a:0:{}i:5;i:7190072;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160715;i:4;a:0:{}i:5;i:7191376;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.161016;i:4;a:0:{}i:5;i:7193864;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.161085;i:4;a:0:{}i:5;i:7191048;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.163786;i:4;a:0:{}i:5;i:7199208;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.163879;i:4;a:0:{}i:5;i:7200432;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164024;i:4;a:0:{}i:5;i:7202000;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164138;i:4;a:0:{}i:5;i:7200976;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164425;i:4;a:0:{}i:5;i:7203464;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164476;i:4;a:0:{}i:5;i:7201008;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168217;i:4;a:0:{}i:5;i:7204368;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168293;i:4;a:0:{}i:5;i:7205592;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168451;i:4;a:0:{}i:5;i:7207032;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168538;i:4;a:0:{}i:5;i:7208800;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168874;i:4;a:0:{}i:5;i:7211808;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168919;i:4;a:0:{}i:5;i:7208992;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.17235;i:4;a:0:{}i:5;i:7214208;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172428;i:4;a:0:{}i:5;i:7215432;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172579;i:4;a:0:{}i:5;i:7216680;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172724;i:4;a:0:{}i:5;i:7217640;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.173053;i:4;a:0:{}i:5;i:7219272;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.173096;i:4;a:0:{}i:5;i:7216696;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176193;i:4;a:0:{}i:5;i:7225840;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.17628;i:4;a:0:{}i:5;i:7227080;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176446;i:4;a:0:{}i:5;i:7228640;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176566;i:4;a:0:{}i:5;i:7227128;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176872;i:4;a:0:{}i:5;i:7228760;}i:117;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176909;i:4;a:0:{}i:5;i:7225928;}i:118;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180195;i:4;a:0:{}i:5;i:7242328;}i:120;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180257;i:4;a:0:{}i:5;i:7243552;}i:121;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180432;i:4;a:0:{}i:5;i:7246016;}i:123;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180587;i:4;a:0:{}i:5;i:7239792;}i:124;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180888;i:4;a:0:{}i:5;i:7243320;}i:126;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180942;i:4;a:0:{}i:5;i:7240504;}i:127;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.184813;i:4;a:0:{}i:5;i:7248640;}i:129;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.184901;i:4;a:0:{}i:5;i:7253984;}i:130;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185082;i:4;a:0:{}i:5;i:7255552;}i:132;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185207;i:4;a:0:{}i:5;i:7254544;}i:133;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185568;i:4;a:0:{}i:5;i:7256176;}i:135;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185609;i:4;a:0:{}i:5;i:7253304;}i:136;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.189983;i:4;a:0:{}i:5;i:7266728;}i:138;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190092;i:4;a:0:{}i:5;i:7267952;}i:139;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190325;i:4;a:0:{}i:5;i:7269768;}i:141;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190479;i:4;a:0:{}i:5;i:7265704;}i:142;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190861;i:4;a:0:{}i:5;i:7267336;}i:144;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190907;i:4;a:0:{}i:5;i:7264440;}i:145;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19447;i:4;a:0:{}i:5;i:7272608;}i:147;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194563;i:4;a:0:{}i:5;i:7273848;}i:148;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19475;i:4;a:0:{}i:5;i:7275408;}i:150;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194914;i:4;a:0:{}i:5;i:7274432;}i:151;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.195276;i:4;a:0:{}i:5;i:7276896;}i:153;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19532;i:4;a:0:{}i:5;i:7274080;}i:154;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198582;i:4;a:0:{}i:5;i:7286544;}i:156;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198644;i:4;a:0:{}i:5;i:7287768;}i:157;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198799;i:4;a:0:{}i:5;i:7289592;}i:159;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198935;i:4;a:0:{}i:5;i:7286040;}i:160;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19924;i:4;a:0:{}i:5;i:7288520;}i:162;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.199283;i:4;a:0:{}i:5;i:7286384;}i:163;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202446;i:4;a:0:{}i:5;i:7302800;}i:165;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202511;i:4;a:0:{}i:5;i:7304024;}i:166;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202689;i:4;a:0:{}i:5;i:7305856;}i:168;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202875;i:4;a:0:{}i:5;i:7300176;}i:169;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.203225;i:4;a:0:{}i:5;i:7301808;}i:171;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.203267;i:4;a:0:{}i:5;i:7298976;}i:172;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206588;i:4;a:0:{}i:5;i:7308120;}i:174;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.20668;i:4;a:0:{}i:5;i:7309384;}i:175;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206856;i:4;a:0:{}i:5;i:7310832;}i:177;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206991;i:4;a:0:{}i:5;i:7309360;}i:178;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.207378;i:4;a:0:{}i:5;i:7310992;}i:180;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.20743;i:4;a:0:{}i:5;i:7308760;}i:181;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.210794;i:4;a:0:{}i:5;i:7316976;}i:183;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.210925;i:4;a:0:{}i:5;i:7318200;}i:184;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211184;i:4;a:0:{}i:5;i:7319768;}i:186;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211318;i:4;a:0:{}i:5;i:7318704;}i:187;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211801;i:4;a:0:{}i:5;i:7320336;}i:189;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211855;i:4;a:0:{}i:5;i:7317464;}i:190;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217231;i:4;a:0:{}i:5;i:7320784;}i:192;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217456;i:4;a:0:{}i:5;i:7322008;}i:193;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217786;i:4;a:0:{}i:5;i:7323160;}i:195;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217911;i:4;a:0:{}i:5;i:7325272;}i:196;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.218292;i:4;a:0:{}i:5;i:7326904;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.21834;i:4;a:0:{}i:5;i:7324032;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.223589;i:4;a:0:{}i:5;i:7335504;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.223735;i:4;a:0:{}i:5;i:7336752;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224012;i:4;a:0:{}i:5;i:7338320;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224236;i:4;a:0:{}i:5;i:7335624;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224689;i:4;a:0:{}i:5;i:7337256;}i:207;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224747;i:4;a:0:{}i:5;i:7334384;}i:208;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228628;i:4;a:0:{}i:5;i:7343520;}i:210;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228728;i:4;a:0:{}i:5;i:7344768;}i:211;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228907;i:4;a:0:{}i:5;i:7346336;}i:213;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229033;i:4;a:0:{}i:5;i:7344808;}i:214;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229377;i:4;a:0:{}i:5;i:7346440;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229415;i:4;a:0:{}i:5;i:7343608;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233625;i:4;a:0:{}i:5;i:7352768;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233695;i:4;a:0:{}i:5;i:7354032;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233855;i:4;a:0:{}i:5;i:7355608;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233977;i:4;a:0:{}i:5;i:7354008;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.234299;i:4;a:0:{}i:5;i:7355640;}i:225;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.234337;i:4;a:0:{}i:5;i:7352808;}i:226;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.237622;i:4;a:0:{}i:5;i:7361048;}i:228;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.237737;i:4;a:0:{}i:5;i:7362312;}i:229;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.23799;i:4;a:0:{}i:5;i:7363760;}i:231;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238172;i:4;a:0:{}i:5;i:7362776;}i:232;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238537;i:4;a:0:{}i:5;i:7364408;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238581;i:4;a:0:{}i:5;i:7361576;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242298;i:4;a:0:{}i:5;i:7369728;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242386;i:4;a:0:{}i:5;i:7370952;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242572;i:4;a:0:{}i:5;i:7372528;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242697;i:4;a:0:{}i:5;i:7371520;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.243036;i:4;a:0:{}i:5;i:7374008;}i:243;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24308;i:4;a:0:{}i:5;i:7371232;}i:244;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24647;i:4;a:0:{}i:5;i:7383680;}i:246;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246547;i:4;a:0:{}i:5;i:7384904;}i:247;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246728;i:4;a:0:{}i:5;i:7386608;}i:249;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246868;i:4;a:0:{}i:5;i:7383240;}i:250;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.247202;i:4;a:0:{}i:5;i:7384872;}i:252;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24724;i:4;a:0:{}i:5;i:7382040;}i:253;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250565;i:4;a:0:{}i:5;i:7395544;}i:255;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250636;i:4;a:0:{}i:5;i:7396768;}i:256;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.25081;i:4;a:0:{}i:5;i:7398472;}i:258;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250955;i:4;a:0:{}i:5;i:7402648;}i:259;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.251289;i:4;a:0:{}i:5;i:7404280;}i:261;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.251333;i:4;a:0:{}i:5;i:7401408;}i:262;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.254807;i:4;a:0:{}i:5;i:7412912;}i:264;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.254879;i:4;a:0:{}i:5;i:7414136;}i:265;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255067;i:4;a:0:{}i:5;i:7415704;}i:267;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255218;i:4;a:0:{}i:5;i:7412984;}i:268;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255555;i:4;a:0:{}i:5;i:7414616;}i:270;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255596;i:4;a:0:{}i:5;i:7411720;}i:271;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259133;i:4;a:0:{}i:5;i:7423144;}i:273;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259206;i:4;a:0:{}i:5;i:7424384;}i:274;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259391;i:4;a:0:{}i:5;i:7426072;}i:276;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259543;i:4;a:0:{}i:5;i:7423328;}i:277;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259874;i:4;a:0:{}i:5;i:7424960;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259919;i:4;a:0:{}i:5;i:7422088;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263244;i:4;a:0:{}i:5;i:7429312;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263309;i:4;a:0:{}i:5;i:7430560;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263462;i:4;a:0:{}i:5;i:7432000;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263607;i:4;a:0:{}i:5;i:7431616;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263929;i:4;a:0:{}i:5;i:7433248;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263972;i:4;a:0:{}i:5;i:7430416;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267481;i:4;a:0:{}i:5;i:7441936;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267555;i:4;a:0:{}i:5;i:7443160;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267739;i:4;a:0:{}i:5;i:7444864;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267922;i:4;a:0:{}i:5;i:7441944;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.268284;i:4;a:0:{}i:5;i:7443576;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.268326;i:4;a:0:{}i:5;i:7440680;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272275;i:4;a:0:{}i:5;i:7468952;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272353;i:4;a:0:{}i:5;i:7470192;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272583;i:4;a:0:{}i:5;i:7473160;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272798;i:4;a:0:{}i:5;i:7459936;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.273173;i:4;a:0:{}i:5;i:7461568;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.273221;i:4;a:0:{}i:5;i:7460016;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.276752;i:4;a:0:{}i:5;i:7469104;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.27682;i:4;a:0:{}i:5;i:7470328;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277018;i:4;a:0:{}i:5;i:7472288;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277161;i:4;a:0:{}i:5;i:7470376;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277524;i:4;a:0:{}i:5;i:7473952;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277573;i:4;a:0:{}i:5;i:7472456;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.280896;i:4;a:0:{}i:5;i:7480632;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.280949;i:4;a:0:{}i:5;i:7481856;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281102;i:4;a:0:{}i:5;i:7483688;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281229;i:4;a:0:{}i:5;i:7482384;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.28153;i:4;a:0:{}i:5;i:7484864;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281576;i:4;a:0:{}i:5;i:7482088;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.284656;i:4;a:0:{}i:5;i:7491168;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.28472;i:4;a:0:{}i:5;i:7492392;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.284897;i:4;a:0:{}i:5;i:7494352;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285038;i:4;a:0:{}i:5;i:7492440;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285392;i:4;a:0:{}i:5;i:7496000;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285441;i:4;a:0:{}i:5;i:7493184;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.288933;i:4;a:0:{}i:5;i:7503640;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289025;i:4;a:0:{}i:5;i:7504888;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289244;i:4;a:0:{}i:5;i:7506712;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289399;i:4;a:0:{}i:5;i:7504320;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289788;i:4;a:0:{}i:5;i:7506792;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289833;i:4;a:0:{}i:5;i:7504016;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293257;i:4;a:0:{}i:5;i:7512152;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293323;i:4;a:0:{}i:5;i:7513416;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293504;i:4;a:0:{}i:5;i:7515248;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293621;i:4;a:0:{}i:5;i:7513968;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.29396;i:4;a:0:{}i:5;i:7517008;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.294;i:4;a:0:{}i:5;i:7514232;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297054;i:4;a:0:{}i:5;i:7522368;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297119;i:4;a:0:{}i:5;i:7523592;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297286;i:4;a:0:{}i:5;i:7525160;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297402;i:4;a:0:{}i:5;i:7524168;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297732;i:4;a:0:{}i:5;i:7526640;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297776;i:4;a:0:{}i:5;i:7523824;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301101;i:4;a:0:{}i:5;i:7532896;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301153;i:4;a:0:{}i:5;i:7534144;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301326;i:4;a:0:{}i:5;i:7536096;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301446;i:4;a:0:{}i:5;i:7534192;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301773;i:4;a:0:{}i:5;i:7537744;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301813;i:4;a:0:{}i:5;i:7534928;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305283;i:4;a:0:{}i:5;i:7543040;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305368;i:4;a:0:{}i:5;i:7544288;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.30556;i:4;a:0:{}i:5;i:7545984;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305682;i:4;a:0:{}i:5;i:7544864;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.30604;i:4;a:0:{}i:5;i:7547872;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.306086;i:4;a:0:{}i:5;i:7545096;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309613;i:4;a:0:{}i:5;i:7555440;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309685;i:4;a:0:{}i:5;i:7556664;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309869;i:4;a:0:{}i:5;i:7558880;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309998;i:4;a:0:{}i:5;i:7556184;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.310361;i:4;a:0:{}i:5;i:7559728;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3104;i:4;a:0:{}i:5;i:7556960;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313499;i:4;a:0:{}i:5;i:7575768;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313558;i:4;a:0:{}i:5;i:7577024;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313803;i:4;a:0:{}i:5;i:7581040;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313956;i:4;a:0:{}i:5;i:7572232;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.314377;i:4;a:0:{}i:5;i:7580152;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.314419;i:4;a:0:{}i:5;i:7577312;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.317766;i:4;a:0:{}i:5;i:7591824;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.317836;i:4;a:0:{}i:5;i:7593064;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.31806;i:4;a:0:{}i:5;i:7595264;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318226;i:4;a:0:{}i:5;i:7590280;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318589;i:4;a:0:{}i:5;i:7592744;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318634;i:4;a:0:{}i:5;i:7589968;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322044;i:4;a:0:{}i:5;i:7607232;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322114;i:4;a:0:{}i:5;i:7608456;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322286;i:4;a:0:{}i:5;i:7610928;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322482;i:4;a:0:{}i:5;i:7604520;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322859;i:4;a:0:{}i:5;i:7608072;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.323996;i:4;a:0:{}i:5;i:7691904;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327498;i:4;a:0:{}i:5;i:7732808;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327801;i:4;a:0:{}i:5;i:7699752;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327992;i:4;a:0:{}i:5;i:7701664;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328532;i:4;a:0:{}i:5;i:7702840;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328675;i:4;a:0:{}i:5;i:7705312;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328777;i:4;a:0:{}i:5;i:7705528;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328899;i:4;a:0:{}i:5;i:7708000;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328996;i:4;a:0:{}i:5;i:7707840;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329112;i:4;a:0:{}i:5;i:7710312;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3292;i:4;a:0:{}i:5;i:7710152;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329305;i:4;a:0:{}i:5;i:7712624;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329371;i:4;a:0:{}i:5;i:7711912;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329471;i:4;a:0:{}i:5;i:7714384;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329545;i:4;a:0:{}i:5;i:7713968;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329648;i:4;a:0:{}i:5;i:7715664;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329742;i:4;a:0:{}i:5;i:7716976;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329836;i:4;a:0:{}i:5;i:7718352;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329926;i:4;a:0:{}i:5;i:7719600;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330022;i:4;a:0:{}i:5;i:7721168;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330127;i:4;a:0:{}i:5;i:7722936;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330233;i:4;a:0:{}i:5;i:7724376;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330339;i:4;a:0:{}i:5;i:7725168;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330437;i:4;a:0:{}i:5;i:7726608;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330511;i:4;a:0:{}i:5;i:7726880;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330605;i:4;a:0:{}i:5;i:7728320;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330764;i:4;a:0:{}i:5;i:7730200;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330954;i:4;a:0:{}i:5;i:7732664;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331041;i:4;a:0:{}i:5;i:7732832;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331152;i:4;a:0:{}i:5;i:7735296;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33125;i:4;a:0:{}i:5;i:7735432;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33137;i:4;a:0:{}i:5;i:7737896;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331456;i:4;a:0:{}i:5;i:7737528;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331585;i:4;a:0:{}i:5;i:7739992;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331685;i:4;a:0:{}i:5;i:7740320;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331793;i:4;a:0:{}i:5;i:7741880;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331899;i:4;a:0:{}i:5;i:7742936;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332003;i:4;a:0:{}i:5;i:7744760;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33216;i:4;a:0:{}i:5;i:7745880;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33231;i:4;a:0:{}i:5;i:7747456;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332451;i:4;a:0:{}i:5;i:7749656;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332601;i:4;a:0:{}i:5;i:7752120;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332685;i:4;a:0:{}i:5;i:7751864;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332804;i:4;a:0:{}i:5;i:7754328;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332896;i:4;a:0:{}i:5;i:7754088;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333042;i:4;a:0:{}i:5;i:7756552;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333113;i:4;a:0:{}i:5;i:7756184;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333213;i:4;a:0:{}i:5;i:7758648;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333297;i:4;a:0:{}i:5;i:7759672;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333414;i:4;a:0:{}i:5;i:7761632;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333506;i:4;a:0:{}i:5;i:7761936;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333666;i:4;a:0:{}i:5;i:7763896;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333758;i:4;a:0:{}i:5;i:7764536;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333874;i:4;a:0:{}i:5;i:7766496;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33395;i:4;a:0:{}i:5;i:7766264;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334072;i:4;a:0:{}i:5;i:7768224;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334151;i:4;a:0:{}i:5;i:7768384;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334277;i:4;a:0:{}i:5;i:7770216;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334373;i:4;a:0:{}i:5;i:7771040;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334505;i:4;a:0:{}i:5;i:7773000;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334588;i:4;a:0:{}i:5;i:7773272;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334694;i:4;a:0:{}i:5;i:7775232;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334774;i:4;a:0:{}i:5;i:7775880;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334885;i:4;a:0:{}i:5;i:7777840;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334947;i:4;a:0:{}i:5;i:7777608;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335046;i:4;a:0:{}i:5;i:7779568;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335119;i:4;a:0:{}i:5;i:7796104;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335222;i:4;a:0:{}i:5;i:7797928;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335341;i:4;a:0:{}i:5;i:7798776;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335446;i:4;a:0:{}i:5;i:7800608;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335529;i:4;a:0:{}i:5;i:7801072;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335629;i:4;a:0:{}i:5;i:7802904;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335689;i:4;a:0:{}i:5;i:7803192;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335788;i:4;a:0:{}i:5;i:7805024;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335852;i:4;a:0:{}i:5;i:7805272;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335947;i:4;a:0:{}i:5;i:7806840;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336036;i:4;a:0:{}i:5;i:7808216;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336147;i:4;a:0:{}i:5;i:7810168;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336261;i:4;a:0:{}i:5;i:7810424;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33637;i:4;a:0:{}i:5;i:7812376;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336459;i:4;a:0:{}i:5;i:7812680;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336569;i:4;a:0:{}i:5;i:7814632;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336664;i:4;a:0:{}i:5;i:7814400;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336789;i:4;a:0:{}i:5;i:7816352;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33688;i:4;a:0:{}i:5;i:7816504;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336991;i:4;a:0:{}i:5;i:7818200;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337109;i:4;a:0:{}i:5;i:7818712;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337216;i:4;a:0:{}i:5;i:7820408;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337296;i:4;a:0:{}i:5;i:7820792;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337403;i:4;a:0:{}i:5;i:7822488;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337472;i:4;a:0:{}i:5;i:7822904;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337578;i:4;a:0:{}i:5;i:7825120;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337664;i:4;a:0:{}i:5;i:7825160;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337771;i:4;a:0:{}i:5;i:7827376;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337867;i:4;a:0:{}i:5;i:7829032;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337991;i:4;a:0:{}i:5;i:7831248;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338058;i:4;a:0:{}i:5;i:7830752;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338163;i:4;a:0:{}i:5;i:7832968;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338238;i:4;a:0:{}i:5;i:7832912;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338399;i:4;a:0:{}i:5;i:7836928;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33849;i:4;a:0:{}i:5;i:7835240;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338648;i:4;a:0:{}i:5;i:7839256;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33874;i:4;a:0:{}i:5;i:7837472;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3389;i:4;a:0:{}i:5;i:7841488;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338986;i:4;a:0:{}i:5;i:7840112;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339131;i:4;a:0:{}i:5;i:7844128;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33922;i:4;a:0:{}i:5;i:7842448;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339366;i:4;a:0:{}i:5;i:7846464;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339472;i:4;a:0:{}i:5;i:7844800;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339634;i:4;a:0:{}i:5;i:7848816;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339727;i:4;a:0:{}i:5;i:7847160;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339879;i:4;a:0:{}i:5;i:7851176;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339983;i:4;a:0:{}i:5;i:7849520;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340138;i:4;a:0:{}i:5;i:7853536;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340231;i:4;a:0:{}i:5;i:7852192;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340379;i:4;a:0:{}i:5;i:7856208;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340465;i:4;a:0:{}i:5;i:7854520;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340609;i:4;a:0:{}i:5;i:7858536;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.34067;i:4;a:0:{}i:5;i:7856320;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340814;i:4;a:0:{}i:5;i:7860336;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340896;i:4;a:0:{}i:5;i:7857936;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341017;i:4;a:0:{}i:5;i:7860136;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.34111;i:4;a:0:{}i:5;i:7860576;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341233;i:4;a:0:{}i:5;i:7863048;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341324;i:4;a:0:{}i:5;i:7862808;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341443;i:4;a:0:{}i:5;i:7865280;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341524;i:4;a:0:{}i:5;i:7865040;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341636;i:4;a:0:{}i:5;i:7867512;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341695;i:4;a:0:{}i:5;i:7866768;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3418;i:4;a:0:{}i:5;i:7869240;}i:609;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341962;i:4;a:0:{}i:5;i:7874888;}i:610;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.342077;i:4;a:0:{}i:5;i:7877352;}}}";s:2:"db";s:125645:"a:1:{s:8:"messages";a:398:{i:15;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.119932;i:4;a:0:{}i:5;i:6982216;}i:16;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131446;i:4;a:0:{}i:5;i:6997912;}i:18;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131558;i:4;a:0:{}i:5;i:6999800;}i:19;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.131768;i:4;a:0:{}i:5;i:7002296;}i:21;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.132978;i:4;a:0:{}i:5;i:7020184;}i:22;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.133343;i:4;a:0:{}i:5;i:7023712;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135172;i:4;a:0:{}i:5;i:7112888;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135913;i:4;a:0:{}i:5;i:7117840;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.135969;i:4;a:0:{}i:5;i:7119928;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139709;i:4;a:0:{}i:5;i:7134416;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139789;i:4;a:0:{}i:5;i:7135656;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.139978;i:4;a:0:{}i:5;i:7137472;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140141;i:4;a:0:{}i:5;i:7134080;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140499;i:4;a:0:{}i:5;i:7135712;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.140543;i:4;a:0:{}i:5;i:7133256;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143676;i:4;a:0:{}i:5;i:7144784;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143733;i:4;a:0:{}i:5;i:7146008;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.143891;i:4;a:0:{}i:5;i:7147968;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144024;i:4;a:0:{}i:5;i:7144832;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144322;i:4;a:0:{}i:5;i:7147320;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.144366;i:4;a:0:{}i:5;i:7144504;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147276;i:4;a:0:{}i:5;i:7151776;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147348;i:4;a:0:{}i:5;i:7153000;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147502;i:4;a:0:{}i:5;i:7154312;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147617;i:4;a:0:{}i:5;i:7154040;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147909;i:4;a:0:{}i:5;i:7155672;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.147948;i:4;a:0:{}i:5;i:7152840;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151199;i:4;a:0:{}i:5;i:7166256;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151367;i:4;a:0:{}i:5;i:7167520;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151777;i:4;a:0:{}i:5;i:7169992;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.151919;i:4;a:0:{}i:5;i:7165320;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.152296;i:4;a:0:{}i:5;i:7169528;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.152374;i:4;a:0:{}i:5;i:7166712;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.155874;i:4;a:0:{}i:5;i:7177184;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.15604;i:4;a:0:{}i:5;i:7184040;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.156493;i:4;a:0:{}i:5;i:7185736;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.156648;i:4;a:0:{}i:5;i:7183448;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.157069;i:4;a:0:{}i:5;i:7185928;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.157136;i:4;a:0:{}i:5;i:7183152;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160391;i:4;a:0:{}i:5;i:7187472;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160466;i:4;a:0:{}i:5;i:7188696;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160621;i:4;a:0:{}i:5;i:7190072;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.160715;i:4;a:0:{}i:5;i:7191376;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.161016;i:4;a:0:{}i:5;i:7193864;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.161085;i:4;a:0:{}i:5;i:7191048;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.163786;i:4;a:0:{}i:5;i:7199208;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.163879;i:4;a:0:{}i:5;i:7200432;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164024;i:4;a:0:{}i:5;i:7202000;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164138;i:4;a:0:{}i:5;i:7200976;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164425;i:4;a:0:{}i:5;i:7203464;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.164476;i:4;a:0:{}i:5;i:7201008;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168217;i:4;a:0:{}i:5;i:7204368;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168293;i:4;a:0:{}i:5;i:7205592;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168451;i:4;a:0:{}i:5;i:7207032;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168538;i:4;a:0:{}i:5;i:7208800;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168874;i:4;a:0:{}i:5;i:7211808;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.168919;i:4;a:0:{}i:5;i:7208992;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.17235;i:4;a:0:{}i:5;i:7214208;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172428;i:4;a:0:{}i:5;i:7215432;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172579;i:4;a:0:{}i:5;i:7216680;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.172724;i:4;a:0:{}i:5;i:7217640;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.173053;i:4;a:0:{}i:5;i:7219272;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.173096;i:4;a:0:{}i:5;i:7216696;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176193;i:4;a:0:{}i:5;i:7225840;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.17628;i:4;a:0:{}i:5;i:7227080;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176446;i:4;a:0:{}i:5;i:7228640;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176566;i:4;a:0:{}i:5;i:7227128;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176872;i:4;a:0:{}i:5;i:7228760;}i:117;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.176909;i:4;a:0:{}i:5;i:7225928;}i:118;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180195;i:4;a:0:{}i:5;i:7242328;}i:120;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180257;i:4;a:0:{}i:5;i:7243552;}i:121;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180432;i:4;a:0:{}i:5;i:7246016;}i:123;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180587;i:4;a:0:{}i:5;i:7239792;}i:124;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180888;i:4;a:0:{}i:5;i:7243320;}i:126;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.180942;i:4;a:0:{}i:5;i:7240504;}i:127;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.184813;i:4;a:0:{}i:5;i:7248640;}i:129;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.184901;i:4;a:0:{}i:5;i:7253984;}i:130;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185082;i:4;a:0:{}i:5;i:7255552;}i:132;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185207;i:4;a:0:{}i:5;i:7254544;}i:133;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185568;i:4;a:0:{}i:5;i:7256176;}i:135;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.185609;i:4;a:0:{}i:5;i:7253304;}i:136;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.189983;i:4;a:0:{}i:5;i:7266728;}i:138;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190092;i:4;a:0:{}i:5;i:7267952;}i:139;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190325;i:4;a:0:{}i:5;i:7269768;}i:141;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190479;i:4;a:0:{}i:5;i:7265704;}i:142;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190861;i:4;a:0:{}i:5;i:7267336;}i:144;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.190907;i:4;a:0:{}i:5;i:7264440;}i:145;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19447;i:4;a:0:{}i:5;i:7272608;}i:147;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194563;i:4;a:0:{}i:5;i:7273848;}i:148;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19475;i:4;a:0:{}i:5;i:7275408;}i:150;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.194914;i:4;a:0:{}i:5;i:7274432;}i:151;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.195276;i:4;a:0:{}i:5;i:7276896;}i:153;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19532;i:4;a:0:{}i:5;i:7274080;}i:154;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198582;i:4;a:0:{}i:5;i:7286544;}i:156;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198644;i:4;a:0:{}i:5;i:7287768;}i:157;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198799;i:4;a:0:{}i:5;i:7289592;}i:159;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.198935;i:4;a:0:{}i:5;i:7286040;}i:160;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.19924;i:4;a:0:{}i:5;i:7288520;}i:162;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.199283;i:4;a:0:{}i:5;i:7286384;}i:163;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202446;i:4;a:0:{}i:5;i:7302800;}i:165;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202511;i:4;a:0:{}i:5;i:7304024;}i:166;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202689;i:4;a:0:{}i:5;i:7305856;}i:168;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.202875;i:4;a:0:{}i:5;i:7300176;}i:169;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.203225;i:4;a:0:{}i:5;i:7301808;}i:171;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.203267;i:4;a:0:{}i:5;i:7298976;}i:172;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206588;i:4;a:0:{}i:5;i:7308120;}i:174;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.20668;i:4;a:0:{}i:5;i:7309384;}i:175;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206856;i:4;a:0:{}i:5;i:7310832;}i:177;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.206991;i:4;a:0:{}i:5;i:7309360;}i:178;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.207378;i:4;a:0:{}i:5;i:7310992;}i:180;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.20743;i:4;a:0:{}i:5;i:7308760;}i:181;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.210794;i:4;a:0:{}i:5;i:7316976;}i:183;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.210925;i:4;a:0:{}i:5;i:7318200;}i:184;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211184;i:4;a:0:{}i:5;i:7319768;}i:186;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211318;i:4;a:0:{}i:5;i:7318704;}i:187;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211801;i:4;a:0:{}i:5;i:7320336;}i:189;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.211855;i:4;a:0:{}i:5;i:7317464;}i:190;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217231;i:4;a:0:{}i:5;i:7320784;}i:192;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217456;i:4;a:0:{}i:5;i:7322008;}i:193;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217786;i:4;a:0:{}i:5;i:7323160;}i:195;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.217911;i:4;a:0:{}i:5;i:7325272;}i:196;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.218292;i:4;a:0:{}i:5;i:7326904;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.21834;i:4;a:0:{}i:5;i:7324032;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.223589;i:4;a:0:{}i:5;i:7335504;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.223735;i:4;a:0:{}i:5;i:7336752;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224012;i:4;a:0:{}i:5;i:7338320;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224236;i:4;a:0:{}i:5;i:7335624;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224689;i:4;a:0:{}i:5;i:7337256;}i:207;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.224747;i:4;a:0:{}i:5;i:7334384;}i:208;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228628;i:4;a:0:{}i:5;i:7343520;}i:210;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228728;i:4;a:0:{}i:5;i:7344768;}i:211;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.228907;i:4;a:0:{}i:5;i:7346336;}i:213;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229033;i:4;a:0:{}i:5;i:7344808;}i:214;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229377;i:4;a:0:{}i:5;i:7346440;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.229415;i:4;a:0:{}i:5;i:7343608;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233625;i:4;a:0:{}i:5;i:7352768;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233695;i:4;a:0:{}i:5;i:7354032;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233855;i:4;a:0:{}i:5;i:7355608;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.233977;i:4;a:0:{}i:5;i:7354008;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.234299;i:4;a:0:{}i:5;i:7355640;}i:225;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.234337;i:4;a:0:{}i:5;i:7352808;}i:226;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.237622;i:4;a:0:{}i:5;i:7361048;}i:228;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.237737;i:4;a:0:{}i:5;i:7362312;}i:229;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.23799;i:4;a:0:{}i:5;i:7363760;}i:231;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238172;i:4;a:0:{}i:5;i:7362776;}i:232;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238537;i:4;a:0:{}i:5;i:7364408;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.238581;i:4;a:0:{}i:5;i:7361576;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242298;i:4;a:0:{}i:5;i:7369728;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242386;i:4;a:0:{}i:5;i:7370952;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242572;i:4;a:0:{}i:5;i:7372528;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.242697;i:4;a:0:{}i:5;i:7371520;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.243036;i:4;a:0:{}i:5;i:7374008;}i:243;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24308;i:4;a:0:{}i:5;i:7371232;}i:244;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24647;i:4;a:0:{}i:5;i:7383680;}i:246;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246547;i:4;a:0:{}i:5;i:7384904;}i:247;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246728;i:4;a:0:{}i:5;i:7386608;}i:249;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.246868;i:4;a:0:{}i:5;i:7383240;}i:250;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.247202;i:4;a:0:{}i:5;i:7384872;}i:252;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.24724;i:4;a:0:{}i:5;i:7382040;}i:253;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250565;i:4;a:0:{}i:5;i:7395544;}i:255;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250636;i:4;a:0:{}i:5;i:7396768;}i:256;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.25081;i:4;a:0:{}i:5;i:7398472;}i:258;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.250955;i:4;a:0:{}i:5;i:7402648;}i:259;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.251289;i:4;a:0:{}i:5;i:7404280;}i:261;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.251333;i:4;a:0:{}i:5;i:7401408;}i:262;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.254807;i:4;a:0:{}i:5;i:7412912;}i:264;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.254879;i:4;a:0:{}i:5;i:7414136;}i:265;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255067;i:4;a:0:{}i:5;i:7415704;}i:267;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255218;i:4;a:0:{}i:5;i:7412984;}i:268;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255555;i:4;a:0:{}i:5;i:7414616;}i:270;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.255596;i:4;a:0:{}i:5;i:7411720;}i:271;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259133;i:4;a:0:{}i:5;i:7423144;}i:273;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259206;i:4;a:0:{}i:5;i:7424384;}i:274;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259391;i:4;a:0:{}i:5;i:7426072;}i:276;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259543;i:4;a:0:{}i:5;i:7423328;}i:277;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259874;i:4;a:0:{}i:5;i:7424960;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.259919;i:4;a:0:{}i:5;i:7422088;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263244;i:4;a:0:{}i:5;i:7429312;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263309;i:4;a:0:{}i:5;i:7430560;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263462;i:4;a:0:{}i:5;i:7432000;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263607;i:4;a:0:{}i:5;i:7431616;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263929;i:4;a:0:{}i:5;i:7433248;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.263972;i:4;a:0:{}i:5;i:7430416;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267481;i:4;a:0:{}i:5;i:7441936;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267555;i:4;a:0:{}i:5;i:7443160;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267739;i:4;a:0:{}i:5;i:7444864;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.267922;i:4;a:0:{}i:5;i:7441944;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.268284;i:4;a:0:{}i:5;i:7443576;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.268326;i:4;a:0:{}i:5;i:7440680;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272275;i:4;a:0:{}i:5;i:7468952;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272353;i:4;a:0:{}i:5;i:7470192;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272583;i:4;a:0:{}i:5;i:7473160;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.272798;i:4;a:0:{}i:5;i:7459936;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.273173;i:4;a:0:{}i:5;i:7461568;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.273221;i:4;a:0:{}i:5;i:7460016;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.276752;i:4;a:0:{}i:5;i:7469104;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.27682;i:4;a:0:{}i:5;i:7470328;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277018;i:4;a:0:{}i:5;i:7472288;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277161;i:4;a:0:{}i:5;i:7470376;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277524;i:4;a:0:{}i:5;i:7473952;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.277573;i:4;a:0:{}i:5;i:7472456;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.280896;i:4;a:0:{}i:5;i:7480632;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.280949;i:4;a:0:{}i:5;i:7481856;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281102;i:4;a:0:{}i:5;i:7483688;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281229;i:4;a:0:{}i:5;i:7482384;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.28153;i:4;a:0:{}i:5;i:7484864;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.281576;i:4;a:0:{}i:5;i:7482088;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.284656;i:4;a:0:{}i:5;i:7491168;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.28472;i:4;a:0:{}i:5;i:7492392;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.284897;i:4;a:0:{}i:5;i:7494352;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285038;i:4;a:0:{}i:5;i:7492440;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285392;i:4;a:0:{}i:5;i:7496000;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.285441;i:4;a:0:{}i:5;i:7493184;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.288933;i:4;a:0:{}i:5;i:7503640;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289025;i:4;a:0:{}i:5;i:7504888;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289244;i:4;a:0:{}i:5;i:7506712;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289399;i:4;a:0:{}i:5;i:7504320;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289788;i:4;a:0:{}i:5;i:7506792;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.289833;i:4;a:0:{}i:5;i:7504016;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293257;i:4;a:0:{}i:5;i:7512152;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293323;i:4;a:0:{}i:5;i:7513416;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293504;i:4;a:0:{}i:5;i:7515248;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.293621;i:4;a:0:{}i:5;i:7513968;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.29396;i:4;a:0:{}i:5;i:7517008;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.294;i:4;a:0:{}i:5;i:7514232;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297054;i:4;a:0:{}i:5;i:7522368;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297119;i:4;a:0:{}i:5;i:7523592;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297286;i:4;a:0:{}i:5;i:7525160;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297402;i:4;a:0:{}i:5;i:7524168;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297732;i:4;a:0:{}i:5;i:7526640;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.297776;i:4;a:0:{}i:5;i:7523824;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301101;i:4;a:0:{}i:5;i:7532896;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301153;i:4;a:0:{}i:5;i:7534144;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301326;i:4;a:0:{}i:5;i:7536096;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301446;i:4;a:0:{}i:5;i:7534192;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301773;i:4;a:0:{}i:5;i:7537744;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.301813;i:4;a:0:{}i:5;i:7534928;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305283;i:4;a:0:{}i:5;i:7543040;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305368;i:4;a:0:{}i:5;i:7544288;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.30556;i:4;a:0:{}i:5;i:7545984;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.305682;i:4;a:0:{}i:5;i:7544864;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.30604;i:4;a:0:{}i:5;i:7547872;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.306086;i:4;a:0:{}i:5;i:7545096;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309613;i:4;a:0:{}i:5;i:7555440;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309685;i:4;a:0:{}i:5;i:7556664;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309869;i:4;a:0:{}i:5;i:7558880;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.309998;i:4;a:0:{}i:5;i:7556184;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.310361;i:4;a:0:{}i:5;i:7559728;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3104;i:4;a:0:{}i:5;i:7556960;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313499;i:4;a:0:{}i:5;i:7575768;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313558;i:4;a:0:{}i:5;i:7577024;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313803;i:4;a:0:{}i:5;i:7581040;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.313956;i:4;a:0:{}i:5;i:7572232;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.314377;i:4;a:0:{}i:5;i:7580152;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.314419;i:4;a:0:{}i:5;i:7577312;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.317766;i:4;a:0:{}i:5;i:7591824;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.317836;i:4;a:0:{}i:5;i:7593064;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.31806;i:4;a:0:{}i:5;i:7595264;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318226;i:4;a:0:{}i:5;i:7590280;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318589;i:4;a:0:{}i:5;i:7592744;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.318634;i:4;a:0:{}i:5;i:7589968;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322044;i:4;a:0:{}i:5;i:7607232;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322114;i:4;a:0:{}i:5;i:7608456;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322286;i:4;a:0:{}i:5;i:7610928;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322482;i:4;a:0:{}i:5;i:7604520;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.322859;i:4;a:0:{}i:5;i:7608072;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.323996;i:4;a:0:{}i:5;i:7691904;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327498;i:4;a:0:{}i:5;i:7732808;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327801;i:4;a:0:{}i:5;i:7699752;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.327992;i:4;a:0:{}i:5;i:7701664;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328532;i:4;a:0:{}i:5;i:7702840;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328675;i:4;a:0:{}i:5;i:7705312;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328777;i:4;a:0:{}i:5;i:7705528;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328899;i:4;a:0:{}i:5;i:7708000;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.328996;i:4;a:0:{}i:5;i:7707840;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329112;i:4;a:0:{}i:5;i:7710312;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3292;i:4;a:0:{}i:5;i:7710152;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329305;i:4;a:0:{}i:5;i:7712624;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329371;i:4;a:0:{}i:5;i:7711912;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329471;i:4;a:0:{}i:5;i:7714384;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329545;i:4;a:0:{}i:5;i:7713968;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329648;i:4;a:0:{}i:5;i:7715664;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329742;i:4;a:0:{}i:5;i:7716976;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329836;i:4;a:0:{}i:5;i:7718352;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.329926;i:4;a:0:{}i:5;i:7719600;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330022;i:4;a:0:{}i:5;i:7721168;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330127;i:4;a:0:{}i:5;i:7722936;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330233;i:4;a:0:{}i:5;i:7724376;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330339;i:4;a:0:{}i:5;i:7725168;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330437;i:4;a:0:{}i:5;i:7726608;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330511;i:4;a:0:{}i:5;i:7726880;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330605;i:4;a:0:{}i:5;i:7728320;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330764;i:4;a:0:{}i:5;i:7730200;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.330954;i:4;a:0:{}i:5;i:7732664;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331041;i:4;a:0:{}i:5;i:7732832;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331152;i:4;a:0:{}i:5;i:7735296;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33125;i:4;a:0:{}i:5;i:7735432;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33137;i:4;a:0:{}i:5;i:7737896;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331456;i:4;a:0:{}i:5;i:7737528;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331585;i:4;a:0:{}i:5;i:7739992;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331685;i:4;a:0:{}i:5;i:7740320;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331793;i:4;a:0:{}i:5;i:7741880;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.331899;i:4;a:0:{}i:5;i:7742936;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332003;i:4;a:0:{}i:5;i:7744760;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33216;i:4;a:0:{}i:5;i:7745880;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33231;i:4;a:0:{}i:5;i:7747456;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332451;i:4;a:0:{}i:5;i:7749656;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332601;i:4;a:0:{}i:5;i:7752120;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332685;i:4;a:0:{}i:5;i:7751864;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332804;i:4;a:0:{}i:5;i:7754328;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.332896;i:4;a:0:{}i:5;i:7754088;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333042;i:4;a:0:{}i:5;i:7756552;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333113;i:4;a:0:{}i:5;i:7756184;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333213;i:4;a:0:{}i:5;i:7758648;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333297;i:4;a:0:{}i:5;i:7759672;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333414;i:4;a:0:{}i:5;i:7761632;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333506;i:4;a:0:{}i:5;i:7761936;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333666;i:4;a:0:{}i:5;i:7763896;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333758;i:4;a:0:{}i:5;i:7764536;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.333874;i:4;a:0:{}i:5;i:7766496;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33395;i:4;a:0:{}i:5;i:7766264;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334072;i:4;a:0:{}i:5;i:7768224;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334151;i:4;a:0:{}i:5;i:7768384;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334277;i:4;a:0:{}i:5;i:7770216;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334373;i:4;a:0:{}i:5;i:7771040;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334505;i:4;a:0:{}i:5;i:7773000;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334588;i:4;a:0:{}i:5;i:7773272;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334694;i:4;a:0:{}i:5;i:7775232;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334774;i:4;a:0:{}i:5;i:7775880;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334885;i:4;a:0:{}i:5;i:7777840;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.334947;i:4;a:0:{}i:5;i:7777608;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335046;i:4;a:0:{}i:5;i:7779568;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335119;i:4;a:0:{}i:5;i:7796104;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335222;i:4;a:0:{}i:5;i:7797928;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335341;i:4;a:0:{}i:5;i:7798776;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335446;i:4;a:0:{}i:5;i:7800608;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335529;i:4;a:0:{}i:5;i:7801072;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335629;i:4;a:0:{}i:5;i:7802904;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335689;i:4;a:0:{}i:5;i:7803192;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335788;i:4;a:0:{}i:5;i:7805024;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335852;i:4;a:0:{}i:5;i:7805272;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.335947;i:4;a:0:{}i:5;i:7806840;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336036;i:4;a:0:{}i:5;i:7808216;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336147;i:4;a:0:{}i:5;i:7810168;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336261;i:4;a:0:{}i:5;i:7810424;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33637;i:4;a:0:{}i:5;i:7812376;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336459;i:4;a:0:{}i:5;i:7812680;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336569;i:4;a:0:{}i:5;i:7814632;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336664;i:4;a:0:{}i:5;i:7814400;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336789;i:4;a:0:{}i:5;i:7816352;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33688;i:4;a:0:{}i:5;i:7816504;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.336991;i:4;a:0:{}i:5;i:7818200;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337109;i:4;a:0:{}i:5;i:7818712;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337216;i:4;a:0:{}i:5;i:7820408;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337296;i:4;a:0:{}i:5;i:7820792;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337403;i:4;a:0:{}i:5;i:7822488;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337472;i:4;a:0:{}i:5;i:7822904;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337578;i:4;a:0:{}i:5;i:7825120;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337664;i:4;a:0:{}i:5;i:7825160;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337771;i:4;a:0:{}i:5;i:7827376;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337867;i:4;a:0:{}i:5;i:7829032;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.337991;i:4;a:0:{}i:5;i:7831248;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338058;i:4;a:0:{}i:5;i:7830752;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338163;i:4;a:0:{}i:5;i:7832968;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338238;i:4;a:0:{}i:5;i:7832912;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338399;i:4;a:0:{}i:5;i:7836928;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33849;i:4;a:0:{}i:5;i:7835240;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338648;i:4;a:0:{}i:5;i:7839256;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33874;i:4;a:0:{}i:5;i:7837472;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3389;i:4;a:0:{}i:5;i:7841488;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.338986;i:4;a:0:{}i:5;i:7840112;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339131;i:4;a:0:{}i:5;i:7844128;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.33922;i:4;a:0:{}i:5;i:7842448;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339366;i:4;a:0:{}i:5;i:7846464;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339472;i:4;a:0:{}i:5;i:7844800;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339634;i:4;a:0:{}i:5;i:7848816;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339727;i:4;a:0:{}i:5;i:7847160;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339879;i:4;a:0:{}i:5;i:7851176;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.339983;i:4;a:0:{}i:5;i:7849520;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340138;i:4;a:0:{}i:5;i:7853536;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340231;i:4;a:0:{}i:5;i:7852192;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340379;i:4;a:0:{}i:5;i:7856208;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340465;i:4;a:0:{}i:5;i:7854520;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340609;i:4;a:0:{}i:5;i:7858536;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.34067;i:4;a:0:{}i:5;i:7856320;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340814;i:4;a:0:{}i:5;i:7860336;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.340896;i:4;a:0:{}i:5;i:7857936;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341017;i:4;a:0:{}i:5;i:7860136;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.34111;i:4;a:0:{}i:5;i:7860576;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341233;i:4;a:0:{}i:5;i:7863048;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341324;i:4;a:0:{}i:5;i:7862808;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341443;i:4;a:0:{}i:5;i:7865280;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341524;i:4;a:0:{}i:5;i:7865040;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341636;i:4;a:0:{}i:5;i:7867512;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341695;i:4;a:0:{}i:5;i:7866768;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.3418;i:4;a:0:{}i:5;i:7869240;}i:609;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.341962;i:4;a:0:{}i:5;i:7874888;}i:610;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532821.342077;i:4;a:0:{}i:5;i:7877352;}}}";s:5:"event";s:7019:"a:40:{i:0;a:5:{s:4:"time";d:1761532821.070051;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1761532821.076835;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1761532821.076847;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:3;a:5:{s:4:"time";d:1761532821.083373;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:4;a:5:{s:4:"time";d:1761532821.093562;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:5;a:5:{s:4:"time";d:1761532821.119876;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1761532821.134513;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:7;a:5:{s:4:"time";d:1761532821.323875;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\db\ActiveRecord";}i:8;a:5:{s:4:"time";d:1761532821.344145;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:9;a:5:{s:4:"time";d:1761532821.344399;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:10;a:5:{s:4:"time";d:1761532821.344964;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:11;a:5:{s:4:"time";d:1761532821.349066;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:12;a:5:{s:4:"time";d:1761532821.349208;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:13;a:5:{s:4:"time";d:1761532821.358213;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:14;a:5:{s:4:"time";d:1761532821.358947;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:15;a:5:{s:4:"time";d:1761532821.35935;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:16;a:5:{s:4:"time";d:1761532821.359626;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:17;a:5:{s:4:"time";d:1761532821.36006;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:18;a:5:{s:4:"time";d:1761532821.360097;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1761532821.360293;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1761532821.360666;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:21;a:5:{s:4:"time";d:1761532821.360823;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:22;a:5:{s:4:"time";d:1761532821.36123;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:1761532821.362051;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:1761532821.362088;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:1761532821.362565;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:26;a:5:{s:4:"time";d:1761532821.362572;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:27;a:5:{s:4:"time";d:1761532821.362631;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:28;a:5:{s:4:"time";d:1761532821.363491;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1761532821.363584;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:1761532821.363795;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:1761532821.363804;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:32;a:5:{s:4:"time";d:1761532821.363831;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1761532821.36384;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:34;a:5:{s:4:"time";d:1761532821.363845;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:35;a:5:{s:4:"time";d:1761532821.36385;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:36;a:5:{s:4:"time";d:1761532821.363857;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:37;a:5:{s:4:"time";d:1761532821.363862;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:38;a:5:{s:4:"time";d:1761532821.364304;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:39;a:5:{s:4:"time";d:1761532821.364459;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1761532821.043524;s:3:"end";d:1761532821.366659;s:6:"memory";i:9004528;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:650:"a:3:{s:8:"messages";a:3:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532821.074325;i:4;a:0:{}i:5;i:4744128;}i:6;a:6:{i:0;s:42:"Request parsed with URL rule: gii/<id:\w+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1761532821.074368;i:4;a:0:{}i:5;i:4745816;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532821.074374;i:4;a:0:{}i:5;i:4746120;}}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";}";s:7:"request";s:9117:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:4:"host";s:9:"localhost";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"848";s:13:"cache-control";s:9:"max-age=0";s:9:"sec-ch-ua";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:6:"origin";s:16:"http://localhost";s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:14:"sec-fetch-site";s:11:"same-origin";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-dest";s:8:"document";s:7:"referer";s:39:"http://localhost/aleko-bo/web/gii/model";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:6:"cookie";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/7.4.33";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68fedb950f5ba";s:16:"X-Debug-Duration";s:3:"322";s:12:"X-Debug-Link";s:50:"/aleko-bo/web/debug/default/view?tag=68fedb950f5ba";}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";s:12:"actionParams";a:1:{s:2:"id";s:5:"model";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:848:"_csrf=hSyQXAUwv3ZS1TVdWX94wVTMOhjTZ0bdUoQBOAKEYgDzYvQIU1_pNRyyZQQAJhWSYPp7d4QdGe8gtHhBdckBdg%3D%3D&Generator%5BtableName%5D=stage_purchase&Generator%5BmodelClass%5D=StagePurchase&Generator%5BstandardizeCapitals%5D=0&Generator%5Bsingularize%5D=0&Generator%5Bns%5D=app%5Cmodels&Generator%5BbaseClass%5D=yii%5Cdb%5CActiveRecord&Generator%5Bdb%5D=db&Generator%5BuseTablePrefix%5D=0&Generator%5BgenerateRelations%5D=all&Generator%5BgenerateRelationsFromCurrentSchema%5D=0&Generator%5BgenerateRelationsFromCurrentSchema%5D=1&Generator%5BgenerateLabelsFromComments%5D=0&Generator%5BgenerateQuery%5D=0&Generator%5BqueryNs%5D=app%5Cmodels&Generator%5BqueryBaseClass%5D=yii%5Cdb%5CActiveQuery&Generator%5BenableI18N%5D=0&Generator%5BmessageCategory%5D=app&Generator%5BuseSchemaName%5D=0&Generator%5BuseSchemaName%5D=1&Generator%5Btemplate%5D=default&preview=";s:7:"Decoded";a:3:{s:5:"_csrf";s:88:"hSyQXAUwv3ZS1TVdWX94wVTMOhjTZ0bdUoQBOAKEYgDzYvQIU1_pNRyyZQQAJhWSYPp7d4QdGe8gtHhBdckBdg==";s:9:"Generator";a:18:{s:9:"tableName";s:14:"stage_purchase";s:10:"modelClass";s:13:"StagePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}}s:6:"SERVER";a:61:{s:16:"REDIRECT_MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:19:"REDIRECT_MYSQL_HOME";s:16:"\xampp\mysql\bin";s:21:"REDIRECT_OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:29:"REDIRECT_PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:14:"REDIRECT_PHPRC";s:10:"\xampp\php";s:12:"REDIRECT_TMP";s:10:"\xampp\tmp";s:15:"REDIRECT_STATUS";s:3:"200";s:7:"MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:9:"localhost";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"848";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:14:"HTTP_SEC_CH_UA";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:11:"HTTP_ORIGIN";s:16:"http://localhost";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:12:"HTTP_REFERER";s:39:"http://localhost/aleko-bo/web/gii/model";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:11:"HTTP_COOKIE";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";s:4:"PATH";s:1305:"c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\System32;C:\Windows;C:\Windows\System32\wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files (x86)\cloudflared;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files (x;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files (x86)\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\Zed\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:95:"<address>Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33 Server at localhost Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:38:"C:/xampp/htdocs/aleko-bo/web/index.php";s:11:"REMOTE_PORT";s:5:"59468";s:12:"REDIRECT_URL";s:23:"/aleko-bo/web/gii/model";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:23:"/aleko-bo/web/gii/model";s:11:"SCRIPT_NAME";s:23:"/aleko-bo/web/index.php";s:8:"PHP_SELF";s:23:"/aleko-bo/web/index.php";s:18:"REQUEST_TIME_FLOAT";d:1761532821.02705;s:12:"REQUEST_TIME";i:1761532821;}s:3:"GET";a:1:{s:2:"id";s:5:"model";}s:4:"POST";a:3:{s:5:"_csrf";s:88:"hSyQXAUwv3ZS1TVdWX94wVTMOhjTZ0bdUoQBOAKEYgDzYvQIU1_pNRyyZQQAJhWSYPp7d4QdGe8gtHhBdckBdg==";s:9:"Generator";a:18:{s:9:"tableName";s:14:"stage_purchase";s:10:"modelClass";s:13:"StagePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}s:6:"COOKIE";a:1:{s:5:"_csrf";s:130:"5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a:2:{i:0;s:5:"_csrf";i:1;s:32:"vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:2082:"a:5:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:6:"yii.js";}s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:55:"C:\xampp\htdocs\aleko-bo\vendor/bower-asset/jquery/dist";s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\62d52359";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/62d52359";s:7:"depends";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\gii\GiiAsset";a:9:{s:10:"sourcePath";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/assets";s:3:"css";a:1:{i:0;s:12:"css/main.css";}s:2:"js";a:2:{i:0;s:20:"js/bs4-native.min.js";i:1;s:9:"js/gii.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\82e5729c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/82e5729c";s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68fedb950f5ba";s:3:"url";s:39:"http://localhost/aleko-bo/web/gii/model";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:1761532821.02705;s:10:"statusCode";i:200;s:8:"sqlCount";i:199;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8974624;s:14:"processingTime";d:0.32220888137817383;}s:10:"exceptions";a:0:{}}