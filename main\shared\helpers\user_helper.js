import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../imports/helper.js";

async function getOrCreateUserByCtxOld(ctx, transaction) {
    const referral_check = common.util_helper.gotReferralChecker(ctx)
    let wallet_id = null

    const check_referral_exist = await query.models.wallet.findOne({
        where: { referral_code: referral_check.referral_code, is_delete: false }, include: [
            {
                model: query.models.user,
                attributes: [['id', 'user_id'], 'username', 'user_telegram_id', 'first_last_name', 'profile_pic', 'is_premium', 'has_joined_channel', 'email_address', 'created_at'],
                required: true,
                where: { is_delete: 0 },
            },
        ],
        raw: true
    });

    //check is referral from telegram only
    if (check_referral_exist) {
        if (check_referral_exist.user.user_telegram_id !== null) {
            wallet_id = check_referral_exist.id
        }
    }

    let [user, isUserCreated] = await query.models.user.findOrCreate({
        where: {
            user_telegram_id: ctx.from.id.toString(),
        },
        defaults: {
            user_telegram_id: ctx.from.id,
            is_premium: ctx.from.is_premium,
            username: ctx.from.username,
            first_last_name: `${ctx.from.first_name || ''} ${ctx.from.last_name || ''}`,
            profile_pic: null,
            temp_referral_wallet_id: wallet_id,
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: false,
        },
        transaction,
    });

    if (isUserCreated) {
        //to get the latest user data after updating
        await user.reload({ transaction });
    }

    if (user.is_delete) {
        //Shadow copy
        const old_user_telegram_id = user.user_telegram_id;
        const old_user_telegram_username = user.username;
        const old_user_telegram_first_last_name = user.first_last_name;
        const old_user_telegram_is_premium = user.is_premium;
        const old_user_temp_referral_wallet_id = user.temp_referral_wallet_id;
        //Shadow delete the user before create new user
        await user.update({
            user_telegram_id: `${old_user_telegram_id}_d_${common.util_helper.getCurrentEpochTime()}`, is_delete: 1, updated_at: common.util_helper.getCurrentEpochTime(),
        }, { transaction: transaction })

        user = await models.user.create({
            user_telegram_id: old_user_telegram_id,
            username: `${old_user_telegram_username || ''}`,
            first_last_name: `${old_user_telegram_first_last_name || ''}`,
            is_premium: old_user_telegram_is_premium || false,
            profile_pic: null,
            temp_referral_wallet_id: old_user_temp_referral_wallet_id,
            account_type: "telegram",
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
        }, {
            transaction: transaction
        })
        //get again user latest data
        user = await models.user.findOne({ where: { user_telegram_id: old_user_telegram_id.toString(), is_delete: false }, transaction: transaction })
    }

    return user;
}

async function getOrCreateUserByCtx(ctx, transaction) {
    const referral_check = common.util_helper.gotReferralChecker(ctx)
    let user_id = null
    const check_referral_exist = await query.models.user_referral.findOne({
        where: { referral_code: referral_check.referral_code, is_delete: false }, include: [
            {
                model: query.models.user,
                required: true,
                where: { is_delete: 0 },
            },
        ],
        raw: true,
        nest: true
    })

    if (check_referral_exist) {
        if (check_referral_exist.user.user_telegram_id !== null) {
            user_id = check_referral_exist.user.id
        }
    }

    let [user, isUserCreated] = await query.models.user.findOrCreate({
        where: {
            user_telegram_id: ctx.from.id.toString(),
        },
        defaults: {
            user_telegram_id: ctx.from.id,
            is_premium: ctx.from.is_premium,
            username: ctx.from.username,
            first_last_name: `${ctx.from.first_name || ''} ${ctx.from.last_name || ''}`,
            profile_pic: null,
            temp_referral_user_id: user_id,
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: false,
        },
        transaction,
    });

    if (isUserCreated) {
        //to get the latest user data after updating
        await user.reload({ transaction });
    }

    if (user.is_delete) {
        //Shadow copy
        const old_user_telegram_id = user.user_telegram_id;
        const old_user_telegram_username = user.username;
        const old_user_telegram_first_last_name = user.first_last_name;
        const old_user_telegram_is_premium = user.is_premium;
        const old_user_temp_referral_user_id = user.temp_referral_user_id;
        //Shadow delete the user before create new user
        await user.update({
            user_telegram_id: `${old_user_telegram_id}_d_${common.util_helper.getCurrentEpochTime()}`, is_delete: 1, updated_at: common.util_helper.getCurrentEpochTime(),
        }, { transaction: transaction })

        user = await models.user.create({
            user_telegram_id: old_user_telegram_id,
            username: `${old_user_telegram_username || ''}`,
            first_last_name: `${old_user_telegram_first_last_name || ''}`,
            is_premium: old_user_telegram_is_premium || false,
            profile_pic: null,
            temp_referral_wallet_id: old_user_temp_referral_user_id,
            account_type: "telegram",
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
        }, {
            transaction: transaction
        })
        //get again user latest data
        user = await models.user.findOne({ where: { user_telegram_id: old_user_telegram_id.toString(), is_delete: false }, transaction: transaction })
    }

    const find_user_referral = await query.models.user_referral.findOne({
        where: { user_id: user.id, is_delete: false },
        raw: true
    })

    if (find_user_referral === null) {
        const gen_hash_id = helper.referral_helper.generateHashId(user.id);
        const gen_group_tag = helper.referral_helper.buildReferralTag(gen_hash_id, check_referral_exist === null ? null : check_referral_exist.group_tag);
        const create_user_referral = await query.models.user_referral.create({
            user_id: user.id,
            hash_id: gen_hash_id,
            group_tag: gen_group_tag,
            referral_user_id: check_referral_exist === null ? null : check_referral_exist.user_id,
            referral_code: helper.referral_helper.referralCodeGenerator(),
            created_at: common.util_helper.getCurrentEpochTime(),
            is_delete: 0,
        }, {
            transaction: transaction,
        })

        if(check_referral_exist){
          //calculate how many user a upline invited, if its match with system setting max invite per week, then we will reward user.
            const last_monday_epoch = common.util_helper.getLastMondayEpoch();
            const next_monday_epoch = common.util_helper.getNextMondayEpoch();
            const get_weekly_max_invite = await query.models.system_setting.findOne({
                where: {
                key: "weekly_invite_max_num",
                is_delete: 0,
                },
            });
            const get_weekly_max_invite_num = parseInt(get_weekly_max_invite?.value);
            const count_invite_this_week = await query.models.user_referral.count({
            where: {
                referral_user_id: check_referral_exist.user_id,
                is_delete: 0,
                created_at: {
                    [query.Op.gte]: last_monday_epoch,
                    [query.Op.lt]: next_monday_epoch,
                },
            },
            transaction: transaction,
            });

            // Add 1 to include the current referral being processed
            const total_invites_including_current = count_invite_this_week + 1;
            // Only reward when user hits EXACTLY the weekly max invite number
            if(total_invites_including_current === get_weekly_max_invite_num){
                // User just hit the weekly target - reward them!
                const find_social_task = await query.models.social_task_lookup.findOne({
                    where: {
                        type: "weekly_task",
                        is_delete: 0,
                    },
                    transaction: transaction,
                });
                // Check if they already have this week's social task to prevent duplicates
                const existing_weekly_task = await query.models.user_social_task.findOne({
                    where: {
                        user_id: check_referral_exist.user_id,
                        social_task_id: find_social_task.id,
                        created_at: {
                            [query.Op.gte]: last_monday_epoch,
                            [query.Op.lt]: next_monday_epoch,
                        },
                        is_delete: 0,
                    },
                    transaction: transaction,
                });
                
                if (!existing_weekly_task) {
                    const create_user_social_task = await query.models.user_social_task.create(
                        {
                            user_id: check_referral_exist.user_id,
                            social_task_id: find_social_task.id,
                            is_done: true,
                            created_at: common.util_helper.getCurrentEpochTime(),
                            updated_at: null,
                            is_delete: 0,
                        },
                        {
                            transaction: transaction,
                        }
                    );
                    console.log(`Weekly referral target achieved! User ${check_referral_exist.user_id} completed weekly task.`);

                    //Add credit to user
                    let [alex_credit, is_alex_created] = await query.models.credit.findOrCreate({
                      where: {
                        user_id: check_referral_exist.user_id,
                        type: "alex",
                      },
                      defaults: {
                        user_id: check_referral_exist.user_id,
                        balance: 0.0,
                        type: "alex",
                        created_at: common.util_helper.getCurrentEpochTime(),
                        updated_at: common.util_helper.getCurrentEpochTime(),
                        is_delete: 0,
                      },
                      raw: true,
                      transaction: transaction,
                    });
                    if (!alex_credit) {
                        throw new Error("Alex credit not found. Please try again.");
                    }
                    console.log(`Weekly referral target reward given! User ${check_referral_exist.user_id} completed weekly task.`);

                    const reward_price = find_social_task?.reward_point || 0.0;
                    const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
                        check_referral_exist.user_id,
                        common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
                        parseFloat(reward_price),
                        common.enum_key.WALLET_TRANSACTION_TYPE.IN,
                        `Weekly Task`,
                        "weekly_task",
                        transaction
                    );
                }
            }
        }
    } //no else because if user_referral is available, any future referral code will still not invoke.

    let [alex_regular, is_alex_created] = await query.models.credit.findOrCreate({
        where: {
            user_id: user.id,
            type: "alex"
        },
        defaults: {
            user_id: user.id,
            balance: 0.0,
            type: "alex",
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: 0,
        },
        transaction: transaction,
    });
    let [energy_regular, is_energy_created] = await query.models.credit.findOrCreate({
        where: {
            user_id: user.id,
            type: "energy"
        },
        defaults: {
            user_id: user.id,
            balance: 0.0,
            type: "energy",
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: 0,
        },
        transaction: transaction,
    });

    if (isUserCreated) {
        if (check_referral_exist) {
            //check to verify when do upline hit the referral reward
            const upline_invite_count = await query.models.user_referral.count({
                where: {
                    referral_user_id: check_referral_exist.user_id,
                    is_delete: 0
                },
                transaction: transaction
            })
            if (upline_invite_count > 0) {
                const unlocked_tiers = await helper.referral_helper.findNewlyUnlockedTiers(check_referral_exist.user_id, upline_invite_count);
                console.log("Created User contribute to upline part (to awarded): ", unlocked_tiers);

                if (unlocked_tiers && unlocked_tiers.length > 0) {
                    const records_to_create = unlocked_tiers.map(tier => {
                        return {
                            user_id: check_referral_exist.user_id,
                            referral_reward_id: tier.id,
                            is_claim: false,
                            created_at: common.util_helper.getCurrentEpochTime(),
                            is_delete: 0,
                        };
                    });
                    await query.models.user_referral_reward.bulkCreate(records_to_create, {
                        transaction: transaction,
                    });
                    console.log(`Successfully created ${records_to_create.length} new reward records for UserID ${check_referral_exist.user_id}`);
                    unlocked_tiers.sort((a, b) => b.invite_tier_count - a.invite_tier_count);
                    let highest_unlocked_tier = unlocked_tiers[0];
                    console.log(`The highest new title for the upline is: ${highest_unlocked_tier.special_tier_title}`);

                    if (highest_unlocked_tier.special_tier_title !== null) {
                        await query.models.user.update({
                            current_referral_ranking: highest_unlocked_tier.special_tier_title,
                            updated_at: common.util_helper.getCurrentEpochTime()
                        }, {
                            where: {
                                id: check_referral_exist.user_id
                            },
                            transaction: transaction
                        })
                    }
                }
            }
        }
    }

    return user;
}

//Does not support referral
async function getOrCreateUserByTelegramID(user_telegram_id, transaction) {
    const user_data = await helper.bot_helper.telegram_bot_api.getChatMember(user_telegram_id, user_telegram_id);

    if (!user_data?.user) {
        return null;
    }

    let [user, isUserCreated] = await query.models.user.findOrCreate({
        where: {
            user_telegram_id: user_telegram_id.toString(),
        },
        defaults: {
            user_telegram_id: user_data.user.id,
            is_premium: user_data.user.is_premium,
            username: user_data.user.username,
            first_last_name: `${user_data.user.first_name || ''} ${user_data.user.last_name || ''}`,
            profile_pic: null,
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: false,
        },
        transaction,
    });

    if (isUserCreated) {
        //to get the latest user data after updating
        await user.reload({ transaction });
    }

    if (user.is_delete) {
        //Shadow copy
        const old_user_telegram_id = user.user_telegram_id;
        const old_user_telegram_username = user.username;
        const old_user_telegram_first_last_name = user.first_last_name;
        const old_user_telegram_is_premium = user.is_premium;
        //Shadow delete the user before create new user
        await user.update({
            user_telegram_id: `${old_user_telegram_id}_d_${common.util_helper.getCurrentEpochTime()}`, is_delete: 1, updated_at: common.util_helper.getCurrentEpochTime(),
        }, { transaction: transaction })

        user = await models.user.create({
            user_telegram_id: old_user_telegram_id,
            username: `${old_user_telegram_username || ''}`,
            first_last_name: `${old_user_telegram_first_last_name || ''}`,
            is_premium: old_user_telegram_is_premium || false,
            profile_pic: null,
            account_type: "telegram",
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
        }, {
            transaction: transaction
        })
        //get again user latest data
        user = await query.models.user.findOne({ where: { user_telegram_id: old_user_telegram_id.toString(), is_delete: false }, transaction: transaction })
    }
    return user;
}

async function getUserData(user_id, transaction) {
    const user = await query.models.user.findOne({
        where: {
            id: user_id,
            is_delete: 0
        },
        transaction: transaction,
        raw: true
    })
    return user;
}

async function getLatestToken(user_id, wallet_id, transaction) {
    const user = await getUserData(user_id, transaction);
    const wallet = await getUserWalletCreditByID(wallet_id, transaction, false);
    const signed_jwt = helper.auth_helper.signJWT(user, wallet.id, wallet.wallet_address, "LOGIN")
    await user.update(
        { jwt_token: signed_jwt, updated_at: common.util_helper.getCurrentEpochTime(), },
        { where: { id: user.id, }, lock: transaction.LOCK.UPDATE, transaction: transaction },
    );
    return signed_jwt;
}

async function getUserWalletCreditByID(wallet_id, transaction, lock = false) {
    const find_wallet = lock === true ? await query.models.wallet.findOne({
        where: { id: wallet_id, is_delete: 0 },
        include: [
            {
                model: models.user,
                attributes: [['id', 'user_id'], 'username', 'user_telegram_id', 'first_last_name', 'profile_pic', 'is_premium', 'has_joined_channel', 'email_address', 'created_at'],
                required: true,
                where: { is_delete: 0 },
            },
            {
                model: models.credit,
                attributes: [['id', 'credit_id'], 'type', 'balance'],
                required: true,
                where: { is_delete: 0 },
            },
        ],
        lock: transaction.LOCK.UPDATE,
        transaction,
        raw: true
    }) : await query.models.wallet.findOne({
        where: { id: wallet_id, is_delete: 0 },
        include: [
            {
                model: models.user,
                attributes: [['id', 'user_id'], 'username', 'user_telegram_id', 'first_last_name', 'profile_pic', 'is_premium', 'has_joined_channel', 'email_address', 'created_at'],
                required: true,
                where: { is_delete: 0 },
            },
            {
                model: models.credit,
                attributes: [['id', 'credit_id'], 'type', 'balance'],
                required: true,
                where: { is_delete: 0 },
            },
        ],
        transaction,
        raw: true
    })


    return find_wallet;
}

export default {
    getOrCreateUserByCtx,
    getOrCreateUserByTelegramID,
    getLatestToken,
    getUserData,
    getUserWalletCreditByID
}