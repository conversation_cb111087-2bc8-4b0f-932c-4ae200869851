import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js"
import { Api } from "grammy";

const user_context = {}
const api = new Api(common.config.telegram.token)


async function initializeUser(ctx) {
    const transaction = await query.sequelize.transaction();
    try {
        user_context[ctx.from.id] = null;

        let user = await helper.user_helper.getOrCreateUserByCtx(ctx, transaction)

        if (!user_context[ctx.from.id]) {
            user_context[ctx.from.id] = {};
        }

        user_context[ctx.from.id].user = user;
        await transaction.commit();
        return [user_context, null];
    } catch (error) {
        await transaction.rollback();
        await common.util_helper.handleErrorMessageBot(ctx, error)
        return [null, error];
    }
}

export default {
    initializeUser,
    user_context,
    telegram_bot_api: api
}