<?php
namespace app\components;

use Yii;
use yii\base\Component;
use kornrunner\Keccak;

class CheckSumHelper extends Component
{
    /**
     * Calculates the checksummed address from a given address following EIP-55 rule.
     * https://github.com/ethereum/ercs/blob/master/ERCS/erc-55.md
     *
     * @param string $address The address to calculate the checksummed address from.
     * @return string The checksummed address.
     */
    public static function checksumWalletAddress($address)
    {
        // Lowercase & strip "0x"
        $address = strtolower(str_replace('0x', '', $address));

        // Keccak256 hash of lowercase address
        $hash = Keccak::hash($address, 256);

        $ret = '0x';

        // EIP-55 rule:
        // If that nibble is ≥ 8, uppercase the corresponding address letter.
        // Otherwise, keep it lowercase.

        // Build checksummed address
        for ($i = 0; $i < strlen($address); $i++) {
            if (ctype_alpha($address[$i])) {
                // If the ith nibble of the hash >= 8 → uppercase this char
                $ret .= (intval($hash[$i], 16) >= 8)
                    ? strtoupper($address[$i]) // hash >= 8, uppercase
                    : $address[$i];  // hash < 8, lowercase
            } else {
                $ret .= $address[$i]; // skip if digit
            }
        }

        return $ret;
    }
}

?>