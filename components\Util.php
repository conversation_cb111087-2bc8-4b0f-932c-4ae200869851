<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\helpers\BaseUrl;
use yii\helpers\Url;

class Util extends Component
{
	/**
	 * Checks if the current IP address has exceeded the allowed rate limit.
	 * By default, it will use the parameters 'ratelimit_max_attempt' and 'ratelimit_lock_second' from the application configuration.
	 * If the IP address has exceeded the allowed rate limit, it will throw an exception with the specified error message.
	 *
	 * @param string $key the session key to store the rate limit information. Defaults to 'ratelimit'.
	 * @param string $error_msg the error message to throw if the IP address has exceeded the allowed rate limit. Defaults to a translated string.
	 * @param int $max_attempt the maximum number of attempts to allow before locking. Defaults to the value of 'ratelimit_max_attempt' from the application configuration.
	 * 
	 * @throws \Exception if the IP address has exceeded the allowed rate limit.
	 */
	public function checkIpRateLimitBySession($key = 'ratelimit', $error_msg = "", $max_attempt = null)
	{
		if (empty($max_attempt)) {
			$max_attempt = Yii::$app->params['ratelimit_max_attempt'];
		}

		$second      = Yii::$app->params['ratelimit_lock_second'];
		$ip_address  = Yii::$app->request->userIP;
		$info        = Yii::$app->session->get("{$key}#{$ip_address}");

		if (empty($error_msg)) {
			$error_msg = Yii::t('app', "Too many attempts, please try again after {time} minutes", [
				'time' => round($second / 60)
			]);
		}

		
		$init_opts = [
			'remain_attempt' => $max_attempt,
			'lock_until'     => 0,
			'lifetime'       => strtotime('now + 1 minutes'),
			'ip'             => $ip_address,
		];
		
		// initialize opts if info is empty, or lifetime or lock time are expired
		if (empty($info)) {
			$info = $init_opts;
		} else {
			if ($info['lifetime'] < strtotime('now')) {
				$info = $init_opts;
			}
			if (!empty($info['lock_until']) && ($info['lock_until'] < strtotime('now'))) {
				$info = $init_opts;
			}
		}

		// deduct one remaining attempt after every fail attempt
		$info['remain_attempt'] -= 1;

		// check if remain attempt is exhausted, lock the IP for a certain period if it is
		if ($info['remain_attempt'] < 0) {
			$info['remain_attempt'] = $max_attempt;
			$info['lock_until']     = strtotime("now + {$second} second");
			Yii::$app->session->set("{$key}#{$ip_address}", $info);
		}

		if (!empty($info['lock_until']) && $info['lock_until'] > strtotime('now')) {
			throw new \Exception($error_msg);
		}

		Yii::$app->session->set("{$key}#{$ip_address}", $info);
	}

	/**
	 * Returns an AWS presigned URL or a public URL for a given S3 object key.
	 * This is version 2 
	 *
	 * @param string $key The object key in the S3 bucket.
	 * 
	 * @return string The presigned or public URL for the object.
	 * 
	 * @throws \Exception if the user does not have permission to perform this action.
	 */
	public static function getAwsPresignedUrlV2($key)
	{
		$identity = Yii::$app->user->identity;
		if (!$identity) {
			throw new \Exception(Yii::t('app', 'You do not have permission to perform this action'));
		}

		$expireInSeconds = 30;
		if (!empty(Yii::$app->params['s3_presigned_expire_second'])) {
			$expireInSeconds = (int)Yii::$app->params['s3_presigned_expire_second'];
		}

		try {
			$s3 = Yii::$app->aws->client()->createS3();

			$aclResult = $s3->getObjectAcl([
				'Bucket' => Yii::$app->params['s3_bucket'],
				'Key' => $key,
			]);

			$isPublicRead = false;
			foreach ($aclResult['Grants'] as $grant) {
				if (isset($grant['Grantee']['URI']) && $grant['Grantee']['URI'] == 'http://acs.amazonaws.com/groups/global/AllUsers' && $grant['Permission'] == 'READ') {
					$isPublicRead = true;
					break;
				}
			}

			if ($isPublicRead == true) {
				$bucket = Yii::$app->params['s3_bucket'];
				$region = Yii::$app->params['s3_region'];
				$url = "https://{$bucket}.s3.{$region}.amazonaws.com/{$key}";
				return $url;
			}

			$cmd = $s3->getCommand('GetObject', [
				'Bucket' => Yii::$app->params['s3_bucket'],
				'Key' => $key,
			]);
			$request = $s3->createPresignedRequest($cmd, "+{$expireInSeconds} seconds");
			$url = (string)$request->getUri();

			return $url;
		} catch (\Exception $e) {
			//throw new \Exception('An error occurred while generating the URL: ' . $e->getMessage());

			$bucket = Yii::$app->params['s3_bucket'];
			$region = Yii::$app->params['s3_region'];
			$url = "https://{$bucket}.s3.{$region}.amazonaws.com/{$key}";
			return $url;
		}
	}

	/**
	 * Generates a presigned URL for a given AWS S3 URL.
	 *
	 * The presigned URL will expire after a certain amount of time which is configured in the application parameters.
	 *
	 * @param string $url The AWS S3 URL.
	 * 
	 * @return string The presigned URL.
	 * 
	 * @throws \Exception if there is an error while generating the presigned URL.
	 */
	public static function getAwsPresignedUrl($url)
	{
		$link = "";

		if (!empty(Yii::$app->params['s3_presigned_expire_second'])) {
			$second = Yii::$app->params['s3_presigned_expire_second'];
		}

		try {
			$explode = explode("/", $url);
			$explode = array_slice($explode, 3);
			$key_imp = implode("/", $explode);

			$aws              = Yii::$app->aws->client();
			$s3               = $aws->createS3();
			$secret_plans_cmd = $s3->getCommand('GetObject', [
				'Bucket' => Yii::$app->params['s3_bucket'],
				'Key'    => $key_imp,
			]);
			$request = $s3->createPresignedRequest($secret_plans_cmd, "+{$second} second");
			$link    = $request->getUri();
		} catch (\Exception $e) {
			throw new \Exception($e);
		}

		return (string) $link;
	}

	public function generateOtpCode($length = 6)
	{
		// Generate a random string of letters
		$letters = strtoupper(substr(str_shuffle("qwertyuiopasdfghjklzxcvbnm"), 0, $length));

		// Introduce numbers into the mix
		$numbers = substr(str_shuffle("0123456789"), 0, $length);

		// Combine and shuffle letters and numbers
		$combined = str_shuffle($letters . $numbers);

		// Extract the desired length
		$str = substr($combined, 0, $length);

		// Calculate a checksum digit and append it to the OTP
		$checksum = array_sum(str_split($str)) % 10;
		$str .= $checksum;

		// Final shuffle to mix in the checksum digit
		return str_shuffle($str);
	}
}
