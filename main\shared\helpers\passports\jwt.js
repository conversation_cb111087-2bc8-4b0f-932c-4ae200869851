import { Strategy, ExtractJwt } from "passport-jwt"
import common from "../../imports/common.js"
import query from "../../imports/query.js";

export default {
    "name": "jwt",
    "strategy": new Strategy({
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        secretOrKey: common.config.secret_config.jwt_secret + "LOGIN",
        passReqToCallback: true
    }, async function (req, payload, done) {
        const token = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
        try {
            const user = await query.models.user.findOne({ where: { id: payload.id, is_delete: false } })
            if (user) {
                const hash = common.util_helper.loginHash(user)
                if (hash !== payload.hash) {
                    return done(null, false, { message: 'Old token, please login again' })
                }
                if (token) {
                    if (token !== user.jwt_token) {
                        return done(null, false, { message: 'Old token, please login again' })
                    }
                }
                user.wallet_id = payload.wallet_id ?? null;
                user.wallet_address = payload.wallet_address ?? null;
                return done(null, user);
            }

            return done(null, false, { message: 'User not valid/found.' });
        } catch (error) {
            return done(error, false, { message: 'Error while authenticating user.' });
        }
    })
}