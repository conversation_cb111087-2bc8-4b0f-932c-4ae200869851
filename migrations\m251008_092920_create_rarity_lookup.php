<?php

use yii\db\Migration;

class m251008_092920_create_rarity_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%rarity_lookup}}', [
            'id' => $this->primaryKey(),
            'rarity_name' => $this->string()->notNull(),
            'upgrade_alex_spending' => $this->integer(),
            'success_rate_percentage' => $this->decimal(10, 2),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'secondary_updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%rarity_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_092920_create_rarity_lookup cannot be reverted.\n";

        return false;
    }
    */
}
