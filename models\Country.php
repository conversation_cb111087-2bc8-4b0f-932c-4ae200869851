<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "country".
 *
 * @property int $id
 * @property string|null $iso
 * @property string|null $name
 * @property string|null $nicename
 * @property string|null $iso3
 * @property int|null $numcode
 * @property int|null $phonecode
 * @property string|null $currency
 * @property int|null $is_active
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class Country extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'country';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['iso', 'name', 'nicename', 'iso3', 'numcode', 'phonecode', 'currency', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['numcode', 'phonecode', 'is_active', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['created_at'], 'required'],
            [['iso', 'iso3'], 'string', 'max' => 10],
            [['name', 'nicename'], 'string', 'max' => 80],
            [['currency'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'iso' => 'Iso',
            'name' => 'Name',
            'nicename' => 'Nicename',
            'iso3' => 'Iso3',
            'numcode' => 'Numcode',
            'phonecode' => 'Phonecode',
            'currency' => 'Currency',
            'is_active' => 'Is Active',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

}
