<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "stage".
 *
 * @property int $id
 * @property string $name
 * @property float $price_per_token
 * @property int $token_available
 * @property float $total_fund
 * @property int $should_end_date
 * @property string|null $status
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class Stage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'stage';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'price_per_token', 'token_available', 'total_fund', 'should_end_date', 'created_at'], 'required'],
            [['price_per_token', 'total_fund'], 'number'],
            [['token_available', 'should_end_date', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['name', 'status'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'price_per_token' => 'Price Per Token',
            'token_available' => 'Token Available',
            'total_fund' => 'Total Fund',
            'should_end_date' => 'Should End Date',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }
}
