<?php

use yii\db\Migration;

class m251008_093703_create_shop_item_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%shop_item_lookup}}', [
            'id' => $this->primaryKey(),
            'item_name' => $this->string()->notNull(),
            'item_icon_image' => $this->text()->notNull(),
            'item_category' => $this->string()->notNull(),
            'item_type' => $this->string()->notNull(),
            'item_boost_percentage' => $this->decimal(10, 2),
            'item_bundle_amount' => $this->decimal(10, 2),
            'item_price' => $this->decimal(10, 2)->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%shop_item_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093703_create_shop_item_lookup cannot be reverted.\n";

        return false;
    }
    */
}
