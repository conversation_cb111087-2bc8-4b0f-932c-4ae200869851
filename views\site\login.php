<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;

$this->title = 'Login';
?>

<div class="site-login">
    <div class="card card-outline card-primary">
        <div class="card-header text-center">
            <?= $this->render('./_alert_flash', []) ?>
            <h5><?= Yii::$app->params['company_name'] ?></h5>
        </div>
        <?php $form = ActiveForm::begin([
            'id' => 'login-form',
        ]); ?>
        <div class="card-body p-3">
            <div class="form-group">
                <?= $form->field($model, 'email', [
                    'options' => ['class' => 'form-group '],
                    'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-envelope"></i></div></div>',
                    'template' => '{beginWrapper}{input}{error}{endWrapper}',
                    'wrapperOptions' => ['class' => 'input-group mb-3'],
                ])->textInput(['placeholder' => $model->getAttributeLabel('email')]) ?>
            </div>
            <div class="form-group">
                <?= $form->field($model, 'password', [
                    'options' => ['class' => 'form-group '],
                    'inputTemplate' => '{input}<div class="input-group-append"><div class="input-group-text"><i class="fas fa-lock"></i></div></div>',
                    'template' => '{beginWrapper}{input}{error}{endWrapper}',
                    'wrapperOptions' => ['class' => 'input-group mb-3'],
                ])->passwordInput(['placeholder' => $model->getAttributeLabel('password')]) ?>
            </div>
            <div class="form-group mb-0">
                <div class="d-flex align-items-center justify-content-start">
                    <?= Html::a(Yii::t('app', 'Forgotten Password?'), ['site/request-password-code'], [
                        'class' => 'mb-3',
                    ]); ?>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <?= Html::submitButton('Sign In', [
                'class' => 'btn btn-primary float-right',
                'name'  => 'Login',
                'value' => 1,
            ]) ?>
        </div>
        <?php ActiveForm::end(); ?>
    </div>
</div>