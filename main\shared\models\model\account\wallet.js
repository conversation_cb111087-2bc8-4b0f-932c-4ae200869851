export default function (sequelize, DataTypes) {
    return sequelize.define('wallet', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        wallet_address: {
            type: DataTypes.TEXT,
            allowNull: false,
            unique: true,
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "token",
        },
        balance: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
            defaultValue: 0,
        },
        hash_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        group_tag: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        referral_wallet_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        referral_code: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true
        },
        network_type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "ton",
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'wallet',
        timestamps: false,
    })
}