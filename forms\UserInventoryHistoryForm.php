<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\UserShopCard;
use app\models\UserShopItem;

class UserInventoryHistoryForm extends Model
{
  public $username;
  public $user_telegram_id;
  public $first_last_name;
  public $wallet_address;
  public $date_range;
  public $date_start;
  public $date_end;

  // card
  public $rarity_name;
  public $instrument_name;

  // item
  public $item_name;
  public $item_category;
  public $item_type;

  public function rules()
  {
    return [
      [[
        'username', 'user_telegram_id', 'first_last_name', 'wallet_address', 'date_range', 'date_start', 'date_end',
        'rarity_name', 'instrument_name', 'item_name', 'item_category', 'item_type'
      ], 'safe'],
      [['username', 'user_telegram_id', 'wallet_address', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Get the query for data provider
   *
   * Select * from user_shop_card as usc
   * join user as u on usc.user_id = u.id
   * join rarity as r on usc.rarity_id = r.id
   * join instrument as i on usc.instrument_id = i.id
   * where u.id = :user_id
   * and u.username like %:username% or u.first_last_name like %:username%
   * and u.user_telegram_id = :user_telegram_id
   * and u.wallet_address like %:wallet_address%
   * and r.rarity_name like %:rarity_name%
   * and i.instrument_name = :instrument_name
   * and usc.created_at between :date_start and :date_end
   * and usc.is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getCardQuery()
  {
    $query = UserShopCard::find()
    ->alias('usc')
    ->joinWith('user u')
    ->joinWith(['rarity r', 'instrument i']);

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->user_telegram_id)) {
      $query->andFilterWhere(['u.user_telegram_id' => $this->user_telegram_id]);
    }

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }

    if (!empty($this->rarity_name)) {
      $query->andFilterWhere(['r.rarity_name' => $this->rarity_name]);
    }

    if (!empty($this->instrument_name)) {
      $query->andWhere(['i.instrument_name' => $this->instrument_name]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'usc.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['usc.is_delete' => 0]);

    return $query;
  }


/**
 * Gets the data provider for the user shop card history.
 *
 * @return ActiveDataProvider The data provider for the user shop card history.
 */
  public function getCardProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getCardQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Get the query for data provider
   *
   * Select * from user_shop_item as uic
   * join user as u on uic.user_id = u.id
   * join item as i on uic.item_id = i.id
   * where u.id = :user_id
   * and u.username like %:username% or u.first_last_name like %:username%
   * and u.user_telegram_id = :user_telegram_id
   * and u.wallet_address like %:wallet_address%
   * and i.item_name like %:item_name%
   * and i.item_category = :item_category
   * and i.item_type = :item_type
   * and uic.created_at between :date_start and :date_end
   * and uic.is_delete = 0
   * 
   * @return ActiveQuery
   */
  public function getItemQuery()
  {
    $query = UserShopItem::find()
    ->alias('uic')
    ->joinWith('user u')
    ->joinWith('item i');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->user_telegram_id)) {
      $query->andFilterWhere(['u.user_telegram_id' => $this->user_telegram_id]);
    }

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }

    if (!empty($this->item_name)) {
      $query->andFilterWhere(['like','i.item_name',"%".$this->item_name."%", false]);
    }

    if (!empty($this->item_category)) {
      $query->andWhere(['i.item_category' => $this->item_category]);
    }

    if (!empty($this->item_type)) {
      $query->andWhere(['i.item_type' => $this->item_type]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'uic.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['uic.is_delete' => 0]);

    return $query;
  }


/**
 * Gets the data provider for the user shop item history.
 *
 * @return ActiveDataProvider The data provider for the user shop item history.
 */
  public function getItemProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getItemQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

}
