import server from "../../../../shared/imports/server.js";
import common from "../../../../shared/imports/common.js";
import query from "../../../../shared/imports/query.js"
import validator from "../../../../shared/imports/validator.js"
import helper from "../../../../shared/imports/helper.js";

const router = server.express.Router();
const rarity_sequence = ['normal', 'rare', 'legendary', 'mythic']
const item_sequence = ['exp', 'token', 'tap']

//Shop Card
router.get("/card", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    const tomorrow_8am_epoch = common.util_helper.getTomorrow8amMalaysiaEpoch();
    const today_first_minute_epoch = common.util_helper.getTodayFirstMinuteEpoch();

    try {
        // =================================================================
        // 1. Fetch Today's Shop Configuration & All Supporting Data
        // =================================================================
        const shop_config = await query.models.shop_card_items_lookup.findOne({
            where: { is_delete: false },
            order: [['created_at', 'DESC']],
            transaction,
        });

        if (!shop_config || !shop_config.item_one || !shop_config.item_two || !shop_config.item_three) {
            await transaction.rollback();
            return res.status(400).json({ msg: "Shop configuration is missing or incomplete.", status: 400, error: true, data: {} });
        }


        // Fetch all possible card definitions (for price, ID, etc.)
        const all_card_definitions = await query.models.shop_card_lookup.findAll({
            where: { is_delete: false },
            transaction
        });
        if (all_card_definitions.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ msg: "Shop card definitions not found.", status: 400, error: true, data: {} });
        }

        // Create a Map for efficient O(1) lookups by card_type
        const card_definitions_map = new Map(all_card_definitions.map(card => [card.card_type, card]));
        const [
            all_instruments,
            user_purchases_today,
            user_resets_today,
            reset_setting
        ] = await Promise.all([
            query.models.instrument_lookup.findAll({ where: { instrument_is_active: true, is_delete: false }, transaction }),
            query.models.user_shop_card.findAll({
                where: { user_id, is_delete: false, created_at: { [query.Op.gte]: today_first_minute_epoch, [query.Op.lt]: tomorrow_8am_epoch } },
                include: [
                    {
                        model: query.models.rarity_lookup,
                        as: "rarity_lookup",
                        where: {
                            is_delete: false,
                        },
                    },
                    {
                        model: query.models.instrument_lookup,
                        as: "instrument_lookup",
                        where: {
                            is_delete: false,
                        },
                    },
                ],
                nest: true,
                transaction
            }),
            query.models.user_reset_card.findAll({
                where: { user_id, is_delete: false, trigger_at: { [query.Op.gte]: today_first_minute_epoch, [query.Op.lt]: tomorrow_8am_epoch } },
                order: [['id', 'DESC']],
                transaction
            }),
            query.models.system_setting.findOne({ where: { key: "shop_card_max_reset_count", is_delete: false }, transaction })
        ]);

        // Error handling for fetched data
        if (all_instruments.length === 0) { throw new Error("Instruments not found."); }
        if (!reset_setting) { throw new Error("Shop card reset count not found."); }

        // =================================================================
        // 2. Process Reset and Purchase Data
        // =================================================================
        const latest_reset_timestamp = user_resets_today.length > 0 ? user_resets_today[0].trigger_at : 0;
        const available_reset_count = Math.max(0, reset_setting.value - user_resets_today.length);

        // Group user purchases by card_type to handle duplicates correctly
        const user_purchases_by_type = new Map();
        for (const purchase of user_purchases_today) {
            const type = purchase.rarity_lookup.rarity_name;
            if (!user_purchases_by_type.has(type)) {
                user_purchases_by_type.set(type, []);
            }
            user_purchases_by_type.get(type).push(purchase);
        }


        // =================================================================
        // 3. Prepare, Sort, and Process Today's Shop Cards
        // =================================================================
        const todays_card_rarities_raw = [shop_config.item_one, shop_config.item_two, shop_config.item_three];
        const sorted_todays_rarities = todays_card_rarities_raw.sort((a, b) => {
            return rarity_sequence.indexOf(a) - rarity_sequence.indexOf(b);
        });
        const shop_card_list_processed = [];


        for (const card_rarity of sorted_todays_rarities) {
            const base_card_data = card_definitions_map.get(card_rarity);
            if (!base_card_data) {
                console.error(`Configuration Error: card type "${card_rarity}" does not exist in definitions.`);
                continue; // Skip invalid card
            }

            // Find a corresponding purchase for this slot.
            // .shift() removes the item, ensuring it's only used once.
            const purchase_records_for_rarity = user_purchases_by_type.get(card_rarity);
            const user_purchase_record = (purchase_records_for_rarity && purchase_records_for_rarity.length > 0)
                ? purchase_records_for_rarity.shift()
                : null;

            const has_purchased = !!(user_purchase_record && (user_purchase_record.created_at > latest_reset_timestamp));
            let card_price, randomized_instrument_id, randomized_instrument_name, randomized_instrument_image;

            if (has_purchased) {
                const purchased_instrument = user_purchase_record.instrument_lookup;
                card_price = purchased_instrument[`instrument_price_${card_rarity}`];
                randomized_instrument_id = purchased_instrument.id;
                randomized_instrument_name = purchased_instrument.instrument_name;
                randomized_instrument_image = purchased_instrument[`instrument_image_${card_rarity}`];
            } else {
                const random_instrument = all_instruments[Math.floor(Math.random() * all_instruments.length)];
                card_price = random_instrument[`instrument_price_${card_rarity}`];
                randomized_instrument_id = random_instrument.id;
                randomized_instrument_name = random_instrument.instrument_name;
                randomized_instrument_image = random_instrument[`instrument_image_${card_rarity}`];
            }

            shop_card_list_processed.push({
                had_purchased: has_purchased,
                // shop_card_id: base_card_data.id,
                rarity_id: base_card_data.rarity_id,
                card_rarity: base_card_data.card_type,
                card_price: card_price,
                randomized_instrument_id,
                randomized_instrument_name,
                randomized_instrument_image,
            });
        }

        await transaction.commit();
        return res.status(200).json({
            data: {
                shop_card_list: shop_card_list_processed,
                is_reset_available: available_reset_count > 0,
                available_reset_amount: available_reset_count,
            },
            status: 200,
            msg: "OK",
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

router.post("/card", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, validator.exp_validator(validator.items_validator.shopValidator.shop_card_validator), async (req, res) => {
    const { id: user_id } = req.user;
    const { rarity_id, instrument_id } = req.body
    const transaction = await query.sequelize.transaction();

    try {
        const find_rarity = await query.models.rarity_lookup.findOne({
            where: {
                id: rarity_id,
                is_delete: false,
            },
            transaction
        })
        if (!find_rarity) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Something went wrong. Please try again.",
                "error": true
            });
        }
        const find_instrument = await query.models.instrument_lookup.findOne({
            where: {
                id: instrument_id,
                is_delete: false,
            },
            transaction
        })
        if (!find_instrument) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Instrument not found. Please try again.",
                "error": true
            });
        }

        let [alex_credit, is_alex_created] = await query.models.credit.findOrCreate({
            where: {
                user_id: user_id,
                type: "alex"
            },
            defaults: {
                user_id: user_id,
                balance: 0.0,
                type: "alex",
                created_at: common.util_helper.getCurrentEpochTime(),
                updated_at: common.util_helper.getCurrentEpochTime(),
                is_delete: 0,
            },
            raw: true,
            transaction: transaction,
        });
        if (!alex_credit) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Alex credit not found. Please try again.",
                "error": true
            });
        }
        const instrument_rarity_price = find_instrument[`instrument_price_${find_rarity.rarity_name}`];
        if (parseFloat(alex_credit.balance) < parseFloat(instrument_rarity_price)) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Insufficient credit. Please try again.",
                "error": true
            });
        }

        await query.models.user_shop_card.create({
            user_id: user_id,
            // shop_card_id: shop_card_id,
            rarity_id: find_rarity.id,
            instrument_id: instrument_id,
            is_used: false,
            created_at: common.util_helper.getCurrentEpochTime(),
            updated_at: common.util_helper.getCurrentEpochTime(),
            is_delete: 0,
        }, {
            transaction: transaction,
        })

        const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
            parseFloat(instrument_rarity_price),
            common.enum_key.WALLET_TRANSACTION_TYPE.OUT,
            `Card Purchase ${common.util_helper.capitalize(find_rarity.rarity_name)} ${find_instrument.instrument_name}`,
            "shop_card_purchase",
            transaction
        );
        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

//Shop Item
router.get("/item",
    helper.auth_helper.authenticateJWT,
    helper.auth_helper.checkOnlyIsVerified,
    async (req, res) => {
        const { id: user_id } = req.user;
        const transaction = await query.sequelize.transaction();
        try {
            const find_all_item = await query.models.shop_item_lookup.findAll({
                where: {
                    item_category: "booster",
                    is_delete: false,
                },
                transaction
            })
            if (find_all_item.length === 0) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Shop items not found. Please try again.",
                    "error": true
                });
            }
            const find_all_bundle_item = await query.models.shop_item_lookup.findAll({
                where: {
                    item_category: "bundle",
                    is_delete: false,
                },
                transaction
            })
            if (find_all_bundle_item.length === 0) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Shop bundle items not found. Please try again.",
                    "error": true
                });
            }

            const shop_item_list_processed = find_all_item
                .map((shop_item) => ({
                    shop_item_id: shop_item.id,
                    item_name: shop_item.item_name,
                    item_icon_image: shop_item.item_icon_image,
                    item_type: shop_item.item_type,
                    item_boost_percentage: shop_item.item_boost_percentage,
                    item_price: shop_item.item_price,
                }))
                .sort((a, b) => {
                    const indexA = item_sequence.indexOf(a.item_type);
                    const indexB = item_sequence.indexOf(b.item_type);
                    const sortOrderA = indexA === -1 ? Infinity : indexA;
                    const sortOrderB = indexB === -1 ? Infinity : indexB;
                    if (sortOrderA !== sortOrderB) {
                        return sortOrderA - sortOrderB;
                    }
                    return a.item_boost_percentage - b.item_boost_percentage;
                });

            const shop_bundle_item_list_processed = find_all_bundle_item
                .map((shop_item) => ({
                    shop_item_id: shop_item.id,
                    item_name: shop_item.item_name,
                    item_icon_image: shop_item.item_icon_image,
                    item_type: shop_item.item_type,
                    item_bundle_amount: shop_item.item_bundle_amount,
                    item_price: shop_item.item_price,
                }))
                .sort((a, b) => {
                    const indexA = item_sequence.indexOf(a.item_type);
                    const indexB = item_sequence.indexOf(b.item_type);
                    if (indexA !== -1 && indexB !== -1) {
                        if (indexA === indexB) {
                            return a.item_bundle_amount - b.item_bundle_amount;
                        }
                        return indexA - indexB;
                    }
                    if (indexA !== -1) return -1;
                    if (indexB !== -1) return 1;
                    return a.item_bundle_amount - b.item_bundle_amount;
                });

            await transaction.commit();
            return res.status(200).json({
                data: {
                    shop_item_bundle_list: shop_bundle_item_list_processed,
                    shop_item_booster_list: shop_item_list_processed,
                },
                status: 200,
                msg: "OK",
                error: false
            });
        } catch (error) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
                error: true
            });
        }
    })

router.post("/item-bundle-purchase",
    helper.auth_helper.authenticateJWT,
    helper.auth_helper.checkOnlyIsVerified,
    validator.exp_validator(validator.items_validator.shopValidator.shop_item_bundle_validator),
    async (req, res) => {
        const { id: user_id } = req.user;
        const { bundle_item_id, quantity } = req.body
        const transaction = await query.sequelize.transaction();
        try {
            const find_bundle_item = await query.models.shop_item_lookup.findOne({
                where: {
                    id: bundle_item_id,
                    item_category: "bundle",
                    is_delete: false,
                },
                transaction
            })
            if (!find_bundle_item) {
                await transaction.rollback();
                return res.status(400).json({
                    data: {},
                    status: 400,
                    msg: "Shop bundle item not found. Please try again.",
                    error: true
                });
            }
            let [alex_credit, is_alex_created] = await query.models.credit.findOrCreate({
                where: {
                    user_id: user_id,
                    type: "alex"
                },
                defaults: {
                    user_id: user_id,
                    balance: 0.0,
                    type: "alex",
                    created_at: common.util_helper.getCurrentEpochTime(),
                    updated_at: common.util_helper.getCurrentEpochTime(),
                    is_delete: 0,
                },
                raw: true,
                transaction: transaction,
            });
            if (!alex_credit) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Something went wrong. Please try again.",
                    "error": true
                });
            }
            const total_price = find_bundle_item.item_price * quantity;
            if (parseFloat(alex_credit.balance) < parseFloat(total_price)) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Insufficient credit. Please try again.",
                    "error": true
                });
            }

            //loop of quantity to create multiple transaction
            for (let i = 0; i < quantity; i++) {
                await query.models.user_shop_item.create({
                    user_id: user_id,
                    item_id: find_bundle_item.id,
                    is_used: 0,
                    created_at: common.util_helper.getCurrentEpochTime(),
                    updated_at: common.util_helper.getCurrentEpochTime(),
                    is_delete: 0,
                }, {
                    transaction: transaction,
                })
                const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
                    user_id,
                    common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
                    parseFloat(total_price),
                    common.enum_key.WALLET_TRANSACTION_TYPE.OUT,
                    `Purchase ${common.util_helper.capitalize(find_bundle_item.item_name)} +${find_bundle_item.item_bundle_amount}`,
                    "shop_bundle_purchase",
                    transaction
                );
            }

            await transaction.commit();
            return res.status(200).json({
                data: {
                    total_price: total_price,
                },
                status: 200,
                msg: "OK",
                error: false
            });
        } catch (error) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
                error: true
            });
        }
    })

router.post("/item-booster-purchase",
    helper.auth_helper.authenticateJWT,
    helper.auth_helper.checkOnlyIsVerified,
    validator.exp_validator(validator.items_validator.shopValidator.shop_item_booster_validator),
    async (req, res) => {
        const { id: user_id } = req.user;
        const { booster_item_id, quantity } = req.body
        const transaction = await query.sequelize.transaction();
        try {
            const find_booster_item = await query.models.shop_item_lookup.findOne({
                where: {
                    id: booster_item_id,
                    item_category: "booster",
                    is_delete: false,
                },
                transaction
            })
            if (!find_booster_item) {
                await transaction.rollback();
                return res.status(400).json({
                    data: {},
                    status: 400,
                    msg: "Shop booster item not found. Please try again.",
                    error: true
                });
            }
            let [alex_credit, is_alex_created] = await query.models.credit.findOrCreate({
                where: {
                    user_id: user_id,
                    type: "alex"
                },
                defaults: {
                    user_id: user_id,
                    balance: 0.0,
                    type: "alex",
                    created_at: common.util_helper.getCurrentEpochTime(),
                    updated_at: common.util_helper.getCurrentEpochTime(),
                    is_delete: 0,
                },
                raw: true,
                transaction: transaction,
            });
            if (!alex_credit) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Something went wrong. Please try again.",
                    "error": true
                });
            }
            const total_price = find_booster_item.item_price * quantity;
            if (parseFloat(alex_credit.balance) < parseFloat(total_price)) {
                await transaction.rollback();
                return res.status(400).json({
                    "data": {},
                    "status": 400,
                    "msg": "Insufficient credit. Please try again.",
                    "error": true
                });
            }

            //loop of quantity to create multiple transaction
            for (let i = 0; i < quantity; i++) {
                await query.models.user_shop_item.create({
                    user_id: user_id,
                    item_id: find_booster_item.id,
                    is_used: 0,
                    created_at: common.util_helper.getCurrentEpochTime(),
                    updated_at: common.util_helper.getCurrentEpochTime(),
                    is_delete: 0,
                }, {
                    transaction: transaction,
                })
                const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
                    user_id,
                    common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
                    parseFloat(find_booster_item.item_price),
                    common.enum_key.WALLET_TRANSACTION_TYPE.OUT,
                    `Purchase ${common.util_helper.capitalize(find_booster_item.item_name)} +${find_booster_item.item_boost_percentage}%`,
                    "shop_booster_purchase",
                    transaction
                );
            }

            await transaction.commit();
            return res.status(200).json({
                data: {},
                status: 200,
                msg: "OK",
                error: false
            });
        } catch (error) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
                error: true
            });
        }
    })

export default router;



/*
shop_item_lookup
- id
- item_name
- item_icon_image
- item_type
- item_boost_percentage
- item_price
- item_category
- created_at integer
- updated_at integer
- is_delete boolean not null default 0



user_shop_item
- id
- user_id
- item_id
- is_used
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/



/*
query.models.shop_card_items_lookup.findOne({
    where: {
        is_delete: 0
    },
    order: [
        ['created_at', 'DESC']
    ],
    limit: 1
})
shop_card_items_lookup
- id primary key auto increment
- item_one string not_null
- item_two string not_null
- item_three string not_null
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/



/*
shop_card_number
- id
- normal_card_number
- rare_card_number
- legendary_card_number
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/


/*
user_reset_card
- id
- user_id
- trigger_at
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/


/*
user_shop_card
- id
- user_id
- shop_card_id 
- instrument_id
- is_used
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/


/*
instrument_lookup
- id
- instrument_name
- instrument_image_normal
- instrument_image_rare
- instrument_image_legendary
- instrument_price_mythic
- instrument_is_active
- created_at integer
- updated_at integer
- is_delete boolean not null default 0

rarity_lookup
- id integer primary key auto increment
- rarity_name string not null
- upgrade_alex_spending integer nullable
- success_rate_percentage double nullable
- created_at integer
- updated_at integer
- is_delete boolean not null default 0

shop_card_lookup
- id
- rarity_id
- card_type
- card_price
- created_at integer
- updated_at integer
- is_delete boolean not null default 0

shop_item_booster_lookup
- id
- item_name
- item_icon_image
- item_type
- item_boost_percentage
- item_price
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/