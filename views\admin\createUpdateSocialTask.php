<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use yii\web\View;
use yii\widgets\Breadcrumbs;
use yii\widgets\Pjax;

use kartik\file\FileInput;

use app\models\SocialTaskLookup;

if ($type == 'create') {
  $this->title = Yii::t('app', 'Create Task');
} else {
  $this->title = Yii::t('app', 'Update Task');
}

$main_page_url = 'social-task-mgmt';

?>

<div class="card card-default">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i> {link} / </i> </li>\n",
    'links' => [
      ['label' => 'Social Task Management', 'url' => [$main_page_url]],
      $this->title,
    ],
  ]);
  ?>
  <?php $form = ActiveForm::begin([
      'id'     => 'your-form',
      'method' => 'post',
      'options' => [
        'data-pjax' => true,
      ],
      'fieldConfig' => [
        'inputOptions' => [
          'class' => 'input-sm form-control',
        ],
      ],
    ]);
  ?>
    <div class="card-body">
      <?= $this->render('/site/_alert_flash', []) ?>
      <div class="row">
        <div class="col-md-12">
          <?= $form->field($model, 'type')->dropDownList(
            SocialTaskLookup::getType()
          , [
            'id'       => 'dropdown-type',
            'prompt'   => 'Select Type',
            'disabled' => $type == 'update',
          ])->label(Yii::t('app', 'Type')) ?>
        </div>
        <div class="col-md-12">
          <?= $form->field($model, 'icon_url')->widget(FileInput::classname(), [
            'pluginOptions' => [
              'showPreview' => false,
              'allowedFileExtensions' => ["png", "jpg", "jpeg"],
              'showUpload'  => false,
              'showRemove'  => false,
              'showCancel'  => false,
              'removeLabel' => "Delete",
              'removeClass' => "",
              'maxFileSize' => 1000
            ],
            'options' => [],
          ])->hint((Yii::t('app', 'Accepts only PNG, JPG, and JPEG formats. Recommended image dimensions are 70x70 pixels. The maximum file size allowed is 1MB.')))->label(Yii::t('app', 'Icon')) ?>
        </div>
        <?php if (!empty($model->icon_url)): ?>
          <div class="col-md-12">
            <label><b><?= Yii::t('app','Preview Icon')?></b></label>
          </div>
          <div class="col-md-12">
            <?= Html::img($model->icon_url, ['width' => '10%', 'style' => 'margin-bottom: 10px']); ?>
          </div>
        <?php endif; ?>
        <div class="col-md-12">
          <?= $form->field($model, 'task_name')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Title')) ?>
        </div>
        <div class="col-md-12">
          <?= $form->field($model, 'reward_point')->textInput([
              'style' => 'width:100%',
              'type'  => 'number',
              'step'  => '1',
              'min'   => '0',
            ])->label(Yii::t('app', 'Reward Box'))
          ?>
        </div>
        <div class="col-md-12">
          <?= $form->field($model, 'redirect_url')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Redirect URL')) ?>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <?= Html::a(Yii::t('app', 'Back'), Url::to($main_page_url), ['class' => 'btn btn-default']); ?>
      <?= Html::submitButton(Yii::t('app', $type == 'create' ? 'Create' : 'Update'), [
        'class' => 'btn btn-success float-right',
        'name'  => $type == 'create' ? 'Create' : 'Update',
        'value' => 1,
      ]) ?>
    </div>
  <?php ActiveForm::end(); ?>
</div>