<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%stage}}`.
 */
class m251016_085737_create_stage_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%stage}}', [
            'id'              => $this->primaryKey(),
            'name'            => $this->string(255)->notNull(),
            'price_per_token' => $this->decimal(36, 18)->notNull(),
            'token_available' => $this->integer()->notNull(),
            'total_fund'      => $this->decimal(36, 18)->notNull(),
            'should_end_date' => $this->bigInteger()->notNull(),
            'status'          => $this->string(255)->defaultValue('queuing'),
            'created_at'      => $this->bigInteger()->notNull(),
            'updated_at'      => $this->bigInteger(),
            'is_delete'       => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByName` (`name`)',
            'key `findByStatus` (`status`)',
            'key `findByShouldEndDate` (`should_end_date`)',
            'key `findByDate` (`created_at`)',
        ], "engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%stage}}');
    }
}
