import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

router.get("/config", async (req, res) => {
    const transaction = await query.sequelize.transaction();
    try {
        const maintenance_mode = await query.models.system_setting.findOne({ where: { key: "maintenance_mode", is_delete: 0 }, transaction })
        const data = {
            maintenance_mode: maintenance_mode.value,
        }
        await transaction.commit();
        return res.status(200).json({
            "data": data,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": "Something went wrong. Please try again.",
            "error": true
        });
    }
})

export default router;