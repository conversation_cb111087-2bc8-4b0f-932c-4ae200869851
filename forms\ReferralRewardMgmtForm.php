<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\Html;
use yii\data\ActiveDataProvider;
use app\models\ReferralRewardLookup;
use app\models\AuditLog;

class ReferralRewardMgmtForm extends Model
{
    public $special_tier_title;
    public $reward_amount;
    public $reward_type;
    public $invite_tier_count;

    public function rules()
    {
        return [
            [['special_tier_title', 'reward_amount', 'reward_type', 'invite_tier_count'], 'safe'],
        ];
    }

    /**
     * Gets the query for the referral reward lookup list.
     *
     * Select * from referral_reward_lookup
     * where is_delete = 0
     *
     * @return ActiveQuery The query for the referral reward lookup list.
     */
    public function getQuery()
    {
        $query = ReferralRewardLookup::find()
        ->where(['is_delete' => 0]);

        return $query;
    }

    /**
     * Gets the data provider for the referral reward lookup list.
     *
     * @return ActiveDataProvider The data provider for the referral reward lookup list.
     */
    public function getProvider()
    {
        $dataProvider = new ActiveDataProvider([
        'query' => $this->getQuery(),
        'sort'  => [
            'defaultOrder' => [
            'id' => SORT_ASC,
            ],
        ],
        'pagination' => [
            'pageSize' => 50,
        ],
        ]);

        return $dataProvider;
    }

    /* Keep for future use when referral reward mgmt required update action*/
    
    // public function getTier($id) {
    //     $model = ReferralRewardLookup::findOne([
    //         'id' => $id,
    //         'is_delete' => 0
    //     ]);

    //     if (empty($model)) {
    //         throw new \Exception('Tier not found');
    //     }

    //     $this->rank_number = $model->rank_number;
    //     $this->price = $model->price;
    //     $this->platform_fee = $model->platform_fee;
    //     $this->doc = $model->doc;
    // }

    // public function UpdateRank($id)
    // {
    //     $model = Rank::findOne([
    //         'id' => $id,
    //         'is_delete' => 0
    //     ]);

    //     $price_before = $model->price;
    //     $platform_fee_before = $model->platform_fee;
    //     $rebate_before = $model->rebate;
    //     $mining_power_before = $model->mining_power;
    //     $doc_before = $model->doc;

    //     $model->price = $this->price;
    //     $model->platform_fee = $this->platform_fee;
    //     $model->rebate = ($this->price) * 50 / 100; // 50% rebate
    //     $model->mining_power = ($this->price) * 25 / 100; //25% mining power
    //     $model->doc = $this->doc;
    //     $model->is_doc_active = 1;
    //     $model->updated_at = time();

    //     if (!$model->save()) {
    //         throw new \Exception(current($model->getFirstErrors()));
    //     }

    //     $user = Yii::$app->user->identity;

    //     if ($model->price != $price_before) {
    //         AuditLog::create([
    //             'function'  => AuditLog::MANAGE_RANK,
    //             'old_value' => $price_before,
    //             'value'     => $this->price,
    //             'remark'    => 'Changed rank price',
    //             'action_by' => $user->id,
    //         ]);
    //     }

    //     if ($model->platform_fee != $platform_fee_before) {
    //         AuditLog::create([
    //             'function'  => AuditLog::MANAGE_RANK,
    //             'old_value' => $platform_fee_before,
    //             'value'     => $this->platform_fee,
    //             'remark'    => 'Changed rank platform fee',
    //             'action_by' => $user->id,
    //         ]);
    //     }

    //     if ($model->doc != $doc_before) {
    //         AuditLog::create([
    //             'function'  => AuditLog::MANAGE_RANK,
    //             'old_value' => $doc_before,
    //             'value'     => $this->doc,
    //             'remark'    => 'Changed rank doc',
    //             'action_by' => $user->id,
    //         ]);
    //     }

    //     return $model;
    // }

    // public function activeRankUrl($id)
    // {
    //     $model = Rank::findOne([
    //         'id'        => $id,
    //         'is_delete' => 0,
    //     ]);
    
    //     if (empty($model)) {
    //         return false;
    //     }

    //     $model->is_doc_active = $model->is_doc_active == 1 ? 0 : 1;
    //     $model->update(false, ['is_doc_active']);

    //     $user = Yii::$app->user->identity;

    //     AuditLog::create([
    //         'function'  => AuditLog::MANAGE_RANK,
    //         'value'     => "Rank Number: {$model->rank_number}",
    //         'remark'    => $model->is_doc_active ? 'Activate rank url' : 'Deactivate rank url',
    //         'action_by' => $user->id,
    //     ]);

    //     return true;
    // }
}