<?php

/**
 * @link https://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license https://www.yiiframework.com/license/
 */

namespace app\commands;

use Yii;
use yii\console\Controller;

use app\models\AdminRole;
use app\models\Admin;
use app\models\User;

class SystemController extends Controller
{
	/**
	 * Create a new admin user.
	 *
	 * This command will guide you to create a new admin user.
	 * It will ask for the role name, email and password.
	 * If the role name is not existed, it will exit.
	 * If the email is already existed, it will exit.
	 * If the password is empty, it will exit.
	 */
	public function actionCreateAdminUser()
	{
		//create new user
		$role_list  = AdminRole::find()->all();

		if (empty($role_list)) {
			echo "> [ERR] Empty User Role\n";
			exit;
		}

		// show role list
		echo "\n";
		echo "### User Role ###\n";
		echo "<Role Name> - <Role Label>\n";

		$roleList = [];
		foreach ($role_list as $a) {
			$roleList[] = $a->name;
			echo " [{$a->name}] - {$a->label}\n";
		}

		// get user input for role name
		echo "\n";
		echo "> Enter Role Name : ";

		$handle = fopen("php://stdin", "r");
		$role   = trim(fgets($handle));

		if (empty($role)) {
			echo "> No role enter. Abort.\n";
			exit;
		}

		if (!in_array(trim($role), $roleList)) {
			echo "\n Exit. Wrong Role Name";
			exit;
		}

		// get user input for email
		echo "> Enter Email : ";

		$email = trim(fgets($handle));

		if (empty($email)) {
			echo "> No email enter. Abort.\n";
			exit;
		}

		// get user input for password
		echo "> Enter Password : ";

		$password = trim(fgets($handle));

		if (empty($password)) {
			echo "> No password enter. Abort.\n";
			exit;
		}

		fclose($handle);

		// create admin user
		$model = new Admin;
		$db    = Yii::$app->db->beginTransaction();

		try {
			// check email unique
			$user = Admin::findOne([
				'email'     => $email,
				'is_delete' => 0
			]);

			if (!empty($user)) {
				throw new \Exception("Email already existed.");
			}

			$user = $model->createAdmin([
				'email'    => $email,
				'password' => $password,
				'role'     => $role,
			]);

			$db->commit();

			echo "[SUCCESS] " . $user->email . " created\n";
		} catch (\Exception $e) {
			$db->rollback();
			throw $e;
		}

		return true;
	}

    /**
     * Create first user (head of node/ company address).
     *
     * @throws \Exception
     */
    public function actionCreateUser()
    {
        $user = User::findOne([
            'is_delete' => 0
        ]);

        // only allow create user if there is no user in the system
		if ($user) {
            throw new \Exception("First user created", 1);
        }

        // get user input for wallet address
		echo "\n";
		echo "> Enter wallet address : ";

		$handle = fopen("php://stdin", "r");
		$wallet_address   = trim(fgets($handle));

        $db = Yii::$app->db->beginTransaction();

        try {
            $model = new User;
            $model->wallet_address = $wallet_address;
            $model->member_id = 70000001;
            $model->created_at   = time();

            if (!$model->save()) {
                throw new \Exception(current($model->getFirstErrors()));
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
			throw $e;
        }

        return true;
    }
}
