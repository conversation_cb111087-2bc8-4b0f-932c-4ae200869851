<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_notice_reward".
 *
 * @property int $id
 * @property int $user_id
 * @property int $notice_id
 * @property int|null $wallet_id
 * @property int $is_claim
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property NoticeLookup $notice
 * @property User $user
 * @property Wallet $wallet
 */
class UserNoticeReward extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_notice_reward';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wallet_id', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'notice_id', 'created_at'], 'required'],
            [['user_id', 'notice_id', 'wallet_id', 'is_claim', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
            [['notice_id'], 'exist', 'skipOnError' => true, 'targetClass' => NoticeLookup::class, 'targetAttribute' => ['notice_id' => 'id']],
            [['wallet_id'], 'exist', 'skipOnError' => true, 'targetClass' => Wallet::class, 'targetAttribute' => ['wallet_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'notice_id' => 'Notice ID',
            'wallet_id' => 'Wallet ID',
            'is_claim' => 'Is Claim',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Notice]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNotice()
    {
        return $this->hasOne(NoticeLookup::class, ['id' => 'notice_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Wallet]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::class, ['id' => 'wallet_id']);
    }

}
