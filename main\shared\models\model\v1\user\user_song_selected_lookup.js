export default function (sequelize, DataTypes) {
    return sequelize.define('user_song_selected_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        song_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        genre_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        tap_count: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        boost_instrument_shop_card_one_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_shop_card_two_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_shop_card_three_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_shop_card_four_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        exp_booster_user_shop_item_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        token_booster_user_shop_item_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        tap_booster_user_shop_item_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'user_song_selected_lookup',
        timestamps: false,
    });
}