{"name": "aleko", "version": "1.0.0", "main": "main/apis/app.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon app.js", "bot": "nodemon main/bot/app-bot.js"}, "author": "<PERSON>", "license": "ISC", "description": "Aleko Backend APIs Project", "dependencies": {"@aws-sdk/client-s3": "^3.299.0", "@grammyjs/menu": "^1.2.1", "@grammyjs/runner": "^2.0.3", "@orbs-network/ton-access": "^2.3.3", "@ton/core": "^0.62.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.3.1", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "chalk": "^4.1.2", "comma-number": "^2.1.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "decimal.js": "^10.6.0", "express": "^4.19.2", "express-brute": "^1.0.1", "express-rate-limit": "^7.4.0", "express-session": "^1.18.0", "express-validator": "^7.2.0", "grammy": "^1.27.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "millify": "^6.1.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "morgan": "^1.10.0", "mysql2": "^3.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-strategy": "^1.0.0", "sequelize": "^6.37.3", "tonweb": "^0.0.66"}, "devDependencies": {"nodemon": "^3.1.4"}}