export default function (sequelize, DataTypes) {
    return sequelize.define('stage_purchase', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        wallet_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        stage_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        log_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        tx_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        payment_jetton: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        contract_address: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        amount: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
            defaultValue: 0
        },
        usd_amount: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
            defaultValue: 0
        },
        token_amount: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
            defaultValue: 0
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'stage_purchase',
        timestamps: false,
    })
}
