import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../imports/helper.js";

async function wallet_transaction_processor(user_id, credit_type, amount, action, description = null, remark = null, transaction) {
    const allowed_actions = Object.values(common.enum_key.WALLET_TRANSACTION_TYPE);
    const allowed_credit_type = Object.values(common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE);
    if (!allowed_actions.includes(action)) {
        throw new Error(`Invalid action type: '${action}'.`);
    }
    if (!allowed_credit_type.includes(credit_type)) {
        throw new Error(`Invalid credit type: '${credit_type}'.`);
    }

    //When create acount user credits should have init, so use findOne instead of findOrCreate
    let find_credit = await query.models.credit.findOne({
        where: {
            user_id: user_id,
            type: credit_type,
            is_delete: false,
        },
        lock: transaction.LOCK.UPDATE, transaction: transaction
    })
    if (!find_credit) {
        throw new Error(`User credit type: '${credit_type}' not found.`);
    }
    const old_credit_amount = find_credit.balance;
    const action_symbol = action === "IN" ? "+" : "-";
    await find_credit.update({ balance: query.sequelize.literal(`balance ${action_symbol} ${parseFloat(amount)}`) }, { lock: transaction.LOCK.UPDATE, transaction: transaction })
    find_credit = await query.models.credit.findOne({
        where: {
            user_id: user_id,
            type: credit_type,
            is_delete: false,
        },
        lock: transaction.LOCK.UPDATE, transaction: transaction
    })
    const new_credit_amount = find_credit.balance;
    await query.models.wallet_transaction.create({
        user_id: user_id,
        credit_id: find_credit.id,
        type: action,
        amount: parseFloat(amount),
        before_balance: old_credit_amount,
        after_balance: new_credit_amount,
        category: credit_type,
        description: description,
        remark: remark,
        created_at: common.util_helper.getCurrentEpochTime(),
        updated_at: common.util_helper.getCurrentEpochTime(),
        is_delete: 0,
    }, {
        transaction: transaction,
    })
    return {
        user_id: user_id,
        credit_id: find_credit.id,
        credit_type: credit_type,
        amount: amount,
        before_balance: old_credit_amount,
        after_balance: new_credit_amount
    }
}

export default {
    wallet_transaction_processor
}