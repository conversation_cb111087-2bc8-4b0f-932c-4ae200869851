<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_social_task}}`.
 */
class m250826_081047_create_user_social_task_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_social_task}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'social_task_id' => $this->integer()->notNull(),
            'wallet_id' => $this->integer(),
            'level_id' => $this->integer(),
            'is_done' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`social_task_id`) references `social_task_lookup` (`id`)',
            'foreign key (`wallet_id`) references `wallet` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findBySocialTaskId` (`social_task_id`, `is_delete`)',
            'key `findByWalletId` (`wallet_id`, `is_delete`)',
            'key `findByUserLevelId` (`user_id`, `level_id`, `is_delete`)',
            'key `findByUserTaskIsDone` (`user_id`, `is_done`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_social_task}}');
    }
}
