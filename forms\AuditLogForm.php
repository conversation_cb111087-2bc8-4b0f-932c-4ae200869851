<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use app\models\AuditLog;

class AuditLogForm extends Model
{
  public $email;
  public $date_range;
  public $date_start;
  public $date_end;
  public $function;

  public function rules()
  {
    return [
      [['email', 'date_range', 'date_start', 'date_end', 'function'], 'safe'],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Gets an array of all available functions in audit log.
   *
   * @return string[] An array with function names as keys and values.
   */
  public function getFunction()
  {
    $query = AuditLog::find();
    $query->select('function');
    $query->groupBy(['function']);
    $result = ArrayHelper::map($query->all(), 'function', 'function');

    return $result;
  }

  /**
   * Gets the query for the audit log based on the current form data.
   * 
   * select * from audit_log as al
   * left join admin a on a.id = al.action_by
   * where a.email = %:email% and al.function  = :function and al.created_at between :date_start and :date_end
   * and al.is_delete = 0
   *
   * @return ActiveQuery The query for the audit log.
   */
  public function getQuery()
  {
    $query = AuditLog::find();
    $query->alias('al');
    $query->leftJoin('admin a', 'a.id = al.action_by');

    if (!empty($this->email)) {
      $query->andFilterWhere(['like','a.email',"%".$this->email."%", false]);
    }

    if (!empty($this->function)) {
      $query->andFilterWhere(['al.function' => $this->function]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'al.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['al.is_delete' => 0]);

    // throw new \Exception($query->createCommand()->rawSql);

    return $query;
  }

  /**
   * Gets the data provider for the audit log based on the current form data.
   *
   * @return ActiveDataProvider The data provider for the audit log.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }
}
