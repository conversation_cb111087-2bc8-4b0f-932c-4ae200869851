/**
 * Admin Wallet Helper
 * 
 * Provides admin wallet initialization and management for TON blockchain operations
 */

import { mnemonicToWalletKey } from "@ton/crypto";
import tonPkg from "@ton/ton";
const { WalletContractV4, WalletContractV5R1 } = tonPkg;
import config from "../../../config.json" assert { type: "json" };
import { Address } from "@ton/core";

// Check which wallet types are available
const hasV5R1 = typeof WalletContractV5R1 !== 'undefined';

/**
 * Initialize admin wallet from mnemonic
 * @param {Object} tonClient - TON client instance
 * @returns {Promise<Object>} Object containing wallet contract, keyPair, address
 */
export async function initializeAdminWallet(tonClient) {
    try {
        console.log('Initializing admin wallet...');
        
        // Validate mnemonic configuration
        if (!config.admin_mnemonic) {
            throw new Error('Admin mnemonic not configured in config.json');
        }

        // Support both string (space-separated) and array formats
        let mnemonicArray;
        if (typeof config.admin_mnemonic === 'string') {
            // String format: "word1 word2 word3 ..."
            mnemonicArray = config.admin_mnemonic.trim().split(/\s+/);
            console.log('Mnemonic format: string (space-separated)');
        } else if (Array.isArray(config.admin_mnemonic)) {
            // Array format: ["word1", "word2", "word3", ...]
            mnemonicArray = config.admin_mnemonic;
            console.log('Mnemonic format: array');
        } else {
            throw new Error('Admin mnemonic must be a string or array');
        }

        if (mnemonicArray.length === 0) {
            throw new Error('Admin mnemonic is empty');
        }

        if (mnemonicArray.length !== 24) {
            throw new Error(`Invalid mnemonic word count: ${mnemonicArray.length} (expected 24)`);
        }

        // Derive wallet from mnemonic
        const keyPair = await mnemonicToWalletKey(mnemonicArray);
        
        // Determine wallet type from config (default: v4 if V5R1 not available)
        const walletType = config.mnemonic_type || (hasV5R1 ? 'v5' : 'v4');
        let wallet;
        
        switch (walletType.toLowerCase()) {
            case 'v4':
            case 'v4r2':
                wallet = WalletContractV4.create({
                    workchain: 0,
                    publicKey: keyPair.publicKey
                });
                console.log(`Wallet type: V4`);
                break;
            
            case 'v5':
            case 'v5r1':
                if (hasV5R1) {
                    wallet = WalletContractV5R1.create({
                        workchain: 0,
                        publicKey: keyPair.publicKey
                    });
                    console.log(`Wallet type: V5R1`);
                } else {
                    console.warn(`WARNING: V5R1 not available in @ton/ton ${tonPkg.version || 'current version'}, falling back to V4`);
                    wallet = WalletContractV4.create({
                        workchain: 0,
                        publicKey: keyPair.publicKey
                    });
                    console.log(`Wallet type: V4 (fallback)`);
                }
                break;
            
            default:
                wallet = WalletContractV4.create({
                    workchain: 0,
                    publicKey: keyPair.publicKey
                });
                console.log(`Wallet type: V4 (default)`);
                break;
        }

        const walletAddress = wallet.address;

        // Generate all address formats
        const isMainnet = config.ton.is_mainnet || false;
        const bounceableAddress = walletAddress.toString({ bounceable: true, testOnly: !isMainnet });
        const nonBounceableAddress = walletAddress.toString({ bounceable: false, testOnly: !isMainnet });

        console.log(`✅ Admin wallet initialized:`);
        console.log(`   Bounceable: ${bounceableAddress}`);
        console.log(`   Non-bounceable: ${nonBounceableAddress}`);
        
        return {
            contract: wallet,
            keyPair: keyPair,
            address: bounceableAddress,
            addressBounceable: bounceableAddress,
            addressNonBounceable: nonBounceableAddress,
            addressObject: walletAddress,
            client: tonClient
        };
    } catch (error) {
        console.error('❌ Failed to initialize admin wallet:', error.message);
        throw error;
    }
}

/**
 * Check admin wallet balance
 * @param {Object} tonHelper - TON helper instance with getTonBalance method
 * @param {Object} walletAddress - Wallet address (string or Address object)
 * @param {number} warningThreshold - Balance threshold for warning (default: 1.0 TON)
 * @param {number} criticalThreshold - Balance threshold for critical error (default: 0.5 TON)
 * @returns {Promise<Object>} Balance check result { balance, warning, critical }
 */
export async function checkAdminWalletBalance(tonHelper, walletAddress, warningThreshold = 1.0, criticalThreshold = 0.5) {
    try {
        const [balance, error] = await tonHelper.getTonBalance(walletAddress);
        
        if (error) {
            console.warn(`Could not fetch wallet balance: ${error}`);
            return { balance: null, warning: false, critical: false, error };
        }

        const balanceNum = parseFloat(balance);
        console.log(`Admin wallet balance: ${balance} TON`);
        
        const result = {
            balance: balance,
            warning: false,
            critical: false,
            error: null
        };

        if (balanceNum < criticalThreshold) {
            result.critical = true;
            console.error(`❌ CRITICAL: Admin wallet balance (${balance} TON) below minimum threshold (${criticalThreshold} TON)`);
        } else if (balanceNum < warningThreshold) {
            result.warning = true;
            console.warn(`WARNING: Admin wallet balance (${balance} TON) below recommended threshold (${warningThreshold} TON)`);
        }

        return result;
    } catch (error) {
        console.error('❌ Error checking wallet balance:', error.message);
        return { balance: null, warning: false, critical: true, error: error.message };
    }
}

export default {
    initializeAdminWallet,
    checkAdminWalletBalance
};
