<?php

namespace app\forms;

use Exception;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\AlertEmail;
use app\models\AuditLog;
class AlertEmailMgmtForm extends AttributeChangeForm
{
    // User
    public $id;
    public $email_address;
    public $is_active;
    public $last_sent_at;
    public $created_at;
    public $updated_at;

    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            [['email_address', 'is_active'], 'safe'],
            [['email_address', 'is_active'], 'required', 'on' => ['create', 'update']],
            [['is_active', 'last_sent_at'], 'integer'],
            [['email_address'], 'string', 'max' => 255],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'email_address' => 'Email Address',
            'is_active' => 'Is Active',
            'last_sent_at' => 'Last Sent At',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
    
    /**
     * Initialize the form model by loading existing data from the database
     */
    public function init()
    {
        // Call parent init
        parent::init();

        // Find existing AlertEmail with is_delete is false
        $model = AlertEmail::findOne([
        'id'        => $this->id,
        'is_delete' => 0,
        ]);

        // Populate the form model with its data if found
        if(!empty($model)){
            $this->email_address = $model->email_address;
            $this->is_active     = $model->is_active;
        }

        // Store the current state of attributes for changes tracking
        $this->initAttributes();
    }


    /**
     * Create a new AlertEmail with input data
     * @return AlertEmail model
     */
    public function createAlertEmail()
    {
        // validate the data with rules
        if (!$this->validate()) {
            throw new \Exception(current($this->getFirstErrors()));
        }

        // Initialization
        $email = new AlertEmail();

        $user = Yii::$app->user->identity;
        
        // Assign the attributes to the model
        $email->email_address = $this->email_address;
        $email->is_active = $this->is_active;
        $email->created_at = date('Y-m-d h:m:s', time());

        // Attempt to save the model
        if (!$email->save(false)){
            throw new \Exception(current($email->getFirstErrors()));
        }
        
        AuditLog::create([
            'function'  => AuditLog::MANAGE_ALERT_EMAIL,
            'value'     => $this->email_address,
            'remark'    => 'Create Alert Email',
            'action_by' => $user->id,
        ]);

        return $email;
    }

    /**
     * Update a new AlertEmail with input data
     * @return boolean
     */
    public function updateAlertEmail()
    {
        // Validate the data with rules
        if (!$this->validate()) {
           throw new \Exception(current($this->getFirstErrors()));
        }

         $user = Yii::$app->user->identity;

        // Find existing AlertEmail with is_delete is false
        $model = AlertEmail::findOne([
            'id'        => $this->id,
            'is_delete' => 0,
            ]);
        if (empty($model)) {
            return false;
        } 

        if ($model->email_address != $this->email_address) {
            AuditLog::create([
                'function'  => AuditLog::MANAGE_ALERT_EMAIL,
                'old_value' => $model->email_address,
                'value'     => $this->email_address,
                'remark'    => 'Update Alert Email Adress',
                'action_by' => $user->id,
            ]);
        }

        if ($model->is_active != $this->is_active) {
            AuditLog::create([
                'function'  => AuditLog::MANAGE_ALERT_EMAIL,
                'old_value' => $model->is_active,
                'value'     => $this->is_active,
                'remark'    => 'Update Active Status of Alert Email',
                'action_by' => $user->id,
            ]);
        }

        // Get the list of changed attributes
        $attributes = $this->getChangedAttributes();

        // Loop through each changed attribute and apply the update
        foreach ($attributes as $attr => $change) {
            $model->$attr = $change['new'];
        }

        // Update the timestamp to the current time
        $model->updated_at = date('Y-m-d h:m:s', time());

        // Perform update in the database
        if (!$model->update(false, ['email_address', 'is_active', 'updated_at'])){
            throw new \Exception(current($model->getFirstErrors()));
        }

        return true;
    }

    /**
     * Soft delete the AlertEmail in the database
     * @return boolean
     */
    public function deleteAlertEmail($id)
    {
        $user = Yii::$app->user->identity;

        // Find existing AlertEmail with is_delete is false
        $model = AlertEmail::findOne([
            'id'        => $id,
            'is_delete' => 0,
        ]);
        if (empty($model)) {
            return false;
        }

        // Set the is_delete to 1
        $model->is_delete = 1;

        // Perform update in the database
        if (!$model->update(false, ['is_delete'])){
            throw new \Exception(current($model->getFirstErrors()));
        }

        AuditLog::create([
            'function'  => AuditLog::MANAGE_ALERT_EMAIL,
            'value'     => $this->email_address,
            'remark'    => 'Delete Alert Email',
            'action_by' => $user->id,
        ]);

        return true;
    }
    
    /**
     * Builds the query to retrieve AlertEmail records
     * @return ActiveQuery object
     */
    public function getQuery()
    {
        // Select * from alert_email
        // Where is_delete => 0
        //  And email_address like %$this->email_address%
        //  And is_active = (int)$this->is_active
        $query = AlertEmail::find()->where(['is_delete' => 0]);
        if (!empty($this->email_address)) {
            $query->andFilterWhere(['like', 'email_address', "%".$this->email_address."%", false]);
        }
        if ($this->is_active !== null && $this->is_active !== '') {
            $query->andFilterWhere(['is_active' => (int)$this->is_active]);
        }

        return $query;
    }

    /**
     * Creates and returns an ActiveDataProvider for paginated and sorted data display
     * @return ActiveDataProvider
     */
    public function getProvider()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => $this->getQuery(),
            'sort' => false,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $dataProvider;
    }

}
