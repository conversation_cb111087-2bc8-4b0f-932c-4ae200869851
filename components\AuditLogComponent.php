<?php

namespace app\components;

use Yii;
use yii\base\Component;
use app\models\AuditLog;

class AuditLogComponent extends Component
{
	/**
	 * Creates an audit log record.
	 *
	 * @param \yii\base\ModelEvent $modelEvent The model event.
	 * @param \yii\db\ActiveRecord $model The model.
	 * @param string[] $excludedAttributes The attributes to be excluded from being logged.
	 * @param string $function The function that is being performed.
	 * 
	 * @return void
	 */
	public function create($modelEvent, $model, $excludedAttributes = [], $function)
	{
		if (Yii::$app instanceof \yii\console\Application) {
			return;
		}
		$user = Yii::$app->user->identity;
		$changedAttributes = $modelEvent->changedAttributes; // array of changed attributes values
		$oldAttributes = $model->getOldAttributes(); // previous attributes values
		$modelClass = (new \ReflectionClass($model))->getShortName(); // model class name

		// for each changed attributes
		foreach ($changedAttributes as $attribute => $newValue) {
			if (in_array($attribute, $excludedAttributes)) { // skip attributes that do not need to log
				continue;
			}
			$humanReadableAttribute = $this->getHumanReadableAttributeName($attribute); // get human-readable attribute name
			$oldValue = (string) $oldAttributes[$attribute];
			$newValue = (string) $changedAttributes[$attribute];
			if ($oldValue === $newValue) { // skip if old and new value are same
				continue;
			}

			$oldValueString = $newValue === '0' ? 'No' : ($newValue === '1' ? 'Yes' : $newValue);
			$newValueString = $oldValue === '0' ? 'No' : ($oldValue === '1' ? 'Yes' : $oldValue);

			// generate remark and create the audit log
			$remark = $this->generateRemark($humanReadableAttribute, $oldValueString, $newValueString, !is_null($user) ? $user : $model, $modelClass);
			if(!empty($user)){
				AuditLog::create([
					'function' => $function, // Use the passed function value
					'old_value' => $oldValueString,
					'value' => $newValueString,
					'remark' => $remark,
					'action_by' => !is_null($user) ? $user->id : $model->id,
				]);
			}
		}
	}

	/**
	 * Takes an attribute name and returns a human-readable version of it.
	 *
	 * @param string $attribute The attribute name to be converted.
	 *
	 * @return string The human-readable attribute name.
	 */
	private function getHumanReadableAttributeName($attribute)
	{
		$replacements = [
			'is_' => '',
			'value' => 'label',
		];

		foreach ($replacements as $search => $replace) {
			$attribute = str_replace($search, $replace, $attribute);
		}

		return $attribute;
	}

	/**
	 * Generate a remark string based on the provided information.
	 *
	 * This function takes in the changed attribute, old value, new value, user who made the change,
	 * and the class name of the model being changed. It then generates a human-readable remark string
	 * that describes the change.
	 *
	 * If the model class matches one of the supported models (ModelA or ModelB), it appends additional
	 * information to the remark string.
	 *
	 * @param string $attribute The changed attribute.
	 * @param string $oldValue The old value of the changed attribute.
	 * @param string $newValue The new value of the changed attribute.
	 * @param \app\models\Admin $user The user who made the change.
	 * @param string $modelClass The class name of the model being changed.
	 *
	 * @return string The generated remark string.
	 */
	private function generateRemark($attribute, $oldValue, $newValue, $user, $modelClass)
	{
		$baseRemark = "User {$user->email} (ID: {$user->id}) changed '{$attribute}' from '{$oldValue}' to '{$newValue}' for {$modelClass} (ID: {$user->id}).";

		switch ($modelClass) {
			case 'ModelA':
				$remark = $baseRemark . " Additional information for ModelA.";
				break;
			case 'ModelB':
				$remark = $baseRemark . " Additional information for ModelB.";
				break;
			default:
				$remark = $baseRemark;
		}

		return $remark;
	}
}
