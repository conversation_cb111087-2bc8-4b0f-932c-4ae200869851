import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

function isStringInCacheValues(searchString) {
    return Object.values(common.enum_key.CACHE_KEYS).includes(searchString);
}

router.get("/", async (req, res) => {
    const token = helper.auth_helper.getBearerToken(req);
    try {
        if (!token) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'Missing Bearer Token.',
                error: true
            });
        }

        if (token !== common.config.secret_config.admin_secret) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'You have no right to access. Please try again.',
                error: true
            });
        }
        const data = helper.cache_helper.getAllCache();
        return res.status(200).json({
            data: data,
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        return res.status(400).json({
            data: {},
            status: 400,
            msg: "Something went wrong. Please try again.",
            error: true
        });
    }
})

router.post("/clear", async (req, res) => {
    const { key } = req.body
    const token = helper.auth_helper.getBearerToken(req);
    try {
        if (!token) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'Missing Bearer Token.',
                error: true
            });
        }
        if (token !== common.config.secret_config.admin_secret) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'You have no right to access. Please try again.',
                error: true
            });
        }
        if (!key) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'Missing parameters. Ensure "key" is available.',
                error: true
            });
        }
        if (!isStringInCacheValues(key)) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'Invalid cache key. Please try again.',
                error: true
            });
        }
        helper.cache_helper.clearCache(key);
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        return res.status(400).json({
            data: {},
            status: 400,
            msg: "Something went wrong. Please try again.",
            error: true
        });
    }
})

export default router;