<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use app\forms\EmailForm;
use app\models\Admin;

class ResetPasswordForm extends Model
{
  public $email;
  public $password;
  public $retype_password;
  public $otp_code;
  public $old_password;

  public function rules()
  {
    return [
      [['email', 'password', 'retype_password', 'otp_code', 'old_password'], 'safe'],
      [['email'], 'required', 'on' => 'request-code'],
      [['email', 'otp_code', 'password', 'retype_password'], 'required', 'on' => 'reset-password'],
      [['email'], 'trim'],
      [['email'], 'string'],
      [['email'], 'email'],
      [['password', 'retype_password'], 'string', 'min' => 8],
      [['retype_password'], 'compare', 'compareAttribute' => 'password'],

      // For BO reset password
      [['password', 'retype_password', 'old_password'], 'required', 'on' => ['update-password']],

    ];
  }

  /**
   * Reset password for the user with the given email address
   * 
   * @throws \Exception
   * @return boolean
   */
  public function resetPassword()
  {
    // get user
    $user = Admin::findOne([
      'email'     => $this->email,
      'is_delete' => 0
    ]);

    if ($this->password != $this->retype_password) {
      throw new \Exception("Password and retype password not match");
    }

    if (!empty($user)) {
      $expire_time = $user->valid_time;
      $today_time  = strtotime("now");

      if ($expire_time < $today_time) {
        throw new \Exception(Yii::t('app', 'Reset password code has been expired'));
      }

      if ($user->otp_code != $this->otp_code) {
        throw new \Exception(Yii::t('app', "Incorrect OTP code."));
      }

      $pass_key = Admin::genPassKey(); // generate a md5 hash as password key

      $user->otp_code     = null;
      $user->password_key = $pass_key;
      $user->password     = Admin::hashPassword(strip_tags($this->password), $pass_key); // hash the password with the password key
      $user->update(false, ['otp_code', 'password', 'password_key']);

      return true;
    }
  }

/**
 * Send email with content to recipient
 *
 * @param string $content Email content
 * @param string $email   Recipient's email
 * @param string $subject Email subject (default is empty string)
 *
 * @throws \Exception
 */
  public function sendEmail($content, $email, $subject = '')
  {
    $model          = new EmailForm;
    $model->email   = $email;
    $model->subject = $subject;
    $model->content = $content;

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    $model->send();
  }

  /**
   * Update current user's password in BO
   * 
   * @param app\models\Admin $user the user object that associated with the current user
   * 
   * @throws \Exception
   */
  public function updateBoPassword($user)
  {
    if ($user->validatePassword($this->old_password)) {
      $pass_key = Admin::genPassKey(); // generate a md5 hash as password key
      $user->password_key = $pass_key;
      $user->password   = Admin::hashPassword(strip_tags($this->password), $pass_key); // hash the password with the password key

      if (!$user->save()) {
        throw new \Exception(current($user->getFirstErrors()));
      }
    } else {
      throw new \Exception(Yii::t('app', "Your old password not same with existing password."), 1);
    }
  }
}
