<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\AuditLog;
use app\models\NoticeLookup;

class NoticeMgmtForm extends Model
{
  public $id;
  public $notice_label;
  public $notice_description;
  public $alex_reward_point;
  public $boost_reward_point;
  public $icon_url;
  public $is_reward;
  public $scenario;

  public function rules()
  {
    return [
      [['notice_label', 'notice_description', 'alex_reward_point', 'boost_reward_point', 'icon_url', 'is_reward', 'scenario'], 'safe'],
      [['notice_label', 'notice_description'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['alex_reward_point', 'boost_reward_point'], 'integer', 'min' => 0],
      [['icon_url'], 'file', 'extensions' => 'png, jpg, jpeg', 'maxSize' => 1024 * 1024 * 1],
      [['notice_label'], 'string', 'max' => 32],
      [['notice_description'], 'string', 'max' => 128],
      [['notice_label', 'notice_description'], 'required', 'on' => ['Create', 'Update']],
    ];
  }

  public function attributeLabels()
  {
    return [
      'notice_label'            => 'Title',
      'notice_description'      => 'Description',
      'alex_reward_point'       => 'Reward AleXs',
      'boost_reward_point'      => 'Reward Boost',
      'icon_url'                => 'Icon',
    ];
  }

  /**
   * Builds the query to retrieve NoticeLookup records
   * 
   * select * from notice_lookup
   * where notice_label like %:notice_label%
   * and is_delete = 0
   * 
   * @return ActiveQuery object
   */
  public function getQuery()
  {
    $query = NoticeLookup::find();

    if (!empty($this->notice_label)) {
      $query->andFilterWhere(['like','notice_label',"%".$this->notice_label."%", false]);
    }

    $query->andWhere(['is_delete' => 0]);
    return $query;
  }

  /**
   * Creates and returns an ActiveDataProvider for paginated and sorted data display
   * 
   * @return ActiveDataProvider
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 20,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Get notice by id
   * 
   * @param int $id
   * 
   * @throws \Exception
   */
  public function getNotice($id)
  {
    $model = NoticeLookup::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Notice not found');
    }

    $this->notice_label             = $model->notice_label;
    $this->notice_description       = $model->notice_description;
    $this->alex_reward_point        = $model->alex_reward_amount * 1;
    $this->boost_reward_point       = $model->boost_reward_amount * 1;
    $this->icon_url                 = $model->icon_image_url;
    $this->is_reward                = $model->is_reward;
  }

  /**
   * Gets notice model by id
   * 
   * @param int $id
   * 
   * @return NoticeLookup
   * 
   * @throws \Exception
   */
  private function getModel($id)
  {
    if ($id > 0) {
      $model = NoticeLookup::findOne([
        'id'        => $id,
        'is_delete' => 0,
      ]);

      if (empty($model)) {
        throw new \Exception('Notice not found');
      }
    } else {
      $model = new NoticeLookup();
    }

    return $model;
  }

  /**
   * Upload icon to S3
   * 
   * Currently not used
   * 
   * @param UploadedFile $icon_file
   * @param string $icon_type
   * 
   * @return string URL of uploaded icon
   */
  protected function uploadIcon($icon_file, $icon_type)
  {
    $file      = $icon_type."-".date("Ymdhis");
    $filename  = Yii::getAlias("@app/runtime/$file");
    $icon_file->saveAs($filename);

    $s3file = Yii::getAlias("@s3-icon/{$file}");
    $type   = mime_content_type($filename);

    $aws = Yii::$app->aws->client();
    $s3  = $aws->createS3();

    $result = $s3->putObject([
      'ACL'         => 'public-read',
      'Bucket'      => Yii::$app->params['s3_bucket'],
      'ContentType' => $type,
      'SourceFile'  => $filename,
      'Key'         => $s3file,
    ]);

    $filepath = $result->get('ObjectURL');
    gc_collect_cycles();
    unlink($filename);

    return $filepath;
  }

  /**
   * Creates or updates a notice
   * 
   * If the notice does not exist, create a new one.
   * If the notice exists, update the notice with the given parameters.
   * 
   * @param int $id The notice ID
   * 
   * @return NoticeLookup The saved notice model
   * 
   * @throws \Exception
   */
  public function createOrUpdateNotice($id = 0)
  {
    $base_model = $this->getModel($id);

    $user = Yii::$app->user->identity;

    // audit log for update notice
    if ($id > 0) {
      if ($base_model->notice_label != $this->notice_label) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_NOTICE,
          'old_value' => $base_model->notice_label,
          'value'     => $this->notice_label,
          'remark'    => 'Update Notice Title',
          'action_by' => $user->id,
        ]);
      }

      if ($base_model->alex_reward_amount != $this->alex_reward_point) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_NOTICE,
          'old_value' => $base_model->alex_reward_amount * 1,
          'value'     => $this->alex_reward_point * 1,
          'remark'    => 'Update Reward AleXs',
          'action_by' => $user->id,
        ]);
      }

      if ($base_model->boost_reward_amount != $this->boost_reward_point) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_NOTICE,
          'old_value' => $base_model->boost_reward_amount * 1,
          'value'     => $this->boost_reward_point * 1,
          'remark'    => 'Update Reward Boost',
          'action_by' => $user->id,
        ]);
      }
    }

    $base_model->notice_label         = $this->notice_label;
    $base_model->notice_description   = $this->notice_description;
    $base_model->alex_reward_amount   = $this->alex_reward_point;
    $base_model->boost_reward_amount  = $this->boost_reward_point;
    $base_model->is_reward            = $this->is_reward ? 1 : 0;

    // TODO: currently icon url does not used, keep for future use (if has)
    if (!empty($this->icon_url) && $this->icon_url != $base_model->icon_image_url) {
      $base_model->icon_image_url = $this->uploadIcon($this->icon_url, 'icon');
    }

    if ($id > 0) {
      $base_model->updated_at = time();

      if (!empty($this->icon_url)) {
        $base_model->update(false, ['notice_label', 'notice_description', 'alex_reward_amount', 'boost_reward_amount', 'is_reward', 'icon_image_url', 'updated_at']);
      } else {
        $base_model->update(false, ['notice_label', 'notice_description', 'alex_reward_amount', 'boost_reward_amount', 'is_reward', 'updated_at']);
      }
    } else { // audit log for create notice
      $base_model->created_at = time();

      if (!$base_model->save()) {
        throw new \Exception(current($base_model->getFirstErrors()));
      }

      AuditLog::create([
        'function'  => AuditLog::MANAGE_SOCIAL_TASK,
        'value'     => $this->notice_label,
        'remark'    => 'Create Notice',
        'action_by' => $user->id,
      ]);
    }

    return $base_model;
  }

  /**
   * Delete notice
   *
   * @param int $id
   * 
   * @return bool
   */
  public function deleteNotice($id)
  {
    $model = NoticeLookup::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      return false;
    }

    $model->is_delete = 1;
    $model->update(false, ['is_delete']);

    return true;
  }
}
