<?php

namespace app\components\report;

use Yii;
use yii\db\ActiveQuery;
use yii\base\Component;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use app\models\User;

class UserManagement extends Component
{
  public $creator;
  public $title;
  public $subject;

  /**
   * Generate an Excel report based on a given ActiveQuery.
   *
   * @param ActiveQuery $query
   * 
   * @return Xlsx
   */
  public function generate(ActiveQuery $query)
  {
    $spreadsheet = new Spreadsheet;
    $spreadsheet->getProperties(); // get the document properties (title, creator, etc)
    $spreadsheet->setActiveSheetIndex(0); // set the active sheet, so Excel opens this as the first sheet
    $sheet = $spreadsheet->getActiveSheet(); // get the current active sheet

    // Set document properties
    $sheet->setCellValue("A1", "Username");
    $sheet->setCellValue("B1", "Telegram ID");
    $sheet->setCellValue("C1", "X ID");
    $sheet->setCellValue("D1", "Instagram ID");
    $sheet->setCellValue("E1", "Is Premium");
    $sheet->setCellValue("F1", "Has Joined Channel");
    $sheet->setCellValue("G1", "Wallet Address");
    $sheet->setCellValue("H1", "Referral Tier Title");
    $sheet->setCellValue("I1", "Accumulate Login");
    $sheet->setCellValue("J1", "Continuous Login");
    $sheet->setCellValue("K1", "Date");

    // Fill data
    $row = 2;
    foreach ($query->each() as $model) {
      $A = empty($model->username) ? $model->first_last_name : $model->username;
      $B = empty($model->user_telegram_id) ? '-' : $model->user_telegram_id;
      $C = empty($model->user_x_id) ? '-' : $model->user_x_id;
      $D = empty($model->user_instagram_id) ? '-' : $model->user_instagram_id;
      $E = $model->is_premium ? Yii::t('app', 'Yes') : Yii::t('app', 'No');
      $F = $model->has_joined_channel ? Yii::t('app', 'Yes') : Yii::t('app', 'No');
      $G = empty($model->wallet_address) ? '-' : $model->wallet_address;
      $H = empty($model->current_referral_ranking) ? '-' : $model->current_referral_ranking;
      $I = empty($model->accumulate_login) ? 0 : $model->accumulate_login;
      $J = empty($model->continuous_login) ? 0 : $model->continuous_login;
      $K = date('Y-m-d H:i:s', $model->created_at);

      $cellValues = [$A, $B, $C, $D, $E, $F, $G, $H, $I, $J, $K];
      $cellLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];

      // Set cell values and styles
      foreach ($cellValues as $index => $value) {
        $cell = $cellLetters[$index] . $row; // cell position, e.g., A2, B2, etc.
        $sheet->setCellValueExplicit($cell, $value, DataType::TYPE_STRING2); // Set value as string to preserve formatting
        $sheet->getStyle($cell)->getAlignment()->setWrapText(true); // Enable text wrapping
        $sheet->getColumnDimension($cellLetters[$index])->setAutoSize(true); // Auto-size columns
      }

      ++$row;
    }

    $spreadsheet->getDefaultStyle()->getFont()->setName('Arial Unicode MS'); // Set default font to Arial Unicode MS

    $writer = new Xlsx($spreadsheet); // Output as Xlsx format
    return $writer;
  }
}
