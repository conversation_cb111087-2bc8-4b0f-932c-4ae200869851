<?php

use yii\web\View;
use yii\helpers\Html;
use yii\widgets\Pjax;
use kartik\grid\GridView;

$this->title  = Yii::t('app', 'Export List');

$this->registerJs('
  function reloadExportList() {
    $.pjax.reload({container:"#export-list-container"});
  }
  setInterval(reloadExportList, 20000);
', View::POS_READY);

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_export_list', [
      'model'   => $model,
      'page'    => 'export-list',
      'pjax_id' => "#pjax-export-list",
    ]); ?>
  </div>

  <?php Pjax::begin(['id' => 'export-list-container']); ?>
    <div class="card-body">
      <?= GridView::widget([
        'dataProvider' => $model->getProvider(),
        'layout'       => '{items}{pager}',
        'tableOptions' => [
          'class' => 'table table-bordered table-hover text-nowrap',
        ],
        'options' => [
          'class' => 'grid-view',
        ],
        'pager' => [
          'class' => '\yii\bootstrap4\LinkPager',
          'options' => [
            'class' => 'mt-3',
          ],
        ],
        'showFooter' => false,
        'striped'    => false,
        'resizableColumns' => false,
        'columns'    => [
          [
            'label' => Yii::t('app', 'Action'),
            'value' => function ($model) {
              return ucwords(str_replace('-', ' ', $model->function));
            }
          ],
          [
            'label' => Yii::t('app', 'Status'),
            'value' => function ($model) {
              return ucfirst($model->status);
            }
          ],
          [
            'label'  => Yii::t('app', 'File'),
            'format' => 'raw',
            'value'  => function ($model) {
              if (empty($model['filepath'])) {
                return "-";
              }

              return Html::a(Yii::t('app', 'Download File'), ['/report/export-list', 'ref_no' => $model->ref_no, 'export' => 1], [
                'class' => 'btn btn-success btn-sm',
                'target' => '_blank',
                'data'   => [
                  'pjax' => '0',
                ],
              ]);
            },
            'headerOptions' => ['style' => 'width:20%'],
          ],
          [
            'label' => Yii::t('app', 'Action By'),
            'value' => function ($model) {
              return $model->actionBy->email;
            },
            'headerOptions' => ['style' => 'width:20%'],
          ],
          [
            'label' => Yii::t('app', 'Date'),
            'value' => function ($model) {
              return date('Y-m-d H:i:s', $model->created_at);
            },
            'headerOptions' => ['style' => 'width:20%'],
          ],
        ]
      ]); ?>
    </div>
  <?php Pjax::end(); ?>
</div>
