<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "song_lookup".
 *
 * @property int $id
 * @property string $song_name
 * @property string|null $song_cover_image
 * @property string|null $song_description
 * @property int $tapping_count
 * @property string $song_type
 * @property int $is_active
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserSongSelectedLookup[] $userSongSelectedLookups
 */
class SongLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'song_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['song_name', 'tapping_count', 'song_type', 'created_at'], 'required'],
            [['song_cover_image', 'song_description'], 'string'],
            [['tapping_count', 'is_active', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['song_name', 'song_type'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'song_name' => 'Song Name',
            'song_cover_image' => 'Song Cover Image',
            'song_description' => 'Song Description',
            'tapping_count' => 'Tapping Count',
            'song_type' => 'Song Type',
            'is_active' => 'Is Active',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserSongSelectedLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['song_id' => 'id']);
    }
}
