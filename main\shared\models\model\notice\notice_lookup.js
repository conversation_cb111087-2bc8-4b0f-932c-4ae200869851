export default function (sequelize, DataTypes) {
    return sequelize.define('notice_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        notice_label: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        notice_description: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        icon_image_url: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        alex_reward_amount: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        boost_reward_amount: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        is_reward: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'notice_lookup',
        timestamps: false,
    })
}