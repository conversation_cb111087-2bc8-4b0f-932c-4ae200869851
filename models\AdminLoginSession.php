<?php

namespace app\models;

use Yii;
use Detection\MobileDetect;

/**
 * This is the model class for table "admin_login_session".
 *
 * @property int $id
 * @property int|null $admin_id
 * @property string|null $action
 * @property string|null $ip_address
 * @property string|null $country_name
 * @property string|null $device_type
 * @property string|null $user_agent
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Admin $admin
 */
class AdminLoginSession extends \yii\db\ActiveRecord
{
    const DEVICE_BROWSER = 'Browser';
    const DEVICE_ANDROID = 'Android';
    const DEVICE_IOS     = 'iOS';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin_login_session';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['admin_id', 'ip_address', 'country_name', 'device_type', 'user_agent', 'updated_at'], 'default', 'value' => null],
            [['action'], 'default', 'value' => 'login'],
            [['is_delete'], 'default', 'value' => 0],
            [['admin_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_agent'], 'string'],
            [['created_at'], 'required'],
            [['action'], 'string', 'max' => 250],
            [['ip_address', 'country_name'], 'string', 'max' => 100],
            [['device_type'], 'string', 'max' => 50],
            [['admin_id'], 'exist', 'skipOnError' => true, 'targetClass' => Admin::class, 'targetAttribute' => ['admin_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'admin_id' => 'Admin ID',
            'action' => 'Action',
            'ip_address' => 'Ip Address',
            'country_name' => 'Country Name',
            'device_type' => 'Device Type',
            'user_agent' => 'User Agent',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Admin]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdmin()
    {
        return $this->hasOne(Admin::className(), ['id' => 'admin_id']);
    }

    
    /**
     * Detect the device type.
     *
     * @return string
     */
    
    public static function detectDeviceType()
    {
        $detect = new MobileDetect;
        $device = self::DEVICE_BROWSER;

        if ($detect->isiOS()) {
            $device = self::DEVICE_IOS;
        }

        if ($detect->isAndroidOS()) {
            $device = self::DEVICE_ANDROID;
        }

        return $device;
    }

    
    /**
     * Get the country name by IP address.
     * 
     * @param string $ip_address IP address.
     * 
     * @return string Country name.
     */
    public static function getCountryIpAddr($ip_address = "")
    {
        $country_ip  = "http://geoip.fun1881.com/api/" . $ip_address;

        try {
            // Send a request to the geoip API
            $country = Yii::$app->api->request($country_ip, "GET");
            if (!empty($country) && $country['status'] == 'success') {
                $country_name = Country::find()
                    ->andWhere(['iso' => $country['country']['code']])
                    ->andWhere(['is_delete' => 0])
                    ->one();

                if (!empty($country_name)) {
                    return $country_name->nicename;
                } else {
                    return '';
                }
            }
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Record admin login session.
     *
     * @param int $user_id User ID.
     * @param string|null $action Action name.
     * @param string|null $user_agent User agent.
     *
     * @return AdminLoginSession
     *
     * @throws \Exception
     */
    public static function record($user_id, $action = null, $user_agent = null)
    {
        $model      = new AdminLoginSession;
        $ip_address = Yii::$app->request->userIP;
        $device     = self::detectDeviceType();

        $model->admin_id = $user_id;

        if (!empty($action)) {
            $model->action   = $action;
        }

        $model->ip_address   = $ip_address;
        $model->country_name = self::getCountryIpAddr($ip_address);
        $model->user_agent   = $user_agent;
        $model->device_type  = $device;
        $model->created_at   = time();

        if (!$model->save()) {
            throw new \Exception(current($model->getFirstErrors()));
        }

        return $model;
    }

}
