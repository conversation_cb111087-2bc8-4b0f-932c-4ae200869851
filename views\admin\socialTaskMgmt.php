<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;

use app\models\SocialTaskLookup;

$this->title = Yii::t('app', 'Social Task Management');

$this->registerCss('
  .alert-info label {
    margin-bottom: 0;
  }
  ul {
    margin-bottom: 0;
  }
');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_social_task', [
      'model'   => $model,
      'page'    => 'social-task-mgmt',
      'pjax_id' => "#pjax-social-task-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="alert alert-info mb-3">
      <i class="icon fas fa-exclamation-triangle"></i><label><?= Yii::t('app', 'Note'); ?></label>
      <ul>
        <li><?= Yii::t('app', 'Modifications to tasks, such as adding, editing, or removing, could require up to 3 hours to update within the Telegram application'); ?></li>
        <li><?= Yii::t('app', 'Click the "Clear Cache" button to immediately refresh the earn task list'); ?></li>
      </ul>
    </div>

    <div class="text-right mb-3">
      <?= Html::a(Yii::t('app', 'Clear Cache'), ['admin/refresh-social-task'], [
        'class' => 'btn btn-info',
      ]); ?>
      <?= Html::a(Yii::t('app', 'Create'), ['admin/create-social-task'], [
        'class' => 'btn btn-success',
      ]); ?>
    </div>

    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app','Task Name'),
          'value' => function ($model) {
            return $model->task_name;
          },
        ],
        [
          'label' => Yii::t('app','Type'),
          'value' => function ($model) {
            return SocialTaskLookup::getType($model->type);
          },
        ],
        [
          'label'  => Yii::t('app','Icon'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->icon_image_url)) {
              return '-';
            }

            return Html::img($model->icon_image_url, ['width' => 50]);
          },
        ],
        [
          'label' => Yii::t('app','Reward AleXs'),
          'value' => function ($model) {
            return $model->reward_point;
          },
        ],
        [
          'label' => Yii::t('app','Redirect URL'),
          'value' => function ($model) {
            if (empty($model->redirect_url)) {
              return '-';
            }

            return $model->redirect_url;
          },
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
        [
          'class'    => 'yii\grid\ActionColumn',
          'template' => '
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                Action <span class="dropdown-icon"></span>
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu" role="menu">
                {update}
                {delete}
              </div>
            </div>
          ',
          'buttons' => [
            'update' => function ($url, $model) {
              return Html::a(Yii::t('app','Update'), ['admin/update-social-task', 'id' => $model->id], [
                'class' => 'dropdown-item',
              ]);
            },
            'delete' => function ($url, $model) {
              return Html::a(Yii::t('app','Delete'), ['admin/delete-social-task', 'id' => $model->id], [
                'class' => 'dropdown-item',
                'data'  => [
                  'confirm' => Yii::t('app', 'Are you sure you want to delete this task?'),
                  'method'  => 'post',
                ],
              ]);
            },
          ],
        ],
      ]
    ]); ?>
  </div>
</div>
