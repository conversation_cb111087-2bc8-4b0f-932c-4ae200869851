<?php

use yii\helpers\Html;
use yii\widgets\Breadcrumbs;
use yii\bootstrap4\ActiveForm;

$this->title = Yii::t('app', 'Manage Setting');

?>

<div class="card">
  <?php $form = ActiveForm::begin([
    'method' => 'post',
    'options' => ['data-pjax' => true],
    'fieldConfig' => [
      'labelOptions' => [
        'class' => '',
      ],
      'inputOptions' => [
        'class' => 'input-sm form-control',
      ],
    ],
  ]); ?>

  <div class="card-body">
    <?= $this->render('/site/_alert_flash', []) ?>
    <div class="card card-outline card-primary">
      <div class="card-header">
        <h3 class="card-title">Explorer Link</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
            <?= $form->field($model, 'ton_explorer')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Ton Explorer')) ?>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Update'), [
      'class' => 'btn btn-success float-right',
      'name'  => 'Update',
      'value' => 1,
    ]) ?>
  </div>

  <?php ActiveForm::end(); ?>
</div>