<?php

use yii\helpers\Html;
use yii\grid\GridView;

$this->title = Yii::t('app', 'Subadmin Management');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <div class="text-right mb-3">
      <?= Html::a(Yii::t('app', 'Create Subadmin'), ['admin/create-sub-admin'], [
        'class' => 'btn btn-success',
      ]); ?>
    </div>
    <?= $this->render('_search_subadmin', [
      'model'   => $model,
      'page'    => 'sub-admin-mgmt',
      'pjax_id' => "#pjax-subadmin-management",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getSubAdminProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'columns'    => [
        [
          'label' => 'Username',
          'value' => function ($model) {
            if (empty($model->username))
              return "-";
            return $model->username;
          },
        ],
        [
          'label' => 'Email',
          'value' => function ($model) {
            if (empty($model->email))
              return "-";
            return $model->email;
          },
        ],
        [
          'label' => 'Status',
          'value' => function ($model) {
            if ($model->is_suspend == 1)
              return Yii::t('app', 'Suspended');
            return Yii::t('app', 'Active');
          },
        ],
        [
          'attribute' => 'created_at',
          'value' => function ($model) {
            return date('d-m-Y H:i:s', $model->created_at);
          },
        ],
        [
          'class'    => 'yii\grid\ActionColumn',
          'template' => '
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                Action <span class="dropdown-icon"></span>
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu" role="menu">
                {profile}
                {permission}
                {suspend}
              </div>
            </div>
          ',
          'buttons'  => [
            'profile'  => function ($url, $model) {
              return Html::a(Yii::t('app', 'Profile'), ['admin/sub-admin-profile', 'id' => $model->id], [
                'class' => 'dropdown-item',
              ]);
            },
            'permission'  => function ($url, $model) {
              if (Yii::$app->user->identity->role != 'subadmin') {
                return Html::a(Yii::t('app', 'Permission'), ['admin/manage-permission', 'id' => $model->id], [
                  'class' => 'dropdown-item',
                ]);
              }
            },
            'suspend'  => function ($url, $model) {
              if ($model->is_suspend == 1) {
                return Html::a(Yii::t('app', 'Unsuspend'), ['admin/suspend-sub-admin', 'id' => $model->id], [
                  'class' => 'dropdown-item',
                ]);
              } else {
                return Html::a(Yii::t('app', 'Suspend'), ['admin/suspend-sub-admin', 'id' => $model->id], [
                  'class' => 'dropdown-item',
                ]);
              }
            },
          ],
        ],
      ]
    ]); ?>
  </div>
</div>