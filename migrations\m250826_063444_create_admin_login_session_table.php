<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%admin_login_session}}`.
 */
class m250826_063444_create_admin_login_session_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%admin_login_session}}', [
            "id"            => $this->primaryKey(),
            "admin_id"      => "int",
            "action"        => "varchar(250) default 'login'",
            "ip_address"    => "varchar(100)",
            "country_name"  => "varchar(100)",
            "device_type"   => "varchar(50)",
            "user_agent"    => "text",
            'created_at'    => $this->bigInteger()->notNull(),
            'updated_at'    => $this->bigInteger(),
            'is_delete'     => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`admin_id`) references `admin` (`id`)',
            "key loginAction  (`action`,`is_delete`,`created_at`)",
            "key countryLogin (`country_name`,`is_delete`,`created_at`)",
            "key deviceLogin  (`device_type`,`is_delete`,`created_at`)",
            "key checkIp      (`ip_address`,`is_delete`,`created_at`)",
            "key deviceUsage  (`device_type`,`is_delete`)",
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%admin_login_session}}');
    }
}
