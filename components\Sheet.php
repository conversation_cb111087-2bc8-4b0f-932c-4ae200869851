<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use yii\helpers\ArrayHelper;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\txt;

class Sheet extends Component
{
	public $header;
	public $body;
	public $model;
	public $type;
	public $file;
	public $fileName;

	/**
	 * Load data from an excel file.
	 *
	 * @param string $file
	 * @param boolean $formula. If true, load formula. If false, load formula result.
	 * 
	 * @return array
	 */
	public function loadData($file, $formula=true)
	{
		$reader = IOFactory::createReaderForFile($file);
		$reader->setReadDataOnly(false);
		$reader->setReadEmptyCells(false);
		$spreadSheet = $reader->load($file);
		$data = $spreadSheet->getActiveSheet()->toArray(null, $formula, true, false);

		return $data;
	}

	/**
	 * Create an excel file based on $header and $body.
	 * 
	 * get sheet data > create Header > create Body > generate as xlsx file 
	 *
	 * @return void
	 */
	public function create()
	{
		$spreadsheet = new Spreadsheet();
		$sheet = $spreadsheet->getActiveSheet();
		$this->findFileOrCreate();
		$this->createHeader($sheet);
		$this->createBody($sheet);
		// $this->XlsFile($spreadsheet);
		$this->XlsxFile($spreadsheet);
	}

	/**
	 * Create header of excel file.
	 *
	 * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
	 */
	protected function createHeader($sheet)
	{
		$alphas = range('A','Z');
		$max = 26; // Max handle 50 column only
		foreach ($this->getHeader() as $key => $value) {
			if($key > ($max - 1)) { // use AA - AZ after 26 columns
				$sheet->setCellValue("A{$alphas[$key - $max]}1", $value);
			}else { // use A - Z within 26 columns
				$sheet->setCellValue("{$alphas[$key]}1", $value);
			}
		}
	}

/**
 * Create body of excel file.
 *
 * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
 */
	protected function createBody($sheet)
	{
		$alphas = range('A','Z');
		$max = 26; // Max handle 50 column only
		$position = 2; // start from second row
		foreach ($this->getModel() as $key => $value) {
			foreach ($this->getBody() as $cKey => $cValue) {
				if($cKey > ($max - 1)) { // use AA - AZ after 26 columns, value's data type is string
					$sheet->setCellValueExplicit("A{$alphas[$cKey - $max]}".$position, $value[$cValue], DataType::TYPE_STRING2); 
				}else { // use A - Z within 26 columns, values's data type is string
					$sheet->setCellValueExplicit("{$alphas[$cKey]}".$position, $value[$cValue], DataType::TYPE_STRING2); 
				}
			}
			++$position;
		}

		$this->fileName = "{$this->type}-".date('Ymd-Hi').".xlsx";
		$this->file = Yii::getAlias("@runtime/export/{$this->fileName}");
	}

	/**
	 * Write the spreadsheet to an XLS file
	 * 
	 * @param Spreadsheet $spreadsheet
	 */
	protected function XlsFile($spreadsheet)
	{
		$writer = new Xls($spreadsheet);
		$writer->save($this->file);
	}

	/**
	 * Write the spreadsheet to an XLSX file
	 * 
	 * @param Spreadsheet $spreadsheet
	 */
	protected function XlsxFile($spreadsheet)
	{
		$writer = new Xlsx($spreadsheet);
		$writer->save($this->file);
	}

	/**
	 * Find the export folder or create it if it is not exists
	 */
	protected function findFileOrCreate()
	{
		$base = Yii::getAlias("@runtime");
		if(!file_exists("{$base}/export")){
			mkdir("{$base}/export");
		}
	}

	/**
	 * Set the model of the report
	 * 
	 * @param yii\db\ActiveRecord $model
	 * 
	 * @return static
	 */
	public function setModel($model)
	{
		$this->model = $model;
	}

	/**
	 * Get the model of the report
	 * 
	 * @return yii\db\ActiveRecord
	 */
	public function getModel()
	{
		return $this->model;
	}

	/**
	 * Set the header of the report
	 * 
	 * @param array $header
	 */
	public function setHeader($header)
	{
		$this->header = $header;
	}

	/**
	 * Get the header of the report
	 * 
	 * @return array
	 */
	public function getHeader()
	{
		return $this->header;
	}

	/**
	 * Set the body of the report
	 * 
	 * @param array $body
	 */
	public function setBody($body)
	{
		$this->body = $body;
	}

	/**
	 * Get the body of the report
	 * 
	 * @return array
	 */
	public function getBody()
	{
		return $this->body;
	}
}
