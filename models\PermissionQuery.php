<?php

namespace app\models;

use Yii;
use yii\base\Model;

class PermissionQuery extends Model
{
  const REPORT = 'report';
  
  const SUB_ADMIN = 'sub-admin';
  const USER_MANAGEMENT = 'user-management';
  const REFERRAL_MANAGEMENT = 'referral-management';
  const SOCIAL_TASK_MANAGEMENT = 'social-task-management';
  const SYSTEM_SETTING = 'system-setting';
  const TIER_LIST_MANAGEMENT = 'tier-list-management';
  const NOTICE_MANAGEMENT = 'notice-management';
  const STAGE_MANAGEMENT = 'stage-management';
  const BUNDLE_MANAGEMENT = 'bundle-management';
  //const WHITELIST_USER = 'whitelist-user';
  //const TRANSLATION_MANAGEMENT = 'translation-management';
  //const ALERT_EMAIL_MANAGEMENT = 'alert-email-management';

  private static function generatePermission($permissions)
  {
    $permission = [];

    foreach ($permissions as $perm) {
      $permission[$perm['name']]['name'] = $perm['name'];
      $permission[$perm['name']]['desc'] = $perm['desc'];
      $permission[$perm['name']]['group'] = isset($perm['group']) ? $perm['group'] : 'admin';
      $permission[$perm['name']]['is_enable'] = isset($perm['is_enable']) ? $perm['is_enable'] : 1;
    }

    return $permission;
  }

  public static function getAllPermission()
  {
    $permissions = [
      ['name' => self::REPORT, 'desc' => 'View report'],
      ['name' => self::SUB_ADMIN, 'desc' => 'Manage subadmin'],
      ['name' => self::USER_MANAGEMENT, 'desc' => 'Manage user'],
      ['name' => self::REFERRAL_MANAGEMENT, 'desc' => 'Manage referral'],
      ['name' => self::SOCIAL_TASK_MANAGEMENT, 'desc' => 'Manage social task'],
      ['name' => self::SYSTEM_SETTING, 'desc' => 'Manage system setting'],
      ['name' => self::TIER_LIST_MANAGEMENT, 'desc' => 'Manage tier list'],
      ['name' => self::NOTICE_MANAGEMENT, 'desc' => 'Manage notice'],
      ['name' => self::STAGE_MANAGEMENT, 'desc' => 'Manage stage'],
      ['name' => self::BUNDLE_MANAGEMENT, 'desc' => 'Manage bundle'],
      //['name' => self::WHITELIST_USER, 'desc' => 'Manage whitelist user'],
      //['name' => self::TRANSLATION_MANAGEMENT, 'desc' => 'Manage translation'],
      //['name' => self::ALERT_EMAIL_MANAGEMENT, 'desc' => 'Alert Email Management'],
    ];

    return self::generatePermission($permissions);
  }

  public static function getSubAdminPermission()
  {
    $permissions = [
      ['name' => self::REPORT, 'desc' => 'View report'],
      
      ['name' => self::USER_MANAGEMENT, 'desc' => 'Manage user'],
      ['name' => self::REFERRAL_MANAGEMENT, 'desc' => 'Manage referral'],
      ['name' => self::SOCIAL_TASK_MANAGEMENT, 'desc' => 'Manage social task'],
      ['name' => self::SYSTEM_SETTING, 'desc' => 'Manage system setting'],
      ['name' => self::TIER_LIST_MANAGEMENT, 'desc' => 'Manage tier list'],
      ['name' => self::NOTICE_MANAGEMENT, 'desc' => 'Manage notice'],
      //['name' => self::WHITELIST_USER, 'desc' => 'Manage whitelist user'],
      //['name' => self::TRANSLATION_MANAGEMENT, 'desc' => 'Manage translation'],
      //['name' => self::ALERT_EMAIL_MANAGEMENT, 'desc' => 'Alert Email Management'],
    ];

    return self::generatePermission($permissions);
  }
}