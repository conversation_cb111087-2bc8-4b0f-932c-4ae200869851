<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "notice_lookup".
 *
 * @property int $id
 * @property string $notice_label
 * @property string|null $notice_description
 * @property string $icon_image_url
 * @property int|null $alex_reward_amount
 * @property int|null $boost_reward_amount
 * @property int $is_reward
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserNoticeReward[] $userNoticeRewards
 */
class NoticeLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'notice_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['notice_label', 'created_at'], 'required'],
            [['icon_image_url'], 'string'],
            [['alex_reward_amount', 'boost_reward_amount', 'is_reward', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['notice_label', 'notice_description'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'notice_label' => 'Notice Label',
            'notice_description' => 'Notice Description',
            'icon_image_url' => 'Icon Image Url',
            'alex_reward_amount' => 'Alex Reward Amount',
            'boost_reward_amount' => 'Boost Reward Amount',
            'is_reward' => 'Is Reward',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserNoticeRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserNoticeRewards()
    {
        return $this->hasMany(UserNoticeReward::className(), ['notice_id' => 'id']);
    }
}
