<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_login_session}}`.
 */
class m250829_070716_creata_user_login_session_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_login_session}}', [
            'id'             => $this->primaryKey(),
            'user_id'      => $this->integer()->notNull(),
            'time'           => $this->string(255)->notNull(),
            'day_month_year' => $this->string(255)->notNull(),
            'created_at'     => $this->bigInteger()->notNull(),
            'updated_at'     => $this->bigInteger(),
            'is_delete'      => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findUser` (`user_id`, `is_delete`)',
            'key `findByTime` (`time`, `is_delete`)',
            'key `findByDayMonthYear` (`day_month_year`, `is_delete`)',
            'key `findByUserAndTime` (`user_id`, `time`, `is_delete`)',
            'key `findByUserAndDayMonthYear` (`user_id`, `day_month_year`, `is_delete`)',
            'key `findByCreatedDate` (`created_at`, `is_delete`)',
        ]);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_login_session}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250829_070716_creata_user_login_session_table cannot be reverted.\n";

        return false;
    }
    */
}
