import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";
import { Api } from "grammy";
const api = new Api(common.config.telegram.token);
import s3 from "../../shared/helpers/storage/s3.js";

async function getUserList() {
    console.log("Come in Get User List")
    let users = await query.models.user.findAll({
        where: { profile_pic: null, is_delete: 0, user_telegram_id: { [query.Op.ne]: null } }, order: [['created_at', 'ASC']],
    });

    return users;
}

async function getTeleProfilePic(user_telegram_id) {
    try {
        console.log("Come in Get Tele Profile Pic")
        const userProfilePhotos = await api.getUserProfilePhotos(user_telegram_id);
        if (userProfilePhotos.total_count === 0) return null;
        const file_id = userProfilePhotos.photos[0][0].file_id;
        const file = await api.getFile(file_id);
        const file_url = `https://api.telegram.org/file/bot${common.config.telegram.token}/${file.file_path}`;
        return file_url;
    } catch (error) {
        return null;
    }
}

import axios from 'axios';
const downloadImageToBuffer = async (url) => {
    console.log("Come in Download Image to Buffer")
    try {
        const response = await axios({
            url,
            method: 'GET',
            responseType: 'arraybuffer',
        });

        return Buffer.from(response.data, 'binary');
    } catch (error) {
        // console.error('Error downloading the image:', error);
        return null;
    }
};

async function main(retry_delay) {
    while (true) {
        try {
            const user_list = await getUserList();

            for (const element of user_list) {
                const transaction = await query.sequelize.transaction();
                try {
                    console.log("User ID : ", element.user_telegram_id)
                    const profile_pic_url = await getTeleProfilePic(element.user_telegram_id)
                    console.log("Profile Pic URL : ", profile_pic_url)
                    if (!profile_pic_url) {
                        await common.util_helper.wait_stopper(1500)
                        await transaction.rollback();
                        continue;
                    };
                    await common.util_helper.wait_stopper(1000)

                    const image_buffer = await downloadImageToBuffer(profile_pic_url)
                    if (!image_buffer) {
                        console.log("NULL IMAGE BUFFER")
                        await transaction.rollback();
                        continue;
                    };
                    await common.util_helper.wait_stopper(1000)

                    const path = `${common.config.s3_bucket.folder_path}${common.config.environment}/${element.user_telegram_id}.png`
                    await s3.put(path, image_buffer, {
                        ACL: 'public-read',
                    })
                    const image_url = s3.getUrl(path)
                    console.log("Image URL generated : ", image_url)
                    await query.models.user.update(
                        { profile_pic: image_url, updated_at: common.util_helper.getCurrentEpochTime() },
                        { where: { id: element.id, }, transaction: transaction },
                    );
                    await transaction.commit();
                    console.log(`#@#@#@#@#@#@#@#@#@#@#@#@# FINISHED with User ID : ${element.id}. Continue in 3 seconds @#@#@#@#@#@#@#@#@#@#@#@#@#`)
                    await common.util_helper.wait_stopper(3000)
                } catch (error) {
                    await transaction.rollback();
                    throw new Error(`Error occured in loop with : ${error.message}`);
                }
            }

            common.util_helper.spacer(2)
            if (user_list.length === 0) {
                console.log(`============ No user with empty profile pic, Continue new loop in ${retry_delay / 1000} seconds =============`);
            }

            console.log(`============ Finish Loop, new process start in ${retry_delay / 1000} seconds =============`);
            await common.util_helper.wait_stopper(retry_delay)
        } catch (error) {
            console.error(error);
            console.error(`Error occured (${common.moment().format('YYYY-MM-DD HH:mm:ss')}) : `, error);
            console.log(`============ Error, Retrying in ${retry_delay / 1000} seconds =============`);
            await common.util_helper.wait_stopper(retry_delay)
        }
    }
}


const retry_delay = 200 * 1000; //200 seconds
main(retry_delay).catch(console.error);