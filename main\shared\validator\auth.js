import { query, body } from "express-validator";

const tg_clicker_validator = [
    query("bot").exists().withMessage("bot is required").bail().isString().withMessage("bot must be a string"),
    query("tguserid").exists().withMessage("tguserid is required").bail().isInt().withMessage("tguserid must be an integer"),
    query("sign").exists().withMessage("sign is required").bail().isString().withMessage("sign must be a string"),
]

const tg_direct_link_validator = [
    query("search_params").exists().withMessage("search_params is required").bail().isString().withMessage("search_params must be a string"),
]

const wallet_validator = [
    body("wallet_address").exists().withMessage("wallet_address is required").bail()
        .isString().withMessage("wallet_address must be a string").bail()
        .notEmpty().withMessage("wallet_address cannot be empty string"),
]

const x_instagram_validator = [
    body("account_id").exists().withMessage("account_id is required").bail()
        .isString().withMessage("account_id must be a string").bail()
        .notEmpty().withMessage("account_id cannot be empty string"),
]

export default {
    tg_clicker_validator,
    tg_direct_link_validator,
    wallet_validator,
    x_instagram_validator
}