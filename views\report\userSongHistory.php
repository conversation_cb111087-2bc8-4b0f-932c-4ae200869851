<?php

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'User Song History');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_user_song_history', [
      'model'   => $model,
      'page'    => 'user-song-history',
      'pjax_id' => "#pjax-user-song-history",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app','Username'),
          'format' => 'raw',
          'value' => function ($model) {
            $username = $model->user->username;
            $telegram_id = $model->user->user_telegram_id;
            if (empty($model->user->username)) {
              $username = $model->user->first_last_name;
            }

            if (empty($model->user->user_telegram_id)) {
              $telegram_id = '-';
            }

            $display = "Username : ". $username . "<br>" . "Telegram ID : " . $telegram_id;

            return $display;
          },
        ],
        [
          'label'  => Yii::t('app','Wallet Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->user->user->wallet_address)) {
              return '-';
            }

            $link = Yii::$app->formatHelper->formatTon('address', $model->user->wallet_address, 5);

            return $link;
          },
        ],
        [
          'label'  => Yii::t('app','Genre Type'),
          'format' => 'raw',
          'value'  => function ($model) {
            return ucfirst($model->genre->genre_type);
          },
        ],
        [
          'label'  => Yii::t('app','Genre Name'),
          'format' => 'raw',
          'value'  => function ($model) {
            return $model->genre->genre_name;
          },
        ],
        [
          'label' => Yii::t('app','Song Name'),
          'value' => function ($model) {
            return $model->song->song_name;
          },
        ],
        [
          'label' => Yii::t('app','Tap Count'),
          'value' => function ($model) {
            return $model->tap_count;
          }
        ],
        [
          'label'  => yii::t('app', 'Equiped Instrument Cards'),
          'format' => 'raw',
          'value'  => function ($model) {
            $instruments_name = [
              1 => $model->boostInstrumentShopCardOne ? $model->boostInstrumentShopCardOne->instrument->instrument_name : ' - ',
              2 => $model->boostInstrumentShopCardTwo ? $model->boostInstrumentShopCardTwo->instrument->instrument_name : ' - ',
              3 => $model->boostInstrumentShopCardThree ? $model->boostInstrumentShopCardThree->instrument->instrument_name : ' - ',
              4 => $model->boostInstrumentShopCardFour ? $model->boostInstrumentShopCardFour->instrument->instrument_name : ' - ',
            ];

            $instruments_rarity = [
              1 => $model->boostInstrumentShopCardOne ? $model->boostInstrumentShopCardOne->rarity->rarity_name : ' - ',
              2 => $model->boostInstrumentShopCardTwo ? $model->boostInstrumentShopCardTwo->rarity->rarity_name : '- ',
              3 => $model->boostInstrumentShopCardThree ? $model->boostInstrumentShopCardThree->rarity->rarity_name : ' - ',
              4 => $model->boostInstrumentShopCardFour ? $model->boostInstrumentShopCardFour->rarity->rarity_name : ' - ',
            ];

            $equiped_card = [];
            foreach ($instruments_name as $key => $name) {
                if ($name != ' - ') {
                    $equiped_card[] = $key . ': ' . $name . ' (' . $instruments_rarity[$key] . ')';
                } else {
                    $equiped_card[] = $key . ': -';
                }
            }
            return implode('<br>', $equiped_card);
          },
        ],
        [
          'label'  => Yii::t('app','Used Shop Items'),
          'format' => 'raw',
          'value'  => function ($model) {
            $items_name = [
              1 => $model->expBoosterUserShopItem ? $model->expBoosterUserShopItem->item->item_name : ' - ',
              2 => $model->tokenBoosterUserShopItem ? $model->tokenBoosterUserShopItem->item->item_name : ' - ',
              3 => $model->tapBoosterUserShopItem ? $model->tapBoosterUserShopItem->item->item_name : ' - ',
            ];

            $equiped_item = [];
            foreach ($items_name as $key => $name) {
                if (!empty($name)) {
                    $equiped_item[] = $key . ': ' . $name;
                }
            }
            return !empty($equiped_item) ? implode('<br>', $equiped_item) : '-';
            
          },
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
      ]
    ]); ?>
  </div>
</div>
