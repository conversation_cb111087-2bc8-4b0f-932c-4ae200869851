// models/song_lookup.js

export default function (sequelize, DataTypes) {
    return sequelize.define('song_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        song_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        song_cover_image: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        song_description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        song_type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "advanced"
        },
        tapping_count: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'song_lookup',
        timestamps: false,
    });
}