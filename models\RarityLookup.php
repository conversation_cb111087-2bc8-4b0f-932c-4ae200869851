<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "rarity_lookup".
 *
 * @property int $id
 * @property string $rarity_name
 * @property int|null $upgrade_alex_spending
 * @property float|null $success_rate_percentage
 * @property int $created_at
 * @property int|null $updated_at
 * @property int|null $secondary_updated_at
 * @property int $is_delete
 *
 * @property ShopCardLookup[] $shopCardLookups
 * @property UserShopCard[] $userShopCards
 */
class RarityLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'rarity_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rarity_name', 'created_at'], 'required'],
            [['upgrade_alex_spending', 'created_at', 'updated_at', 'secondary_updated_at', 'is_delete'], 'integer'],
            [['success_rate_percentage'], 'number'],
            [['rarity_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rarity_name' => 'Rarity Name',
            'upgrade_alex_spending' => 'Upgrade Alex Spending',
            'success_rate_percentage' => 'Success Rate Percentage',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'secondary_updated_at' => 'Secondary Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[ShopCardLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getShopCardLookups()
    {
        return $this->hasMany(ShopCardLookup::className(), ['rarity_id' => 'id']);
    }

    /**
     * Gets query for [[UserShopCards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserShopCards()
    {
        return $this->hasMany(UserShopCard::className(), ['rarity_id' => 'id']);
    }

    /**
     * Gets all rarities as an associative array.
     *
     * Select id, rarity_name from rarity_lookup
     * where is_delete = 0
     * order by id ASC
     *
     * @return array An associative array of rarities.
     */
    public static function getAllRarity()
    {
        $query = self::find()
            ->select([
                'id',
                'rarity_name'
            ])
            ->where(['is_delete' => 0])
            ->orderBy('id ASC')
            ->all();
        
        // capitalized first letter of every word
        foreach ($query as $item) {
            $item->rarity_name = ucfirst($item->rarity_name);
        }

        return ArrayHelper::map($query, 'rarity_name', 'rarity_name');
    }
}
