<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;

use app\models\SocialTaskLookup;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'task_name')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Title')) ?>
      <?= $form->field($model, 'type')->dropDownList(SocialTaskLookup::getType(), [
        'prompt' => Yii::t('app', 'Select Type'),
      ])->label(Yii::t('app', 'Type')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-warning float-right']) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>
