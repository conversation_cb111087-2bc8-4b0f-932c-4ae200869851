import common from "../shared/imports/common.js";
import server from "../shared/imports/server.js";
import helper from "../shared/imports/helper.js";
import controller from "../shared/imports/controller.js";
import session from "express-session";
import http from "http";
import logger from "morgan"
import cors from "cors"
import passport from "passport"

const app = server.express();
const port = common.config.port;

app.set("port", port);
app.set("trust proxy", 2);
app.use(passport.initialize());
// app.use(
//     session({
//         secret: common.config.secret_config.session_secret,
//         saveUninitialized: false,
//         resave: false,
//     })
// );
app.use(cors())
app.use(logger('dev'));
app.use(server.express.json());
app.use(server.express.urlencoded({ extended: false }));

const server_express = http.createServer(app)
server_express.listen(port)
server_express.on('error', function (error) {
    {
        if (error.syscall !== 'listen') {
            throw error
        }
        var bind = typeof port === 'string'
            ? 'Pipe ' + port
            : 'Port ' + port

        // handle specific listen errors with friendly messages
        switch (error.code) {
            case 'EACCES':
                console.error(bind + ' requires elevated privileges')
                process.exit(1)
                break
            case 'EADDRINUSE':
                console.error(bind + ' is already in use')
                process.exit(1)
                break
            default:
                throw error
        }
    }
})
server_express.on('listening', function () {
    const addr = server_express.address()
    const bind = typeof addr === 'string'
        ? 'pipe ' + addr
        : 'port ' + addr.port
    common.util_helper.spacer(40)
    console.log('Server Running ->>> Listening on ' + bind)
})




//Passport Strategy
passport.use(helper.passport_helper.jwt.name, helper.passport_helper.jwt.strategy);

//API ENDPOINTS
app.get("/healthcheck", (req, res) => {
    const healthcheck = {
        uptime: process.uptime(),
        msg: 'OK',
        timestamp: Date.now(),
    };
    try {
        return res.status(200).send(healthcheck);
    } catch (error) {
        console.error("Health check failed:", error);
        return res.status(503).send({ msg: "Service Unavailable" });
    }
});
app.use("/auth", controller.authController)
app.use("/user", controller.userController)
app.use("/task", controller.taskController)
app.use("/profile", controller.profileController)
app.use("/notice", controller.noticeController)
app.use("/referral", controller.referralController)
app.use("/platform", controller.platformController)
app.use("/cache", controller.cacheController)
app.use("/presale", controller.presaleController)

//v1
import v1Controller from "../apis/controllers/v1/v1.js"
app.use("/v1", v1Controller)