import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();


// router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => { })

router.get("/direct-downlines", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    try {
        const get_referral_downline = await query.models.user_referral.findAll({
            where: {
                referral_user_id: user_id,
                is_delete: false
            },
            include: [
                {
                    model: query.models.user,
                    where: { is_delete: 0 },
                    required: true,
                },
            ],
            transaction: transaction,
            nest: true
        })

        const data = {
            direct_referral_list: get_referral_downline.map(element => {
                return {
                    username: element.user.username,
                    first_last_name: element.user.first_last_name,
                    created_at: element.created_at
                }
            }),
            count: get_referral_downline.length
        }

        await transaction.commit();
        return res.status(200).json({
            "data": data,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": await common.util_helper.handleErrorMessageAPI(wallet_address, error),
            "error": true
        });
    }
})


export default router;