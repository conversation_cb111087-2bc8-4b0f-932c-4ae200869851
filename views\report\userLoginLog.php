<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'User Login History');

$this->registerCss('
  .alert-info label {
    margin-bottom: 0;
  }
  ul {
    margin-bottom: 0;
  }
');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_login_log', [
      'model'   => $model,
      'page'    => 'user-login-log',
      'pjax_id' => "#pjax-user-login-log",
    ]); ?>
  </div>
  <div class="card-body">
    <?php if (!empty($last_log)): ?>
      <div class="alert alert-info mb-3">
        <i class="icon fas fa-exclamation-triangle"></i><label><?= Yii::t('app', 'Note'); ?></label>
        <ul>
          <li><?= Yii::t('app', 'System Last Log Date'); ?> : <?= $last_log ?></li>
          <li><?= Yii::t('app', 'Today\'s login count will be updated tomorrow') ?></li>
        </ul>
      </div>
    <?php endif; ?>

    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','Username'),
          'format' => 'raw',
          'value'  => function ($model) {
            $user = $model->user;

            $_name = empty($user->username) ? $user->first_last_name : $user->username;
            $display = "";
            $display .= "Username : ".$_name;

            return $display;
          },
        ],
        // '[
        //   'label'  => Yii::t('app','Address'),
        //   'format' => 'raw',
        //   'value'  => function ($model) {
        //     if (empty($model->wallet->wallet_address)) {
        //       return '-';
        //     }

        //     $link = Yii::$app->formatHelper->formatTon('address', $model->wallet->wallet_address, 5);

        //     return $link;
        //   },
        // ],'
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
      ]
    ]); ?>
  </div>
</div>
