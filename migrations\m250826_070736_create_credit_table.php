<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%credit}}`.
 */
class m250826_070736_create_credit_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%credit}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'type' => $this->string()->notNull(),
            'balance' => $this->decimal(36, 8)->notNull()->defaultValue(0),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByUserType` (`user_id`, `type`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%credit}}');
    }
}
