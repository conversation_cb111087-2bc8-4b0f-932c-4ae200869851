<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "shop_card_items_lookup".
 *
 * @property int $id
 * @property string $item_one
 * @property string $item_two
 * @property string $item_three
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class ShopCardItemsLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shop_card_items_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['item_one', 'item_two', 'item_three', 'created_at'], 'required'],
            [['created_at', 'updated_at', 'is_delete'], 'integer'],
            [['item_one', 'item_two', 'item_three'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'item_one' => 'Item One',
            'item_two' => 'Item Two',
            'item_three' => 'Item Three',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }
}
