<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\helpers\Html;
use yii\helpers\Url;
use app\models\SystemSetting;

class FormatHelper extends Component
{
  /*
  * shorten address and provide correct redirect link
  * @param type   - address / transaction
  * @param value  - txid or address
  * @param length - length of address to show
  */
  public function formatTon($type, $value, $length)
  {
    $type_path    = "";
    $explorer_url = SystemSetting::findOrCreate(SystemSetting::TON_EXPLORER, "https://tonviewer.com");

    // only accept address and transaction type
    if ($type == "address") {
      $type_path = "/address/";
    } else if ($type == "transaction") {
      $type_path = "/tx/";
    } else {
      throw new \Exception("Invalid type");
    }

    // get explorer url
    // if ($network_id == "14149") {
    //     $explorer_url = SystemSetting::findOrCreate(SystemSetting::TON_EXPLORER, "https://www.bthscan.io");
    // } else if ($network_id == "141491") {
    //     $explorer_url = SystemSetting::findOrCreate(SystemSetting::TON_EXPLORER, "https://testnet.bthscan.io");
    // }
    
    // else {
    //   throw new \Exception("Invalid network id");
    // }

    // only show first and last few characters
    $str_start = substr($value, 0, $length);
    $str_end   = substr($value, -$length);
    $str_fin   = $str_start . "..." . $str_end;

    // declaration for copy button
    $copy_button = Html::a("", "javascript:void(0)", [
      'id'    => $value,
      'class' => 'fa fa-copy button-pull-right',
      'data-clipboard-text' => $value,
    ]);

    $redirect_link = $explorer_url->value.$type_path.$value;

    // redirect link to tonviewer
    $link = Html::a($str_fin, Url::to($redirect_link), [
      'target' => '_blank',
      'style'  => 'text-decoration: underline; color: blue;',
    ])." ".$copy_button;
      
    return $link;
  }

  /*
  * shorten address and provide correct redirect link
  * @param type   - address / transaction
  * @param value  - txid or address
  * @param length - length of address to show
  */
  public function formatBsc($type, $value, $length)
  {
    $type_path    = "";
    $explorer_url = "";

    if ($type == "address") {
      $type_path = "/address/";
    } else if ($type == "transaction") {
      $type_path = "/tx/";
    }

    $bsc_url = SystemSetting::findOrCreate(SystemSetting::BSC_EXPLORER, "https://bscscan.com");

    $str_start = substr($value, 0, $length);
    $str_end   = substr($value, -$length);
    $str_fin   = $str_start . "..." . $str_end;

    $copy_button = Html::a("", "javascript:void(0)", [
      'id'    => $value,
      'class' => 'fa fa-copy button-pull-right',
      'data-clipboard-text' => $value,
    ]);

    $redirect_link = $bsc_url->value.$type_path.$value;

    // redirect link to bscscan
    $link = Html::a($str_fin, Url::to($redirect_link), [
      'target' => '_blank',
      'style'  => 'text-decoration: underline; color: blue;',
    ])." ".$copy_button;

    return $link;
  }
}
