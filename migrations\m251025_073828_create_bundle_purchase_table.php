<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%bundle_purchase}}`.
 */
class m251025_073828_create_bundle_purchase_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%bundle_purchase}}', [
            'id'               => $this->primaryKey(),
            'user_id'          => $this->integer()->notNull(),
            'wallet_id'        => $this->integer()->notNull(),
            'bundle_id'        => $this->integer()->notNull(),
            'log_id'           => $this->integer(),
            'tx_id'            => $this->string(255),
            'payment_jetton'   => $this->string(255),
            'contract_address' => $this->string(255),
            'amount'           => $this->decimal(36, 18)->defaultValue(1)->notNull(),
            'usd_amount'       => $this->decimal(36, 18)->defaultValue(0)->notNull(),
            'token_amount'     => $this->decimal(36, 18)->defaultValue(0)->notNull(),
            'bonus_token'      => $this->decimal(36, 18)->defaultValue(0),
            'created_at'       => $this->bigInteger()->notNull(),
            'updated_at'       => $this->bigInteger(),
            'is_delete'        => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`wallet_id`) references `wallet` (`id`)',
            'foreign key (`bundle_id`) references `bundle` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findTxId` (`tx_id`)',
            'key `findByBundleId` (`bundle_id`)',
            'key `findByUserId` (`user_id`)',
            'key `findByWalletId` (`wallet_id`)',
            'key `findByLogId` (`log_id`)',
            'key `findByDate` (`created_at`)',
            'key `findByPaymentJetton` (`payment_jetton`)',
            'key `findByContractAddress` (`contract_address`)'
        ], "engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%bundle_purchase}}');
    }
}
