<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="col-md-12">
      <div class="d-inline-block mr-1">
        <?= $form->field($model, 'email')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Email')) ?>
      </div>
      <div class="d-inline-block float-right">
        <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-default']) ?>
      </div>
    </div>
  </div>
</div>

<?php ActiveForm::end(); ?>