<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use yii\web\View;
use yii\widgets\Breadcrumbs;
use yii\widgets\Pjax;

use kartik\file\FileInput;

use app\models\NoticeLookup;

if ($model->scenario == 'create') {
  $this->title = Yii::t('app', 'Create Notice');
} else {
  $this->title = Yii::t('app', 'Update Notice');
}

$main_page_url = 'notice-mgmt';

// live update for character counters in title and description with input event listener
$registerJs = <<< JS
    const text_area = document.getElementById('description');
    const counter_description = document.getElementById('char-count-description');

    const text_input = document.getElementById('title');
    const counter_title = document.getElementById('char-count-title');

    function updateDescriptionCounter() {
        counter_description.textContent = text_area.value.length;
    }

    text_area.addEventListener('input', updateDescriptionCounter);
    updateDescriptionCounter();

    function updateTitleCounter() {
        counter_title.textContent = text_input.value.length;
    }

    text_input.addEventListener('input', updateTitleCounter);
    updateTitleCounter();
JS;

$this->registerJs($registerJs, View::POS_READY);

?>

<div class="card card-default">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i> {link} / </i> </li>\n",
    'links' => [
      ['label' => 'Social Task Management', 'url' => [$main_page_url]],
      $this->title,
    ],
  ]);
  ?>
  <?php $form = ActiveForm::begin([
      'id'     => 'your-form',
      'method' => 'post',
      'options' => [
        'data-pjax' => true,
      ],
      'fieldConfig' => [
        'inputOptions' => [
          'class' => 'input-sm form-control',
        ],
      ],
    ]);
  ?>
    <div class="card-body">
      <?= $this->render('/site/_alert_flash', []) ?>
      <div class="row">
        <div class="col-md-12">
          <?= $form->field($model, 'notice_label')->textInput([
                'id' => 'title', 
                'placeholder' => ''
            ])->label(Yii::t('app', 'Title')) ?>
          <div class="d-flex justify-content-between text-muted small mt-1 mb-3">
                <div>Not more than 32 characters</div>
                <div><span id="char-count-title">0</span>/32</div>
            </div>
        </div>
        <div class="col-md-12">
          <?= $form->field($model, 'notice_description')->textarea([
                'id' => 'description', 
                'placeholder' => '', 
                'rows' => 3
            ])->label(Yii::t('app', 'Description')) ?>
          <div class="d-flex justify-content-between text-muted small mt-1 mb-3">
                <div>Not more than 128 characters</div>
                <div><span id="char-count-description">0</span>/128</div>
            </div>
        </div>
        
        <!-- <div class="col-md-12">
          <?= $form->field($model, 'alex_reward_point')->textInput([
              'style' => 'width:100%',
              'type'  => 'number',
              'step'  => '1',
              'min'   => '0',
            ])->label(Yii::t('app', 'Reward AleXs'))
          ?>
        </div>
        <div class="col-md-12">
          <?= $form->field($model, 'boost_reward_point')->textInput([
              'style' => 'width:100%',
              'type'  => 'number',
              'step'  => '1',
              'min'   => '0',
            ])->label(Yii::t('app', 'Reward Boost'))
          ?>
        </div> -->
      </div>
    </div>
    <div class="card-footer">
      <?= Html::a(Yii::t('app', 'Back'), Url::to($main_page_url), ['class' => 'btn btn-default']); ?>
      <?= Html::submitButton(Yii::t('app', $model->scenario == 'create' ? 'Create' : 'Update'), [
        'class' => 'btn btn-success float-right',
        'name'  => $model->scenario == 'create' ? 'Create' : 'Update',
        'value' => 1,
      ]) ?>
    </div>
  <?php ActiveForm::end(); ?>
</div>