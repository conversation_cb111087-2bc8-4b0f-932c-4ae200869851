//import here
import bot from "../helpers/bot_helper.js"
import user from "../helpers/user_helper.js"
import jwt from "../helpers/jwt_helper.js"
import auth from "../helpers/auth_helper.js"
import passport_jwt from "../helpers/passports/jwt.js"
import scanner from "../helpers/scanner_helper.js"
import referral from "../helpers/referral_helper.js"
import cache from "../helpers/cache_helper.js"
import transaction from "../helpers/transaction_helper.js"
import admin_wallet from "../helpers/admin_wallet_helper.js"

export default {
    bot_helper: bot,
    user_helper: user,
    jwt_helper: jwt,
    auth_helper: auth,
    passport_helper: {
        jwt: passport_jwt
    },
    scanner_helper: scanner,
    referral_helper: referral,
    cache_helper: cache,
    transaction_helper: transaction,
    admin_wallet_helper: admin_wallet
}