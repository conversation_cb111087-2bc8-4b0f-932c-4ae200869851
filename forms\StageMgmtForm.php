<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use yii\data\ActiveDataProvider;
use app\models\AuditLog;
use app\models\Stage;

class StageMgmtForm extends Model
{
  public $name;
  public $price_per_token;
  public $token_available;
  public $total_fund;
  public $should_end_date;

  public function rules()
  {
    return [
      [['name', 'price_per_token', 'token_available', 'total_fund', 'should_end_date'], 'safe'],
      [['name'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['price_per_token', 'token_available', 'total_fund'], 'number', 'min' => 0],
      [['name', 'should_end_date'], 'required', 'on' => 'update'],
    ];
  }

  public function attributeLabels()
  {
    return [
      'name'            => Yii::t('app', 'Name'),
      'price_per_token' => Yii::t('app', 'Price Per Token'),
      'token_available' => Yii::t('app', 'Token Available'),
      'total_fund'      => Yii::t('app', 'Total Fund'),
      'should_end_date' => Yii::t('app', 'Stage End Date'),
    ];
  }

  public function getQuery()
  {
    $query = Stage::find();

    if (!empty($this->name)) {
      $query->andWhere(['like', 'name', '%'.$this->name.'%', false]);
    }

    $query->andWhere(['is_delete' => 0]);

    // throw new \Exception($query->createCommand()->getRawSql());

    return $query;
  }

  public function getProvider()
  {
    $dataProvider =  new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 10,
      ],
    ]);

    return $dataProvider;
  }

  public function getStageInfo($id)
  {
    $model = Stage::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Stage not found');
    }

    $this->name            = $model->name;
    $this->price_per_token = $model->price_per_token * 1;
    $this->token_available = $model->token_available * 1;
    $this->total_fund      = $model->total_fund * 1;
    $this->should_end_date = date('Y-m-d H:i:s', $model->should_end_date);
  }

  // will only run once
  public function initStage()
  {
    // check if there is any stage in the database
    $model = Stage::findOne([
      'is_delete' => 0,
    ]);

    if (!empty($model)) {
      return;
    }

    // batch create stage
    $stage_data = [
      [
        'name'            => 'Stage 1',
        'price_per_token' => 0.03,
        'token_available' => 28000000,
        'total_fund'      => 840000,
        'should_end_date' => time() + 86400,
      ],
      [
        'name'            => 'Stage 2',
        'price_per_token' => 0.04,
        'token_available' => 18000000,
        'total_fund'      => 720000,
        'should_end_date' => time() + 86400,
      ],
      [
        'name'            => 'Stage 3',
        'price_per_token' => 0.06,
        'token_available' => 16000000,
        'total_fund'      => 960000,
        'should_end_date' => time() + 86400,
      ],
      [
        'name'            => 'Stage 4',
        'price_per_token' => 0.09,
        'token_available' => 14000000,
        'total_fund'      => 1260000,
        'should_end_date' => time() + 86400,
      ],
      [
        'name'            => 'Stage 5',
        'price_per_token' => 0.12,
        'token_available' => 12000000,
        'total_fund'      => 1440000,
        'should_end_date' => time() + 86400,
      ],
      [
        'name'            => 'Stage 6',
        'price_per_token' => 0.15,
        'token_available' => 12000000,
        'total_fund'      => 1800000,
        'should_end_date' => time() + 86400,
      ],
    ];

    foreach ($stage_data as $data) {
      $model                  = new Stage();
      $model->name            = $data['name'];
      $model->price_per_token = $data['price_per_token'];
      $model->token_available = $data['token_available'];
      $model->total_fund      = $data['total_fund'];
      $model->should_end_date = $data['should_end_date'];
      $model->created_at      = time();

      if (!$model->save()) {
        throw new \Exception(current($model->getFirstErrors()));
      }
    }

    return;
  }

  public function updateStage($id)
  {
    $model = Stage::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Stage not found');
    }

    $admin = Yii::$app->user->identity;

    if ($model->should_end_date != strtotime($this->should_end_date)) {
      AuditLog::create([
        'function'  => AuditLog::MANAGE_STAGE,
        'old_value' => date('Y-m-d H:i:s', $model->should_end_date),
        'value'     => $this->should_end_date,
        'remark'    => "Update Stage End Date",
        'action_by' => $admin->id,
      ]);
    }

    $model->should_end_date = strtotime($this->should_end_date);
    $model->updated_at = time();

    if (!$model->update([false, ['should_end_date']])) {
      throw new \Exception(current($model->getFirstErrors()));
    }
  }
}
