<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "shop_item_lookup".
 *
 * @property int $id
 * @property string $item_name
 * @property string $item_icon_image
 * @property string $item_category
 * @property string $item_type
 * @property float|null $item_boost_percentage
 * @property float|null $item_bundle_amount
 * @property float $item_price
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserShopItem[] $userShopItems
 */
class ShopItemLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shop_item_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['item_name', 'item_icon_image', 'item_category', 'item_type', 'item_price', 'created_at'], 'required'],
            [['item_icon_image'], 'string'],
            [['item_boost_percentage', 'item_bundle_amount', 'item_price'], 'number'],
            [['created_at', 'updated_at', 'is_delete'], 'integer'],
            [['item_name', 'item_category', 'item_type'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'item_name' => 'Item Name',
            'item_icon_image' => 'Item Icon Image',
            'item_category' => 'Item Category',
            'item_type' => 'Item Type',
            'item_boost_percentage' => 'Item Boost Percentage',
            'item_bundle_amount' => 'Item Bundle Amount',
            'item_price' => 'Item Price',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserShopItems]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserShopItems()
    {
        return $this->hasMany(UserShopItem::className(), ['item_id' => 'id']);
    }

    /**
     * Gets all categories of shop items.
     *
     * @return array with category values as keys and formatted labels as values
     */
    public static function getAllCategory()
    {
        $query = self::find()
            ->select([
                'id',
                'item_category'
            ])
            ->where(['is_delete' => 0])
            ->orderBy('id ASC')
            ->all();
        
        // capitalized first letter of every word
        foreach ($query as $item) {
            $item->item_category = ucfirst($item->item_category);
        }

        return ArrayHelper::map($query, 'item_category', 'item_category');
    }

    public static function getAllType()
    {
        $query = self::find()
            ->select([
                'id',
                'item_type'
            ])
            ->where(['is_delete' => 0])
            ->orderBy('id ASC')
            ->all();
        
        // capitalized first letter of every word
        foreach ($query as $item) {
            $item->item_type = ucfirst($item->item_type);
        }

        return ArrayHelper::map($query, 'item_type', 'item_type');
    }
}
