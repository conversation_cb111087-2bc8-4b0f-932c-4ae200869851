export default function (sequelize, DataTypes) {
    return sequelize.define('rarity_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        rarity_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        upgrade_alex_spending: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        success_rate_percentage: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'rarity_lookup',
        timestamps: false,
    })
}