import jwt from "jsonwebtoken";
import common from "../../shared/imports/common.js";

export default {
    sign: (payload, keyExtra = '') => {
        return jwt.sign(payload, common.config.secret_config.jwt_secret + keyExtra, { algorithm: 'HS256' }, null);
    },
    verify: (payload, keyExtra = '') => {
        return jwt.verify(payload, common.config.secret_config.jwt_secret + keyExtra, { algorithm: 'HS256' }, null);
    }
}