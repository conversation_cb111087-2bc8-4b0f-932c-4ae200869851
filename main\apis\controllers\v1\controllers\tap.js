import server from "../../../../shared/imports/server.js";
import common from "../../../../shared/imports/common.js";
import query from "../../../../shared/imports/query.js"
import validator from "../../../../shared/imports/validator.js"
import helper from "../../../../shared/imports/helper.js";
import rateLimit from "express-rate-limit";
const tapLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 600,
    keyGenerator: (req) => {
        return req.user ? req.user.id : req.ip
    },
    handler: function (req, res, next) {
        console.log("Rate Limit User : ", req.user, req.ip)
        res.status(429).json({
            "error": true,
            "status": 429,
            "message": "Too many requests, please try again later.",
            "data": {}
        })
    }
});


const router = server.express.Router();

router.post("/",
    helper.auth_helper.authenticateJWT,
    helper.auth_helper.checkOnlyIsVerified,
    tapLimiter,
    validator.exp_validator(validator.items_validator.tapValidator.tap_count_validator),
    async (req, res) => {
        const { id: user_id } = req.user;
        const { tap_count } = req.body

        const transaction = await query.sequelize.transaction();
        try {

        } catch (error) {

        }
    })

export default router;