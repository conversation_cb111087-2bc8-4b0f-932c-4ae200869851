<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "social_task_lookup".
 *
 * @property int $id
 * @property string $task_name
 * @property string $task_label_name
 * @property string $type
 * @property string $message_key
 * @property string $label_message_key
 * @property string|null $redirect_url
 * @property string $icon_image_url
 * @property int $reward_point
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserSocialTask[] $userSocialTasks
 */
class SocialTaskLookup extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'social_task_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['redirect_url', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['task_name', 'task_label_name', 'type', 'message_key', 'label_message_key', 'icon_image_url', 'reward_point', 'created_at'], 'required'],
            [['icon_image_url'], 'string'],
            [['reward_point', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['task_name', 'task_label_name', 'type', 'message_key', 'label_message_key', 'redirect_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'task_name' => 'Task Name',
            'task_label_name' => 'Task Label Name',
            'type' => 'Type',
            'message_key' => 'Message Key',
            'label_message_key' => 'Label Message Key',
            'redirect_url' => 'Redirect Url',
            'icon_image_url' => 'Icon Image Url',
            'reward_point' => 'Reward Point',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserSocialTasks]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSocialTasks()
    {
        return $this->hasMany(UserSocialTask::class, ['social_task_id' => 'id']);
    }

    /**
     * Return list of excluded types
     * 
     * @return array
     */
    public static function getExcludeType()
    {
        return [
            'daily_rewards',
            'social_profile',
            'invite_referral',
            'invite_referral_premium'
        ];
    }

    /**
     * Get type of social task
     * 
     * @param string $_search If $_search is null, will return all types. If $_search is not null, will return type name of $_search.
     * 
     * @return array|string
     */
    public static function getType($_search = null)
    {
        $_type = [
            'social_task' => 'Social Task',
            'daily_rewards' => 'Daily Rewards',
            'weekly_task' => 'Weekly Task',
        ];

        if ($_search != null) {
            if (array_key_exists($_search, $_type)) {
                return $_type[$_search];
            } else {
                return ucwords(str_replace('_',' ',$_search));
            }
        }

        return $_type;
    }

    /**
     * Return all task names
     * 
     * Select id, task name from social_task_lookup
     * where is_delete = 0
     * order by id ASC
     * 
     * @return array
     */
    public static function getAllTaskName()
    {
        $query = self::find()
            ->select([
                'id',
                'task_name'
            ])
            ->where(['is_delete' => 0])
            ->orderby(['id' => SORT_ASC])
            ->all();

        return ArrayHelper::map($query, 'task_name', 'task_name');
    }

}
