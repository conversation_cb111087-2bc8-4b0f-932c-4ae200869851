<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use app\events\AttributeChangeEvent;

abstract class AttributeChangeForm extends Model
{
	const EVENT_ATTR_CHANGED   = 'EVENT_ATTR_CHANGED';

	protected $_attributes   = [];
	protected $_changedAttrs = [];

	protected $_admin;

	public function init()
	{
		parent::init();

		$this->on(self::EVENT_ATTR_CHANGED,   [$this, 'attributeChanged']);
		$this->on(self::EVENT_AFTER_VALIDATE, [$this, 'afterValidation']);
	}

	public function getChangedAttributes()
	{
		return $this->_changedAttrs;
	}

	protected function initAttributes()
	{
		$this->_attributes = $this->attributes;
	}

	protected function attributeChanged(AttributeChangeEvent $event)
	{
		$this->_changedAttrs[$event->attribute] = $event->list;
	}

	protected function afterValidation($event)
	{
		foreach ($this->attributes as $key => $value) {
			$change  = [];
			$oldAttr = null;
			if (array_key_exists($key, $this->_attributes)) {
				$oldAttr = $this->_attributes[$key];
			}

			if (is_array($value)) {
				foreach ($value as $k => $v) {
					if ($oldAttr[$k] != $v) {
						$change[]  = [
							'change_type' => $k,
							'old'         => preg_replace('#<script(.*?)>(.*?)</script>#is', '', $oldAttr[$k]),
							'new'         => preg_replace('#<script(.*?)>(.*?)</script>#is', '', $v),
						];
					}
				}
			} else {
				if ($oldAttr != $value) {
					$change = [
						'old' => preg_replace('#<script(.*?)>(.*?)</script>#is', '', $oldAttr),
						'new' => preg_replace('#<script(.*?)>(.*?)</script>#is', '', $value),
					];
				}
			}

			if (!empty($change)) {
				$this->trigger(self::EVENT_ATTR_CHANGED, Yii::createObject([
					'class'     => AttributeChangeEvent::className(),
					'attribute' => $key,
					'list'      => $change
				]));
			}
		}
	}
}
