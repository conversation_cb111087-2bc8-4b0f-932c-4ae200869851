<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\helpers\Html;
use yii\data\ActiveDataProvider;
use app\models\Wallet;
use app\models\UserReferral;

class ReferralMgmtForm extends Model
{
  public $username;
  public $address;
  public $referral_address;
  public $referral_code;

  public function rules()
  {
    return [
      [['username', 'address', 'referral_address', 'referral_code'], 'safe'],
      [['username', 'address', 'referral_address', 'referral_code'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
    ];
  }

  /**
   * Get the query for data provider
   * 
   * Select * from user_referral as ur
   * left join user u on u.id = ur.user_id
   * left join user u2 u2.id = ur.referral_user_id
   * where u.username like %:username% or u.first_last_name like %:username%
   * and u.wallet_address like %:address% and ur.referral_code like %:referral_code%
   * and u2.wallet_address like %:referral_address%
   * and ur.is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = UserReferral::find();
    $query->alias('ur')
      ->joinWith('user u');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->address."%", false]);
    }

    if (!empty($this->referral_address)) {
      $query->leftJoin('user u2', 'u2.id = ur.referral_user_id');
      $query->andFilterWhere(['like','u2.wallet_address',"%".$this->referral_address."%", false]);
    }

    if (!empty($this->referral_code)) {
      $query->andFilterWhere(['like','ur.referral_code',"%".$this->referral_code."%", false]);
    }

    $query->andWhere(['ur.is_delete' => 0]);

    return $query;
  }

  /**
   * Creates and returns an ActiveDataProvider for paginated and sorted data display
   * 
   * @return ActiveDataProvider
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Get the referral tree, given a user_id
   * 
   * This function is a recursive function to get the referral tree, given a user_id.
   * It will return an array of referrals, where each referral is an array contains id, label, and items.
   * The items is also an array of referrals, which is a recursive call of this function.
   * 
   * @param int $user_id user id
   * 
   * @return array
   */
  public function getReferralTree($user_id)
  {
    $ref = UserReferral::findOne([
      'id'        => $user_id,
      'is_delete' => 0
    ]);

    if (empty($ref)) {
      throw new \Exception("Referral not found");
    }

    $ref_data = [];

    $query = UserReferral::find();
    $query->andWhere(['referral_user_id' => $user_id]);
    $query->andWhere(['is_delete' => 0]);

    $referrals = $query->all();

    $all_ref = $query->all();

    if (empty($all_ref)) {
      return $ref_data;
    }

    foreach ($all_ref as $_ref) {
      $_user = $_ref->user;
      $_name = empty($_user->username) ? $_user->first_last_name : $_user->username;

      // <name> (<wallet_address>) button<view tree>
      $label = Html::tag('span',$_name, ['class' => 'badge badge-info']).' ('.$_ref->user->wallet_address.') '.Html::a('View Tree', [
        'admin/view-referral',
        'user_id' => $_ref->id,
      ], [
        'target' => '_blank',
        'class'  => ''
      ]).'</b>';

      $ref_data[] = [
        'id'    => $_ref->id,
        'label' => $label,
        'items' => $this->getReferralTree($_ref->id)
      ];
    }

    return $ref_data;
  }
}
