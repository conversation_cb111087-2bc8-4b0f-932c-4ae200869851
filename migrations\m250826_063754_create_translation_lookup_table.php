<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%translation_lookup}}`.
 */
class m250826_063754_create_translation_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%translation_lookup}}', [
            'id'          => $this->primaryKey(),
            'message_key' => 'varchar(50)',
            'en_us'       => 'varchar(512)',
            'zh_cn'       => 'varchar(512)',
            'ja_jp'       => 'varchar(512)',
            'ko_kr'       => 'varchar(512)',
            'ms_my'       => 'varchar(512)',
            'created_at'  => $this->bigInteger()->notNull(),
            'updated_at'  => $this->bigInteger(),
            'is_delete'   => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByMessageKey` (`message_key`, `is_delete`)',
            'key `findByEnUs` (`en_us`, `is_delete`)',
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%translation_lookup}}');
    }
}
