<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "invite_reward_lookup".
 *
 * @property int $id
 * @property string $reward_label
 * @property int $invite_qualify_count
 * @property int $reward_amount
 * @property string $icon_image_url
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserInviteReward[] $userInviteRewards
 */
class InviteRewardLookup extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'invite_reward_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['reward_label', 'invite_qualify_count', 'reward_amount', 'icon_image_url', 'created_at'], 'required'],
            [['invite_qualify_count', 'reward_amount', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['icon_image_url'], 'string'],
            [['reward_label'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'reward_label' => 'Reward Label',
            'invite_qualify_count' => 'Invite Qualify Count',
            'reward_amount' => 'Reward Amount',
            'icon_image_url' => 'Icon Image Url',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserInviteRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserInviteRewards()
    {
        return $this->hasMany(UserInviteReward::class, ['invite_reward_id' => 'id']);
    }

}
