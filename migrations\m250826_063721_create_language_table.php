<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%language}}`.
 */
class m250826_063721_create_language_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%language}}', [
            'id'         => $this->primaryKey(),
            'code'       => $this->string(255)->unique(),
            'label'      => $this->string(255),
            'name'       => $this->string(255),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete'  => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByCode` (`code`, `is_delete`)',
            'key `findByLabel` (`label`, `is_delete`)',
            'key `findByName` (`name`, `is_delete`)',
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%language}}');
    }
}
