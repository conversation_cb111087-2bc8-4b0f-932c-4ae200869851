/**
 * ABI Parser for TON Smart Contracts
 * 
 * Enhanced with robust address decoding from slices/cells and raw stack item handling
 * Returns non-bounceable addresses by default
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { Address, TupleReader, TupleBuilder, Cell, Slice } from '@ton/ton';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Detect item type from raw stack value (ENHANCED)
 * @param {*} item - Raw stack item
 * @returns {string|null} Detected type
 */
function detectItemType(item) {
    // Check for raw BigInt
    if (typeof item === 'bigint') {
        return 'int';
    }
    
    // Check for object with type property (standard format)
    if (item && typeof item === 'object' && 'type' in item && typeof item.type === 'string') {
        return item.type;
    }
    
    // Check for Cell instance
    if (item instanceof Cell) {
        return 'cell';
    }
    
    // Check for Slice instance
    if (item instanceof Slice) {
        return 'slice';
    }
    
    // Check for slice-like objects (has loadAddress, loadBit, etc.)
    if (item && typeof item === 'object') {
        if (typeof item.loadAddress === 'function' || typeof item.loadBit === 'function') {
            return 'slice';
        }
        
        // Check for cell-like objects (has beginParse, hash, etc.)
        if (typeof item.beginParse === 'function' || typeof item.hash === 'function') {
            return 'cell';
        }
        
        // Check string representation for x{...}_ pattern (TON slice notation)
        const str = String(item);
        if (str.match(/^x\{[0-9A-Fa-f]+.*\}_?$/)) {
            return 'slice';
        }
        
        // Check constructor name
        const constructorName = item.constructor?.name;
        if (constructorName === 'Slice') return 'slice';
        if (constructorName === 'Cell') return 'cell';
        
        // Last resort: check for any cell/slice indicators
        if (item._cell || item.cell) return 'cell';
    }
    
    return null;
}

/**
 * Get value from stack item (handles both raw and wrapped formats)
 * @param {*} item - Stack item
 * @returns {*} Extracted value
 */
function getItemValue(item) {
    if (typeof item === 'bigint') {
        return item;
    }
    if (item && typeof item === 'object' && 'value' in item) {
        return item.value;
    }
    return item;
}

/**
 * Decode address from raw item (ENHANCED - Returns Address object)
 * @param {*} item - Raw stack item containing address
 * @returns {Address|null} Decoded address or null
 */
function decodeAddressFromRawItem(item) {
    try {
        let address = null;
        
        // Handle Slice instance directly
        if (item instanceof Slice) {
            address = item.loadAddress();
        }
        // Handle object with loadAddress method
        else if (item && typeof item.loadAddress === 'function') {
            address = item.loadAddress();
        }
        // Handle Cell instance
        else if (item instanceof Cell) {
            const slice = item.beginParse();
            address = slice.loadAddress();
        }
        // Handle object with beginParse method (cell-like)
        else if (item && typeof item.beginParse === 'function') {
            const slice = item.beginParse();
            address = slice.loadAddress();
        }
        // Handle object with cell property
        else if (item && typeof item === 'object' && item.cell) {
            if (item.cell instanceof Cell) {
                const slice = item.cell.beginParse();
                address = slice.loadAddress();
            }
            else if (typeof item.cell.beginParse === 'function') {
                const slice = item.cell.beginParse();
                address = slice.loadAddress();
            }
        }
        // Handle internal _cell property
        else if (item && typeof item === 'object' && item._cell) {
            if (item._cell instanceof Cell) {
                const slice = item._cell.beginParse();
                address = slice.loadAddress();
            }
        }
        // Check for Symbol properties (TON library sometimes uses symbols)
        else if (item && typeof item === 'object') {
            const symbols = Object.getOwnPropertySymbols(item);
            for (const sym of symbols) {
                const val = item[sym];
                if (val instanceof Cell) {
                    const slice = val.beginParse();
                    address = slice.loadAddress();
                    break;
                }
                if (val instanceof Slice) {
                    address = val.loadAddress();
                    break;
                }
            }
        }
        
        if (!address) {
            console.warn(`⚠️ Cannot decode address from item:`, {
                type: typeof item,
                constructor: item?.constructor?.name,
                hasLoadAddress: typeof item?.loadAddress === 'function',
                hasBeginParse: typeof item?.beginParse === 'function',
                string: String(item),
                keys: item && typeof item === 'object' ? Object.keys(item) : []
            });
            return null;
        }
        
        return address;
    } catch (error) {
        console.error(`❌ Failed to decode address: ${error.message}`, { 
            item,
            type: typeof item,
            constructor: item?.constructor?.name 
        });
        return null;
    }
}

/**
 * Convert Address object to string (non-bounceable by default)
 * @param {Address|null} address - Address object
 * @param {boolean} bounceable - Whether to use bounceable format
 * @returns {string|null} Address string or null
 */
function addressToString(address, bounceable = false) {
    if (!address) return null;
    try {
        return address.toString({ 
            bounceable: bounceable,
            testOnly: false 
        });
    } catch (error) {
        console.error(`❌ Failed to convert address to string:`, error.message);
        return null;
    }
}

/**
 * Smart address reader - tries multiple strategies (Returns non-bounceable by default)
 * @param {TupleReader} tuple - Tuple reader
 * @param {Object} stackItem - Raw stack item
 * @param {string} fieldName - Field name for logging
 * @param {boolean} bounceable - Whether to return bounceable format
 * @returns {string|null} Address string or null
 */
function readAddressSmart(tuple, stackItem, fieldName, bounceable = false) {
    try {
        // Strategy 1: Direct read (standard format)
        const addr = tuple.readAddress();
        return addressToString(addr, bounceable);
    } catch (e1) {
        console.debug(`Direct address read failed for ${fieldName}, trying slice decode...`);
        
        try {
            // Strategy 2: Read as cell and decode
            const cell = tuple.readCell();
            const slice = cell.beginParse();
            const address = slice.loadAddress();
            return addressToString(address, bounceable);
        } catch (e2) {
            console.debug(`Slice decode failed for ${fieldName}, trying raw item decode...`);
            
            try {
                // Strategy 3: Decode from raw stack item
                const address = decodeAddressFromRawItem(stackItem);
                if (address) {
                    // Tuple was not advanced, skip manually if using TupleReader
                    if (tuple.skip) {
                        tuple.skip(1);
                    }
                    return addressToString(address, bounceable);
                }
            } catch (e3) {
                console.error(`❌ All address decode strategies failed for ${fieldName}:`, {
                    direct: e1.message,
                    slice: e2.message,
                    raw: e3.message,
                    stackItem
                });
            }
        }
    }
    
    return null;
}

/**
 * Load and parse ABI file
 * @param {string} abiFileName - Name of the ABI file (e.g., 'general_presale.json')
 * @returns {Object} Parsed ABI object
 */
export function loadABI(abiFileName) {
    try {
        const abiPath = join(__dirname, '..', 'abis', abiFileName);
        const abiContent = readFileSync(abiPath, 'utf-8');
        const abi = JSON.parse(abiContent);
        console.log(`✅ Loaded ABI: ${abi.name || abiFileName}`);
        return abi;
    } catch (error) {
        console.error(`❌ Error loading ABI file ${abiFileName}:`, error.message);
        throw new Error(`Failed to load ABI: ${error.message}`);
    }
}

/**
 * Stringify TON tuple with support for single-line and pretty formats
 * @param {*} value - Value to stringify
 * @param {Object} options - Options
 * @param {boolean} options.pretty - Pretty print with indentation (default: false)
 * @param {number} options.indent - Spaces per indent level (default: 2)
 * @returns {string} Stringified value
 */
function stringifyTonTuple(value, options = {}) {
    const { pretty = false, indent = 2 } = options;
    const seen = new WeakSet();

    const stringify = (val, depth = 0) => {
        // Handle primitives
        if (val === null) return 'null';
        if (val === undefined) return 'undefined';
        if (typeof val === 'string') return JSON.stringify(val);
        if (typeof val === 'number') return String(val);
        if (typeof val === 'boolean') return String(val);

        // Handle BigInt
        if (typeof val === 'bigint') {
            return val.toString() + 'n';
        }

        // Handle objects
        if (typeof val === 'object') {
            // Check for circular reference
            if (seen.has(val)) {
                return '"[Circular]"';
            }
            seen.add(val);

            // Check if it's a TON Cell/Slice (has toString with x{...}_ format)
            const strVal = val.toString();
            if (strVal.match(/^x\{[0-9A-Fa-f]+.*\}_?$/)) {
                return `"${strVal}"`;
            }

            // Handle arrays
            if (Array.isArray(val)) {
                if (val.length === 0) return '[]';

                if (pretty) {
                    const spacing = ' '.repeat(indent * (depth + 1));
                    const items = val.map(item => spacing + stringify(item, depth + 1)).join(',\n');
                    const closing = ' '.repeat(indent * depth);
                    return `[\n${items}\n${closing}]`;
                } else {
                    const items = val.map(item => stringify(item, depth + 1)).join(',');
                    return `[${items}]`;
                }
            }

            // Handle objects
            const keys = Object.keys(val);
            if (keys.length === 0) return '{}';

            if (pretty) {
                const spacing = ' '.repeat(indent * (depth + 1));
                const props = keys.map(key => {
                    const value = stringify(val[key], depth + 1);
                    return `${spacing}"${key}": ${value}`;
                }).join(',\n');
                const closing = ' '.repeat(indent * depth);
                return `{\n${props}\n${closing}}`;
            } else {
                const props = keys.map(key => {
                    const value = stringify(val[key], depth + 1);
                    return `"${key}":${value}`;
                }).join(',');
                return `{${props}}`;
            }
        }

        return String(val);
    };

    return stringify(value);
}
/**
 * Validate stack item against expected ABI type (ENHANCED)
 * @param {Object|BigInt} item - Stack item from TupleReader or raw BigInt
 * @param {Object} fieldType - ABI field type definition
 * @param {string} fieldName - Field name for logging
 * @param {number} index - Field index
 * @returns {boolean} True if valid
 */
function validateStackItem(item, fieldType, fieldName, index) {
    if (item === null || item === undefined) {
        console.error(`❌ No stack item for ${fieldName} (index ${index})`);
        return false;
    }

    const expectedType = fieldType.type;
    const itemType = detectItemType(item);
    const itemValue = getItemValue(item);

    if (!itemType) {
        // For address fields, be more permissive - try to decode anyway
        if (expectedType === 'address') {
            console.debug(`Unknown type for address field ${fieldName}, will try to decode`);
            return true; // Allow it, will try to decode in parsing phase
        }
        console.error(`❌ Cannot detect item type for ${fieldName} (index ${index})`, { 
            item,
            string: String(item),
            type: typeof item,
            constructor: item?.constructor?.name
        });
        return false;
    }

    if (fieldType.kind === 'simple') {
        if (expectedType === 'bool' && itemType === 'int') {
            return itemValue === 0n || itemValue === 1n || itemValue === -1n;
        }
        if ((expectedType === 'uint' || expectedType === 'int') && itemType === 'int') {
            if (expectedType === 'uint' && itemValue < 0n) {
                console.warn(`⚠️ Negative value ${itemValue} for uint field ${fieldName} (index ${index})`);
                return false;
            }
            return true;
        }
        // Enhanced address validation - accept slice, cell, or unknown types
        if (expectedType === 'address') {
            return itemType === 'slice' || itemType === 'cell' || itemType === 'address';
        }
        if ((expectedType === 'cell' || expectedType === 'slice') && (itemType === 'cell' || itemType === 'slice')) {
            return true;
        }
        if (expectedType === itemType) {
            return true;
        }
    } else if (fieldType.kind === 'tuple' && itemType === 'tuple') {
        return true;
    } else if (fieldType.kind === 'dict' && itemType === 'cell') {
        return true;
    }

    console.warn(`⚠️ Type mismatch for ${fieldName} (index ${index}): expected ${expectedType || fieldType.kind}, got ${itemType}`, { 
        item,
        string: String(item)
    });
    return false;
}

/**
 * Parse tuple based on type definition from ABI with enhanced error handling
 * @param {TupleReader|Array} tuple - Tuple reader from contract response or raw items array
 * @param {Object} typeDefinition - Type definition from ABI
 * @param {string} typeName - Name of the type for logging
 * @param {boolean} bounceableAddresses - Whether to return bounceable address format
 * @returns {Object|null} Parsed object or null
 */
function parseTupleByTypeDefinition(tuple, typeDefinition, typeName, bounceableAddresses = false) {
    try {
        const result = {};
        
        // Handle raw items array vs TupleReader
        const items = tuple.items || tuple;
        const isRawArray = Array.isArray(items) && !tuple.readAddress;
        
        if (!items || (Array.isArray(items) && items.length < typeDefinition.fields.length)) {
            console.error(`❌ Insufficient tuple items for ${typeName}: expected ${typeDefinition.fields.length}, got ${items?.length || 0}`);
            return null;
        }

        for (let index = 0; index < typeDefinition.fields.length; index++) {
            const field = typeDefinition.fields[index];
            const fieldName = field.name;
            const fieldType = field.type;

            const stackItem = items[index];
            
            if (!validateStackItem(stackItem, fieldType, fieldName, index)) {
                console.error(`❌ Invalid stack item for ${fieldName} (index ${index}) of ${typeName}`, { 
                    stackItem,
                    string: String(stackItem),
                    type: typeof stackItem,
                    constructor: stackItem?.constructor?.name
                });
                return null;
            }

            try {
                if (fieldType.kind === 'simple') {
                    if (fieldType.type === 'address') {
                        // For raw items array, decode directly
                        if (isRawArray) {
                            const address = decodeAddressFromRawItem(stackItem);
                            result[fieldName] = addressToString(address, bounceableAddresses);
                            if (!result[fieldName]) {
                                console.error(`❌ Failed to decode address for ${fieldName}`, {
                                    stackItem,
                                    string: String(stackItem),
                                    type: typeof stackItem
                                });
                            }
                        } else {
                            // For TupleReader, use smart reader
                            result[fieldName] = readAddressSmart(tuple, stackItem, fieldName, bounceableAddresses);
                        }
                    } else if (fieldType.type === 'bool') {
                        const itemValue = getItemValue(stackItem);
                        if (isRawArray) {
                            result[fieldName] = itemValue !== 0n;
                        } else {
                            if (typeof stackItem === 'bigint' || (stackItem.type === 'int' && itemValue !== undefined)) {
                                result[fieldName] = itemValue !== 0n;
                            } else {
                                result[fieldName] = tuple.readBoolean();
                            }
                        }
                    } else if (fieldType.type === 'uint' || fieldType.type === 'int') {
                        const itemValue = getItemValue(stackItem);
                        if (isRawArray) {
                            result[fieldName] = itemValue;
                        } else {
                            if (typeof stackItem === 'bigint' || (stackItem.type === 'int' && itemValue !== undefined)) {
                                result[fieldName] = itemValue;
                            } else {
                                result[fieldName] = tuple.readBigNumber();
                            }
                        }
                    } else if (fieldType.type === 'cell' || fieldType.type === 'slice') {
                        if (isRawArray) {
                            result[fieldName] = stackItem;
                        } else {
                            result[fieldName] = tuple.readCell();
                        }
                    } else {
                        console.warn(`⚠️ Unsupported simple type ${fieldType.type} for ${fieldName}, using raw value`);
                        if (isRawArray) {
                            result[fieldName] = stackItem;
                        } else {
                            result[fieldName] = tuple.readCellOpt();
                        }
                    }
                } else if (fieldType.kind === 'dict') {
                    if (isRawArray) {
                        result[fieldName] = stackItem;
                    } else {
                        result[fieldName] = tuple.readCellOpt();
                    }
                } else if (fieldType.kind === 'tuple') {
                    const nestedType = fieldType.type || fieldType.name;
                    const nestedDef = typeDefinition.abi?.types?.find(t => t.name === nestedType);
                    if (nestedDef) {
                        nestedDef.abi = typeDefinition.abi;
                        if (isRawArray) {
                            const nestedItems = stackItem.items || stackItem;
                            result[fieldName] = parseTupleByTypeDefinition(nestedItems, nestedDef, nestedType, bounceableAddresses);
                        } else {
                            const nestedTuple = tuple.readTuple();
                            result[fieldName] = parseTupleByTypeDefinition(nestedTuple, nestedDef, nestedType, bounceableAddresses);
                        }
                    } else {
                        console.warn(`⚠️ Missing tuple type definition for ${fieldName}: ${nestedType}`);
                        result[fieldName] = stackItem;
                    }
                } else {
                    console.warn(`⚠️ Unknown type kind ${fieldType.kind} for ${fieldName}, using raw value`);
                    result[fieldName] = stackItem;
                }
            } catch (error) {
                console.error(`❌ Error parsing field ${fieldName} (index ${index}) of ${typeName}: ${error.message}`, { 
                    stackItem,
                    string: String(stackItem)
                });
                return null;
            }
        }

        return result;
    } catch (error) {
        console.error(`❌ Error parsing tuple for ${typeName}: ${error.message}`, { typeDefinition });
        return null;
    }
}

/**
 * Get getter method definition from ABI
 * @param {Object} abi - ABI object
 * @param {string} methodName - Name of the getter method
 * @returns {Object|null} Getter definition or null
 */
export function getGetterDefinition(abi, methodName) {
    const getter = abi.getters?.find(g => g.name === methodName);
    if (!getter) {
        console.warn(`⚠️ Getter ${methodName} not found in ABI`);
    }
    return getter || null;
}

/**
 * Get type definition from ABI
 * @param {Object} abi - ABI object
 * @param {string} typeName - Name of the type
 * @returns {Object|null} Type definition or null
 */
export function getTypeDefinition(abi, typeName) {
    const typeDef = abi.types?.find(t => t.name === typeName);
    if (!typeDef) {
        console.warn(`⚠️ Type ${typeName} not found in ABI`);
    }
    return typeDef || null;
}

/**
 * Validate ABI structure
 * @param {Object} abi - ABI object to validate
 * @returns {boolean} True if valid
 */
export function validateABI(abi) {
    if (!abi.name) {
        console.error('❌ ABI missing contract name');
        return false;
    }
    
    if (!Array.isArray(abi.types)) {
        console.error('❌ ABI missing types array');
        return false;
    }
    
    if (!Array.isArray(abi.getters)) {
        console.error('❌ ABI missing getters array');
        return false;
    }
    
    console.log(`✅ ABI validated: ${abi.name}`);
    return true;
}

/**
 * Convert method arguments to stack format based on ABI type definitions
 * @param {Array} args - Raw arguments
 * @param {Object} getter - Getter definition from ABI
 * @returns {Array} Formatted stack arguments for TupleBuilder
 */
function formatArgsForStack(args, getter) {
    if (!args || args.length === 0) return [];
    
    const tupleBuilder = new TupleBuilder();
    const argDefinitions = getter.arguments || [];
    
    if (args.length > argDefinitions.length) {
        console.warn(`⚠️ Too many arguments provided for getter ${getter.name}: expected ${argDefinitions.length}, got ${args.length}`);
    }
    
    for (let i = 0; i < argDefinitions.length; i++) {
        const arg = args[i];
        const argDef = argDefinitions[i];
        
        if (!argDef) {
            console.warn(`⚠️ No type definition for argument ${i}, skipping`);
            continue;
        }
        
        const argType = argDef.type;
        
        try {
            if (argType.kind === 'simple') {
                if (argType.type === 'address') {
                    let address;
                    if (typeof arg === 'string') {
                        address = Address.parse(arg);
                    } else if (arg instanceof Address) {
                        address = arg;
                    } else {
                        throw new Error(`Invalid address argument at position ${i}: ${arg}`);
                    }
                    tupleBuilder.writeAddress(address);
                } else if (argType.type === 'int' || argType.type === 'uint') {
                    const value = typeof arg === 'bigint' ? arg : BigInt(arg);
                    tupleBuilder.writeNumber(value);
                } else if (argType.type === 'bool') {
                    if (typeof arg !== 'boolean') {
                        throw new Error(`Invalid boolean argument at position ${i}: ${arg}`);
                    }
                    tupleBuilder.writeBoolean(arg);
                } else if (argType.type === 'cell') {
                    if (!(arg instanceof Cell)) {
                        throw new Error(`Invalid cell argument at position ${i}: ${arg}`);
                    }
                    tupleBuilder.writeCell(arg);
                } else if (argType.type === 'slice') {
                    tupleBuilder.writeSlice(arg);
                } else {
                    console.warn(`⚠️ Unsupported simple type ${argType.type} for arg ${i}, attempting number`);
                    tupleBuilder.writeNumber(BigInt(arg));
                }
            } else if (argType.kind === 'tuple') {
                console.warn(`⚠️ Tuple argument type not fully supported for arg ${i}, attempting cell`);
                tupleBuilder.writeCell(arg);
            } else {
                console.warn(`⚠️ Complex argument type ${argType.kind} not supported for arg ${i}`);
            }
        } catch (error) {
            console.error(`❌ Error formatting argument ${i} for getter ${getter.name}:`, error.message);
            throw error;
        }
    }
    
    return tupleBuilder.build();
}

/**
 * Create ABI-aware contract caller
 * @param {Object} abi - ABI object
 * @param {TonClient} client - TON client instance
 * @param {string} contractAddress - Contract address
 * @param {Object} options - Configuration options
 * @param {boolean} options.bounceableAddresses - Whether to return addresses in bounceable format (default: false)
 * @returns {Object} Contract caller with typed methods
 */
export function createContractCaller(abi, client, contractAddress, options = {}) {
    const { bounceableAddresses = false } = options;
    
    let address;
    try {
        address = Address.parse(contractAddress);
    } catch (error) {
        console.error(`❌ Invalid contract address ${contractAddress}:`, error.message);
        throw new Error(`Invalid contract address: ${error.message}`);
    }
    
    const caller = {
        address,
        abi,
        bounceableAddresses,
        
        /**
         * Call a getter method (generic)
         * @param {string} methodName - Name of the getter
         * @param {Array} args - Method arguments (raw values: Address objects, BigInt, boolean, etc.)
         * @returns {Promise<any>} Raw method result
         */
        async callGetter(methodName, args = []) {
            const getter = getGetterDefinition(abi, methodName);
            if (!getter) {
                console.warn(`⚠️ Getter ${methodName} not found in ABI`);
                return null;
            }
            
            const formattedArgs = formatArgsForStack(args, getter);
            try {
                const result = await client.runMethod(address, methodName, formattedArgs);
                console.debug(`Raw stack for ${methodName}:`, stringifyTonTuple(result.stack.items));
                return result;
            } catch (error) {
                console.error(`❌ Error calling getter ${methodName}:`, error.message);
                return null;
            }
        },
        
        /**
         * Call getter and get simple return value (int, bool, address)
         * @param {string} methodName - Name of the getter
         * @param {Array} args - Method arguments
         * @returns {Promise<any>} Parsed return value
         */
        async callGetterSimple(methodName, args = []) {
            const result = await this.callGetter(methodName, args);
            if (!result) return null;
            
            const getter = getGetterDefinition(abi, methodName);
            if (!getter) return null;
            
            const returnType = getter.returnType;
            
            if (returnType.kind === 'simple') {
                try {
                    if (returnType.type === 'int' || returnType.type === 'uint') {
                        return result.stack.readBigNumber();
                    } else if (returnType.type === 'bool') {
                        const stackItem = result.stack.items[0];
                        if (stackItem && stackItem.type === 'int') {
                            return stackItem.value !== 0n;
                        }
                        return result.stack.readBoolean();
                    } else if (returnType.type === 'address') {
                        return readAddressSmart(result.stack, result.stack.items[0], 'return', this.bounceableAddresses);
                    } else if (returnType.type === 'cell') {
                        return result.stack.readCell();
                    } else {
                        console.warn(`⚠️ Unsupported simple return type ${returnType.type} for ${methodName}`);
                        return result.stack.readCellOpt();
                    }
                } catch (error) {
                    console.error(`❌ Error parsing simple return for ${methodName}: ${error.message}`, { stackItem: result.stack.items[0] });
                    return null;
                }
            }
            
            return null;
        },
        
        /**
         * Call getter and parse tuple return value based on ABI type definition
         * @param {string} methodName - Name of the getter
         * @param {Array} args - Method arguments
         * @returns {Promise<Object|null>} Parsed object or null
         */
        async callGetterTyped(methodName, args = []) {
            let result;
            try {
                result = await this.callGetter(methodName, args);
                if (!result) return null;
                
                const getter = getGetterDefinition(abi, methodName);
                if (!getter) return null;
                
                const returnType = getter.returnType;
                
                // Handle optional return types
                if (returnType.optional) {
                    if (result.stack.remaining === 0) {
                        console.debug(`Optional return for ${methodName} is null (empty stack)`);
                        return null;
                    }
                    
                    // Check if first item is a tuple
                    const firstItem = result.stack.items[0];
                    if (firstItem && firstItem.type === 'tuple') {
                        console.debug(`Optional return for ${methodName} is a tuple, treating as non-optional`);
                        const typeDefinition = getTypeDefinition(abi, returnType.type);
                        if (typeDefinition) {
                            typeDefinition.abi = abi;
                            return parseTupleByTypeDefinition(firstItem.items, typeDefinition, returnType.type, this.bounceableAddresses);
                        }
                        console.warn(`⚠️ No type definition for ${returnType.type}, returning raw tuple`);
                        return firstItem.items;
                    }
                    
                    try {
                        const hasValue = result.stack.readBoolean();
                        if (!hasValue) {
                            console.debug(`Optional return for ${methodName} is null (false marker)`);
                            return null;
                        }
                    } catch (error) {
                        console.warn(`⚠️ Failed to read optional marker for ${methodName}, attempting tuple parse:`, error.message);
                        const typeDefinition = getTypeDefinition(abi, returnType.type);
                        if (typeDefinition) {
                            typeDefinition.abi = abi;
                            const tuple = result.stack.readTuple();
                            return parseTupleByTypeDefinition(tuple, typeDefinition, returnType.type, this.bounceableAddresses);
                        }
                        console.warn(`⚠️ No type definition for ${returnType.type}, returning null`);
                        return null;
                    }
                }
                
                // Handle custom tuple types
                if (returnType.kind === 'simple' && !['int', 'uint', 'bool', 'address', 'cell'].includes(returnType.type)) {
                    const typeDefinition = getTypeDefinition(abi, returnType.type);
                    if (typeDefinition) {
                        typeDefinition.abi = abi;
                        const tuple = result.stack.readTuple();
                        return parseTupleByTypeDefinition(tuple, typeDefinition, returnType.type, this.bounceableAddresses);
                    }
                    console.warn(`⚠️ No type definition for ${returnType.type}, returning null`);
                    return null;
                }
                
                // Handle simple types
                if (returnType.kind === 'simple') {
                    if (returnType.type === 'int' || returnType.type === 'uint') {
                        return result.stack.readBigNumber();
                    } else if (returnType.type === 'bool') {
                        const stackItem = result.stack.items[0];
                        if (stackItem && stackItem.type === 'int') {
                            return stackItem.value !== 0n;
                        }
                        return result.stack.readBoolean();
                    } else if (returnType.type === 'address') {
                        return readAddressSmart(result.stack, result.stack.items[0], 'return', this.bounceableAddresses);
                    } else if (returnType.type === 'cell') {
                        return result.stack.readCell();
                    } else {
                        console.warn(`⚠️ Unsupported simple return type ${returnType.type} for ${methodName}`);
                        return result.stack.readCellOpt();
                    }
                }
                
                // Default to null for unhandled types
                console.warn(`⚠️ Unhandled return type kind ${returnType.kind} for ${methodName}`);
                return null;
            } catch (error) {
                if (error.message?.includes('exit_code')) {
                    console.debug(`Contract returned exit_code for ${methodName}, returning null`);
                    return null;
                }
                if (error.message?.includes('Unsupported stack item type')) {
                    console.error(`❌ Unsupported stack item in ${methodName}:`, error.message, { stack: result?.stack?.items });
                    return null;
                }
                console.error(`❌ Error in callGetterTyped for ${methodName}: ${error.message}`, { stack: result?.stack?.items });
                return null;
            }
        },
        
        /**
         * Dynamically create method based on getter name
         * Converts snake_case getter names to camelCase method names
         * @param {string} getterName - Name of the getter from ABI (e.g., 'get_latest_log_id')
         * @returns {Function} Function that calls the getter
         */
        createMethod(getterName) {
            return async (...args) => {
                return await this.callGetterTyped(getterName, args);
            };
        }
    };
    
    // Automatically create methods for all getters in ABI
    if (abi.getters) {
        for (const getter of abi.getters) {
            const methodName = getter.name
                .split('_')
                .map((word, index) => {
                    if (index === 0) return word;
                    return word.charAt(0).toUpperCase() + word.slice(1);
                })
                .join('');
            
            caller[methodName] = caller.createMethod(getter.name);
        }
    }
    
    return caller;
}

/**
 * ABI Parser class for advanced usage
 */
export class ABIParser {
    constructor(abiFileName, options = {}) {
        this.abi = loadABI(abiFileName);
        this.validate();
        this.options = {
            bounceableAddresses: false,
            ...options
        };
    }
    
    validate() {
        if (!validateABI(this.abi)) {
            throw new Error('Invalid ABI structure');
        }
    }
    
    getContractName() {
        return this.abi.name;
    }
    
    getGetters() {
        return this.abi.getters || [];
    }
    
    getTypes() {
        return this.abi.types || [];
    }
    
    getGetter(methodName) {
        return getGetterDefinition(this.abi, methodName);
    }
    
    getType(typeName) {
        return getTypeDefinition(this.abi, typeName);
    }
    
    /**
     * Create a contract caller instance
     * @param {TonClient} client - TON client instance
     * @param {string} contractAddress - Contract address
     * @param {Object} options - Configuration options (overrides constructor options)
     * @returns {Object} Contract caller with auto-generated methods
     */
    createCaller(client, contractAddress, options = {}) {
        return createContractCaller(this.abi, client, contractAddress, {
            ...this.options,
            ...options
        });
    }
    
    /**
     * List all available getter methods
     * @returns {Array<string>} Array of getter method names
     */
    listGetterMethods() {
        return this.abi.getters?.map(g => g.name) || [];
    }
    
    /**
     * Get information about a specific getter
     * @param {string} methodName - Name of the getter
     * @returns {Object|null} Getter information or null
     */
    getGetterInfo(methodName) {
        const getter = this.getGetter(methodName);
        if (!getter) return null;
        
        return {
            name: getter.name,
            arguments: getter.arguments || [],
            returnType: getter.returnType,
            description: getter.description || 'No description available'
        };
    }
    
    /**
     * Debug contract state by calling a getter and logging raw stack
     * @param {TonClient} client - TON client instance
     * @param {string} contractAddress - Contract address
     * @param {string} methodName - Getter method name
     * @param {Array} args - Method arguments
     * @returns {Promise<any>} Raw stack items
     */
    async debugGetter(client, contractAddress, methodName, args = []) {
        const caller = this.createCaller(client, contractAddress);
        const result = await caller.callGetter(methodName, args);
        if (result) {
            console.log(`Raw stack for ${methodName}:`, stringifyTonTuple(result.stack.items));
            
            // Try to inspect the first item in detail
            if (result.stack.items.length > 0 && result.stack.items[0].type === 'tuple') {
                const tupleItems = result.stack.items[0].items;
                console.log('Tuple items details:');
                tupleItems.forEach((item, i) => {
                    console.log(`  [${i}]:`, {
                        value: item,
                        type: typeof item,
                        constructor: item?.constructor?.name,
                        string: String(item),
                        hasLoadAddress: typeof item?.loadAddress === 'function',
                        hasBeginParse: typeof item?.beginParse === 'function',
                        keys: item && typeof item === 'object' ? Object.keys(item) : []
                    });
                });
            }
            
            return result.stack.items;
        }
        return null;
    }
}

export default {
    loadABI,
    parseTupleByTypeDefinition,
    getGetterDefinition,
    getTypeDefinition,
    validateABI,
    createContractCaller,
    ABIParser,
    decodeAddressFromRawItem,
    addressToString,
    readAddressSmart,
    detectItemType,
    getItemValue
};
