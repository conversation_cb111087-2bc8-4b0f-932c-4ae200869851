<?php

namespace app\controllers;

use Yii;
use yii\web\Response;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use app\models\AdminLoginSession;
use app\models\Admin;
use app\forms\LoginForm;
use app\forms\ResetPasswordForm;

class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['logout'],
                'rules' => [
                    [
                        'actions' => ['logout'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
                'except' => [
                    'ping'
                ]
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'logout' => ['post'],
                    'ping' => ['post']
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        return $this->redirect(['login']);
    }

    protected function throwFirstError($model)
    {
        throw new \Exception(current($model->getFirstErrors()));
    }

    /**
     * Checks if the user is logged in and update the last activity in session
     *
     * @return array
     */
    public function actionPing()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!Yii::$app->user->isGuest) {
            Yii::$app->session->set('last_activity', time());
            return ['success' => true];
        }
        return ['success' => false];
    }

    /**
     * Login action.
     *
     * @return string|array
     */
    public function actionLogin()
    {
        $this->layout = 'login';

        // if user already logged in, redirect to user management page
        if (!Yii::$app->user->isGuest) {
            return $this->redirect(['admin/user-mgmt']);
        }

        $model = new LoginForm();

        $request = Yii::$app->request;
        $db      = Yii::$app->db->beginTransaction();

        try {
            if (!empty($request->post())) {
                if ($request->post('Login', false)) {
                    $model->load($request->post());

                    if (empty($model->email)) {
                        throw new \Exception(Yii::t('app', 'Please Enter Your Email Address'));
                    }

                    if (empty($model->password)) {
                        throw new \Exception(Yii::t('app', 'Please Enter Your Password'));
                    }

                    $model->login();

                    $user = Yii::$app->user->identity;

                    // record admin login session
                    if (!empty($user)) {
                        $record_ip = AdminLoginSession::record($user->id, 'login', Yii::$app->request->userAgent);
                        $db->commit();

                        if ($user->role == "admin") {
                            return $this->redirect(['admin/user-mgmt']);
                        }

                        return $this->redirect(['admin/change-password']);
                    }
                }
            }
        } catch (\Exception $e) {
            $db->rollback();
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['site/login']);
        }

        return $this->render('login', compact('model'));
    }

    /**
     * Request password code action
     *
     * @return string|array
     */
    public function actionRequestPasswordCode()
    {
        $this->layout = 'login';

        // if user already logged in, redirect to login page
        if (!Yii::$app->user->isGuest) {
            return $this->redirect(['site/login']);
        }

        $model = new ResetPasswordForm;
        $model->scenario = "request-code";

        $request = Yii::$app->request;
        $db      = Yii::$app->db->beginTransaction();

        try {
            if (!empty($request->post())) {
                if ($request->post('Request', false)) {
                    $model->load($request->post());

                    if (!$model->validate()) {
                        $this->throwFirstError($model);
                    }

                    $second    = Yii::$app->params['ratelimit_lock_second'];
                    $error_msg = Yii::t('app', "Please request login code after {time} minutes", [
                        'time' => round($second / 60)
                    ]);

                    // get admin user by email
                    $user = Admin::findByUsername($model->email);

                    if (empty($user)) {
                        throw new \Exception(Yii::t('app', 'Email Address Not Found'));
                    }

                    // check rate limit (attempt limit) by session
                    Yii::$app->util->checkIpRateLimitBySession(Yii::$app->params['RequestCoderatelimit_key'], $error_msg, Yii::$app->params['RequestCodeRatelimit_max_attempt']);

                    if (!empty($user)) {
                        $minute_type = 5;

                        $user->otp_code   = strtoupper(substr(str_shuffle("qwertyuiopasdfghjklzxcvbnm"), 0, 6)); // generate 6 random letter code
                        $user->valid_time = strtotime("+{$minute_type} minutes", strtotime("now"));
                        $user->updated_at = strtotime(date('Y-m-d H:i:s'));

                        if ($user->update(false, ['otp_code', 'valid_time', 'updated_at']) === false) {
                            throw new \Exception(current($model->getFirstErrors()));
                        }

                        //send email
                        $this->layout = '@app/mail/layouts/html';
                        $subject = 'Reset Password Code';

                        $content_params =  [
                            'subject'             => $subject,
                            'email_address'       => $model->email,
                            'otp_code'            => $user->otp_code,
                            'company_name'        => Yii::$app->params['company_name'],
                            'company_name_footer' => Yii::$app->params['email_footer'],
                            'company_logo'        => Yii::$app->params['company_logo'],
                        ];
                        $content = $this->render('@app/mail/codev3', $content_params);

                        $model->sendEmail($content, $model->email, $subject);

                        Yii::$app->session->setFlash('success', Yii::t('app', "Reset password code has been sent to $model->email"));
                        $db->commit();

                        return $this->redirect(['reset-password', 'email_address' => $model->email]);
                    }
                }
            }
        } catch (\Exception $e) {
            $db->rollback();
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['request-password-code']);
        }

        return $this->render('request_password_code', compact('model'));
    }

    /**
     * Reset password action
     *
     * @param string $email_address Email address that associated with the account
     *
     * @return string|array
     */
    public function actionResetPassword($email_address)
    {
        $this->layout = 'login';

        // if user already logged in, redirect to login page
        if (!Yii::$app->user->isGuest) {
            return $this->redirect(['site/login']);
        }

        $model = new ResetPasswordForm;
        $model->scenario = "reset-password";

        $request = Yii::$app->request;
        $db      = Yii::$app->db->beginTransaction();

        try {
            if (!empty($request->post())) {
                if ($request->post('Reset', false)) {
                    $model->load($request->post());

                    if (!$model->validate()) {
                        $this->throwFirstError($model);
                    }

                    // reset password if Reset Code button is clicked
                    $model->resetPassword();
                    $db->commit();

                    Yii::$app->session->setFlash('success', Yii::t('app', "Reset password successfully"));

                    return $this->redirect(['site/login']);
                }
            }
        } catch (\Exception $e) {
            $db->rollback();
            Yii::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect(['reset-password', 'email_address' => $email_address]);
        }

        return $this->render('reset_password', compact('model'));
    }

    /**
     * Logout action
     *
     * Logs out the current user and record the session to the database
     *
     * @return mixed
     */
    public function actionLogout()
    {
        $user = Yii::$app->user->identity;

        // record admin logout session
        if (!empty($user)) {
            AdminLoginSession::record($user->id, 'logout', Yii::$app->request->userAgent);
        }

        Yii::$app->user->logout();

        return $this->goHome();
    }

    /**
     * Handles the Error action.
     *
     * This is the default action to handle the 404 error page.
     *
     * @return mixed
     */
    public function actionError()
    {
        $this->layout = 'error';

        return $this->render('error');
    }
}
