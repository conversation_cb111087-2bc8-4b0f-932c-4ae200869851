const CREDIT_TYPE = Object.freeze({
    ALEX: 'alex',
    ENERGY: 'energy',
});

const WALLET_TRANSACTION_CREDIT_PROCESS_TYPE = Object.freeze({
    ALEX: 'alex',
    ENERGY: 'energy',
});

const WALLET_TRANSACTION_TYPE = Object.freeze({
    IN: 'IN',
    OUT: 'OUT',
});

const CACHE_KEYS = {
    TASK_LOOKUP: 'task_lookup',
};

export default {
    CACHE_KEYS,
    CREDIT_TYPE,
    WALLET_TRANSACTION_TYPE,
    WALLET_TRANSACTION_CREDIT_PROCESS_TYPE
};