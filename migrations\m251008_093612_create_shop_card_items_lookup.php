<?php

use yii\db\Migration;

class m251008_093612_create_shop_card_items_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%shop_card_items_lookup}}', [
            'id' => $this->primaryKey(),
            'item_one' => $this->string()->notNull(),
            'item_two' => $this->string()->notNull(),
            'item_three' => $this->string()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%shop_card_items_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093612_create_shop_card_items_lookup cannot be reverted.\n";

        return false;
    }
    */
}
