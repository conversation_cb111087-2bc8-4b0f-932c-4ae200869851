<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%country}}`.
 */
class m250826_063844_create_country_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%country}}', [
            'id'         => $this->primaryKey(),
            'iso'        => "varchar(10)",
            'name'       => "varchar(80)",
            'nicename'   => "varchar(80)",
            'iso3'       => "varchar(10)",
            'numcode'    => "int",
            'phonecode'  => "int",
            'currency'   => 'varchar(50)',
            'is_active'  => 'tinyint(1) default 0',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete'  => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByIso` (`iso`, `is_delete`)',
            'key `findByIso3` (`iso3`, `is_delete`)',
            'key `findByName` (`name`, `is_delete`)',
            'key `findByIsActive` (`is_active`, `is_delete`)',
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%country}}');
    }
}
