<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use app\assets\AppAsset;
use app\assets\AdminLteAsset;
use app\assets\ClipboardAsset;
use app\assets\IdleTimerAsset;

IdleTimerAsset::register($this);
AppAsset::register($this);
AdminLteAsset::register($this);
ClipboardAsset::register($this);

$this->registerCssFile('https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback');

$copied = json_encode(Yii::t('app', 'Copied'));

$script = <<< JS
  var copied = $copied;

  $(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip();
  });

  $(".fa-copy").tooltip({
    trigger  : "click",
    placement: "bottom"
  });

  function setTooltip(btn, message) {
    $(btn).tooltip("hide")
    .attr("data-original-title", message)
    .tooltip("show");
  }

  function hideTooltip(btn) {
    setTimeout(function() {
      $(btn).tooltip("hide");
    }, 2000);
  }

  var clipboard = new ClipboardJS('.fa-copy');
  clipboard.on('success', function(e) {
    e.clearSelection();
    setTooltip(e.trigger, copied);
    hideTooltip(e.trigger);
  });
JS;

$this->registerJs($script, View::POS_READY);

// $this->registerJs("
//   var idleConfig = {
//     pingUrl: '" . Url::to(['/site/ping']) . "',
//     logoutUrl: '" . Url::to(['/site/logout']) . "',
//     logoutTime: 15, // minutes
//     warningTime: 1,  // minutes before warning
//     warningMessage: 'Your session has expired due to inactivity',
//   };
// ", View::POS_HEAD);

?>

<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">

<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>

    <link rel="shortcut icon" href="<?php echo Yii::getAlias('@web/favicon.ico'); ?>" type="image/x-icon" />
</head>

<body class="sidebar-mini">
    <?php $this->beginBody() ?>

    <div class="wrapper">
      <div id="generalModal" class="modal">
        <div class="modal-dialog modal-lg modal-full">
          <div class="modal-content">
            <div id="modalContent"></div>
          </div>
        </div>
      </div>

        <!-- Navbar -->
        <?= $this->render('navbar') ?>

        <!-- Main Sidebar Container -->
        <?= $this->render('sidebar') ?>

        <!-- Content Wrapper. Contains page content -->
        <?= $this->render('content', ['content' => $content]) ?>

        <!-- Main Footer -->
        <?= $this->render('footer') ?>
    </div>

    <?php $this->endBody() ?>
</body>

</html>
<?php $this->endPage() ?>