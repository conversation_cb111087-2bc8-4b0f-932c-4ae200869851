<?php

use yii\web\View;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'email')->textInput(['placeholder' => ''])->label(Yii::t('app', "Email")) ?>
      <?= $form->field($model, 'function')->dropDownList($model->getFunction(), [
        'prompt' => Yii::t('app', 'All'),
      ])->label(Yii::t('app', 'Action')) ?>
      <?= $form->field($model, 'date_range')->widget(DateRangePicker::className(), [
        'model'         => $model,
        'attribute'     => 'date_range',
        'convertFormat' => true,
        'pluginOptions' => [
          'timePicker'  => false,
          'locale' => [
            'format' => 'Y-m-d'
          ]
        ],
        'options' => [],
      ])->label(Yii::t('app', 'Date')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-success float-right']) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>
