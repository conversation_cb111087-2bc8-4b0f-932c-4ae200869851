<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Language;
use app\models\TranslationLookup;

class TranslationApiForm extends Model
{
  public $lang;
  public $trans_json;

  public function rules()
  {
    return [
      [['lang', 'trans_json'],'safe'],
      [['lang', 'trans_json'], 'required', 'on' => 'insert'],
      [['lang', 'trans_json'], 'required', 'on' => 'api-insert'],
      [['lang'], 'validateLang'],
    ];
  }

  public function validateLang($attribute, $params)
  {
    $_lang = Language::findOne([
      'code'      => $this->lang,
      'is_delete' => 0
    ]);

    if (empty($_lang)) {
      $this->addError($attribute, Yii::t('app','Language not found'));
    }
  }

  public function setValueByLang()
  {
    $_lang = Language::findOne([
      'code'      => $this->lang,
      'is_delete' => 0
    ]);

    if (empty($_lang)) {
      throw new \Exception(Yii::t('app','Language not found'));
    }

    $decode_word = json_decode($this->trans_json, true);

    foreach ($decode_word as $dw => $wo) {
      $model = TranslationLookup::createOrUpdateEn($dw, $wo);
    }

    return true;
  }

  public function getValueByLang()
  {
    $_lang = Language::findOne([
      'code'      => $this->lang,
      'is_delete' => 0
    ]);

    if (empty($_lang)) {
      throw new \Exception(Yii::t('app','Language not found'));
    }

    $lang_array = [];

    $lower_case_key = str_replace("-","_", strtolower($_lang->code));

    $model = TranslationLookup::findAll(['is_delete' => 0]);

    foreach ($model as $m) {
      $lang_array[$m->message_key] = $m->{$lower_case_key};
    }

    $final_json = json_encode($lang_array);

    return $final_json;
  }

  public function insertValueByLang()
  {
    $_lang = Language::findOne([
      'code'      => $this->lang,
      'is_delete' => 0
    ]);

    if (empty($_lang)) {
      throw new \Exception(Yii::t('app','Language not found'));
    }

    $decode_word = json_decode($this->trans_json, true);

    foreach ($decode_word as $dw => $wo) {
      $model = TranslationLookup::insertValueByLang($_lang->code, $dw, $wo);
    }

    return true;
  }
}