<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$project= 'aleko';

Yii::setAlias("@s3-export","{$project}/export/");
Yii::setAlias("@s3-icon","{$project}/images/icons/");
Yii::setAlias("@s3-translation","{$project}/translation/");

$config = [
    'id' => 'basic',
    'name' => 'Aleko',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'modules' => [
        'gridview' =>  [
            'class' => '\kartik\grid\Module'
        ],
    ],
    'components' => [
        'formatter' => [
            'defaultTimeZone' => 'Asia/Kuala_Lumpur',
        ],
        'util' => [
            'class' => 'app\components\Util',
        ],
        'api' => [
            'class' => 'app\components\API',
        ],
        'formatHelper' => [
            'class' => 'app\components\FormatHelper',
        ],
        'auditLog' => [
            'class' => 'app\components\AuditLogComponent',
        ],
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '5yejq5_K2szCCUYjkGM5xh4jGzlbdYfc',
            'parsers' => [
                'application/json' => 'yii\web\JsonParser'
            ],
            'trustedHosts' => [
                '127.0.0.1',
            ],
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'user' => [
            'identityClass' => 'app\models\Admin',
            'enableAutoLogin' => false,
            'enableSession' => true,
            'authTimeout' => 86400, // 1 day
            'identityCookie' => ['name' => '_identity-frontend', 'httpOnly' => true],
        ],
        'error' => [
            'class' => 'app\components\ErrorCode'
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => true,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => $db,
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [],
        ],
        'aws' => [
            'class'  => 'app\components\AWS',
            'key' => '********************',
            'secret' => 'yF+g+89teRB0cEjK9FggM5BJ1kWY+XhAEpKlThCq',
            'region' => 'ap-southeast-1',
        ],
        'mandrill' => [
            'class'          => 'app\components\Mandrill',
            'api_key'        => '',
            'from_name'      => '',
            'from_email'     => '',
            'reply_to_email' => '',
            'company_name'   => '',
            'company_logo'   => '',
        ],
        'session' => [
            'class' => 'yii\web\Session',
            'cookieParams' => ['httponly' => true, 'lifetime' => 1800],
            'timeout' => 86400, // 1 day
            'useCookies' => true,
        ],
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
