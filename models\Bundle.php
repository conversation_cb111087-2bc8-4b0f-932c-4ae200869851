<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "bundle".
 *
 * @property int $id
 * @property float $usd_price
 * @property int $token_allocation
 * @property int $total_supply
 * @property string|null $status
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class Bundle extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'bundle';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['usd_price', 'token_allocation', 'total_supply', 'created_at'], 'required'],
            [['usd_price'], 'number'],
            [['token_allocation', 'total_supply', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['status'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'usd_price' => 'Usd Price',
            'token_allocation' => 'Token Allocation',
            'total_supply' => 'Total Supply',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    public function getBundleRewards()
    {
        return $this->hasMany(BundleReward::className(), ['bundle_id' => 'id']);
    }
}
