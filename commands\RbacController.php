<?php

/**
 * @link https://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license https://www.yiiframework.com/license/
 */

namespace app\commands;

use Yii;
use yii\console\Exception;
use yii\console\Controller;

use app\models\AdminRole;
use app\models\PermissionQuery;
use app\models\Admin;

class RbacController extends Controller
{
	/**
	 * Get the auth manager instance.
	 * 
	 * @return \yii\rbac\ManagerInterface
	 */
	protected function getAuth()
	{
		return Yii::$app->authManager;
	}

	/**
	 * Create auth item.
	 *
	 * @param string $type
	 * @param string $name
	 * 
	 * @throws Exception
	 */
	protected function createAuthItem($type, $name)
	{
		// only allow role type for now
		if (!in_array($type, ['role']))
			throw new Exception("Invalid role type");

		$auth = $this->getAuth();
		$auth_item = $auth->getRole($name);

		// if role not exist, create it
		if (empty($auth_item)) {
			$auth_item = $auth->createRole($name);
			$auth->add($auth_item);

			echo "Create auth item $name\n";
		}
	}

	/**
	 * Initialize the RBAC system.
	 * 
	 * This command will create the auth item for each role found in the database, 
	 * and assign the permission to the admin user based on the permission configuration 
	 * in the database.
	 * 
	 * @return int exit status
	 */
	public function actionInit()
	{
		// create role
		$role = AdminRole::findAll([
			'is_delete' => 0
		]);

		if (empty($role)) {
			echo "User role not configure in database yet\n";
			return true;
		}

		echo "Initial ... \n";

		// init user role
		foreach ($role as $r) {
			$this->createAuthItem('role', $r->name);
		}

		// get permission list
		$permissions = PermissionQuery::getAllPermission();

		if (empty($permissions)) {
			echo "Permission not configure yet\n";
			return true;
		}

		// get all superadmin from db
		$user = Admin::findAll([
			'role'      => 'admin',
			'is_delete' => 0,
		]);

		if (empty($user)) {
			echo "Admin not configure yet\n";
			return true;
		}

		$auth = $this->getAuth();

		foreach ($user as $u) {
			foreach ($permissions as $perm) {
				$auth_item = $auth->getPermission($perm['name']);

				// if permission not exist, create it if is_enable = 1
				if (empty($auth_item) && $perm['is_enable'] == 1) {
					$auth_item = $auth->createPermission($perm['name']);
					$auth_item->description = $perm['desc'];
					$auth_item->data = $perm['group'];
					$auth->add($auth_item);
					echo "[Create] Authorization item '{$perm['name']}'\n";
				}

				// check if user already have permission
				$has_perm = $auth->checkAccess($u->id, $perm['name']);

				// if enable and not have permission, assign it to user else remove it
				if ($perm['is_enable'] == 1 && !$has_perm) {
					$auth->assign($auth_item, $u->id);
					echo "[Assign] Permission '{$perm['name']}' to '{$u->email}'\n";
				} else if ($perm['is_enable'] == 0 && $has_perm) {
					$auth->revoke($auth_item, $u->id);
					echo "[Revoke] Permission '{$perm['name']}' from '{$u->email}'\n";
				}
			}
		}

		echo "[DONE]\n";
	}

	/**
	 * Reset the RBAC system.
	 * 
	 * This command will remove all the auth item and their assignment from the system.
	 * 
	 * @return int exit status
	 */
	public function actionReset()
	{
		$auth = $this->getAuth();
		$auth->removeAll();
	}
}
