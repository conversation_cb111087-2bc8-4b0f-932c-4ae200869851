<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%audit_log}}`.
 */
class m250826_063610_create_audit_log_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%audit_log}}', [
            'id'         => $this->primaryKey(),
            'function'   => $this->string(255),
            'old_value'  => $this->text(),
            'value'      => $this->text(),
            'remark'     => $this->text(),
            'action_by'  => $this->integer(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete'  => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`action_by`) references `admin` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByFunction` (`function`, `is_delete`)',
            'key `findByActionBy` (`action_by`, `is_delete`)',
            'key `findByFunctionAndActionBy` (`function`, `action_by`, `is_delete`)',
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%audit_log}}');
    }
}
