import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();
const sort_social_task = ["daily", "weekly", "social", "game"]

router.get("/14102005", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    try {

        let task_lookup_cache = helper.cache_helper.getCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP);
        let task_list = [];
        if (task_lookup_cache && task_lookup_cache.data && task_lookup_cache.timestamp) {
            if (((current_time - task_lookup_cache.timestamp) / 3600) < common.config.cache_config.duration_hour) {
                task_list = task_lookup_cache.data.filter(task => task.is_delete === false);
            }
        }

        if (!task_list || task_list.length === 0) {
            task_list = await query.models.social_task_lookup.findAll({
                where: {
                    is_delete: 0,
                },
                transaction: transaction
            })
            if (task_list && task_list.length > 0) {
                task_lookup_cache = {
                    data: task_list,
                    timestamp: current_time
                };
                helper.cache_helper.setCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP, task_lookup_cache);
            }
        }

        const user_with_task = await query.models.user.findOne({
            where: {
                id: user_id,
                is_delete: 0
            },
            include: [
                {
                    model: query.models.user_social_task,
                    where: { is_delete: 0 },
                    required: false,
                },
            ],
            transaction: transaction,
            nest: true
        })

        if (!user_with_task) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "User not found.",
                "error": true
            });
        }

        const user_task = user_with_task.user_social_tasks
        const formatted_data = task_list.reduce((acc, task) => {
            const is_task_found = user_task.find(t => t.social_task_id === task.id);
            let task_completed_status = false;
            let to_call_api = false;

            if (task.type === "daily_rewards" && is_task_found) {
                const is_after = common.moment().startOf('day').unix() > parseInt(is_task_found.updated_at || 0);
                task_completed_status = !is_after;
                to_call_api = true;
            } else if (task.type === "social_task") {
                task_completed_status = !!(is_task_found && is_task_found.is_done);
                to_call_api = true;
            } else if (is_task_found && is_task_found.is_done) {
                task_completed_status = true;
                to_call_api = true;
            }

            acc.push({
                id: task.id,
                title: task.task_name,
                type: task.type,
                redirect_url: task.redirect_url,
                icon_image_url: task.icon_image_url,
                task_completed: task_completed_status,
                reward_point: task.reward_point,
                to_call_api: to_call_api
            })
            return acc;
        }, [])

        // Sort formatted_data according to sort_social_task array
        const getTypeOrder = (type) => {
            if (type === "daily_rewards") return sort_social_task.indexOf("daily");
            if (type === "weekly_task") return sort_social_task.indexOf("weekly");
            if (type === "social_task") return sort_social_task.indexOf("social");
            return sort_social_task.indexOf("game"); // default for other types
        };

        formatted_data.sort((a, b) => {
            const orderA = getTypeOrder(a.type);
            const orderB = getTypeOrder(b.type);
            return orderA - orderB;
        });

        await transaction.commit();
        return res.status(200).json({
            "data": formatted_data,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": await common.util_helper.handleErrorMessageAPI(user_id, error),
            "error": true
        });
    }
})

router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();

    try {
        let task_lookup_cache = helper.cache_helper.getCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP);
        let task_list = [];
        if (task_lookup_cache && task_lookup_cache.data && task_lookup_cache.timestamp) {
            if ((current_time - task_lookup_cache.timestamp) / 3600 < common.config.cache_config.duration_hour) {
                task_list = task_lookup_cache.data.filter((task) => task.is_delete === false);
            }
        }

        if (!task_list || task_list.length === 0) {
            task_list = await query.models.social_task_lookup.findAll({
                where: {
                is_delete: 0,
                },
                transaction: transaction,
            });
            if (task_list && task_list.length > 0) {
                task_lookup_cache = {
                data: task_list,
                timestamp: current_time,
                };
                helper.cache_helper.setCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP, task_lookup_cache);
            }
        }

        const user_with_task = await query.models.user.findOne({
            where: {
                id: user_id,
                is_delete: 0
            },
            include: [
                {
                    model: query.models.user_social_task,
                    where: { is_delete: 0 },
                    required: false,
                },
            ],
            transaction: transaction,
            nest: true
        })

        if (!user_with_task) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "User not found.",
                "error": true
            });
        }

        const get_weekly_max_invite = await query.models.system_setting.findOne({
            where: {
                key: "weekly_invite_max_num",
                is_delete: 0,
            },
            transaction: transaction,
        });
        const last_monday_epoch = common.util_helper.getLastMondayEpoch();
        const next_monday_epoch = common.util_helper.getNextMondayEpoch();
        const find_social_task = await query.models.social_task_lookup.findOne({
          where: {
            type: "weekly_task",
            is_delete: 0,
          },
          transaction: transaction,
        });

        if (!find_social_task) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Something went wrong. Please try again.",
                "error": true
            });
        }
        // Check if they already have this week's social task to prevent duplicates
        const existing_weekly_task = await query.models.user_social_task.findOne({
          where: {
            user_id: user_id,
            social_task_id: find_social_task.id,
            created_at: {
              [query.Op.gte]: last_monday_epoch,
              [query.Op.lt]: next_monday_epoch,
            },
            is_delete: 0,
          },
          transaction: transaction,
        });
        const count_invite_this_week = await query.models.user_referral.count({
            where: {
                referral_user_id: user_id,
                is_delete: 0,
                created_at: {
                    [query.Op.gte]: last_monday_epoch,
                    [query.Op.lt]: next_monday_epoch,
                },
            },
            transaction: transaction,
        });

        const formatted_data = task_list.reduce((acc, task) => {
            const typeMapping = {
                "daily_rewards": "daily",
                "weekly_task": "weekly", 
                "social_task": "social"
            };
            const main_title = (typeMapping[task.type] || "game").charAt(0).toUpperCase() + (typeMapping[task.type] || "game").slice(1);
            
            const main_task = acc.find((t) => t.main_title === main_title && t.type === task.type);
            const is_task_found = user_with_task.user_social_tasks.find((t) => t.social_task_id === task.id);
            let task_completed_status = false;
            let to_call_api = false;

            if (task.type === "daily_rewards" && is_task_found) {
                const is_after = common.moment().startOf("day").unix() > parseInt(is_task_found.updated_at || 0);
                task_completed_status = !is_after;
                to_call_api = true;
            } else if (task.type === "weekly_task" && is_task_found) {
                // ADD YOUR WEEKLY TASK LOGIC HERE
                // Example: Check if task was completed within the current week
                if(count_invite_this_week >= parseInt(get_weekly_max_invite.value || 0)) {
                    task_completed_status = true;
                } else {
                    task_completed_status = false;
                }
                to_call_api = false;
            } else if (task.type === "social_task") {
                task_completed_status = !!(is_task_found && is_task_found.is_done);
                to_call_api = true;
            } else if (is_task_found && is_task_found.is_done) {
                task_completed_status = true;
                to_call_api = true;
            }

            const task_data = {
                id: task.id,
                title: task.task_name,
                type: task.type,
                redirect_url: task.redirect_url,
                icon_image_url: task.icon_image_url,
                task_completed: task_completed_status,
                reward_point: task.reward_point,
                to_call_api: to_call_api,
            }

            // Add custom data only for weekly tasks
            if (task.type === "weekly_task") {
                task_data.invite_count_this_week = count_invite_this_week;
                task_data.weekly_max_invite = parseInt(get_weekly_max_invite.value || 0);
            }

            if (main_task) {
                main_task.task_listing.push(task_data);
            } else {
                acc.push({
                    main_title: main_title,
                    type: task.type,
                    task_listing: [task_data],
                });
            }
            return acc;
        }, [])

        // Sort formatted_data according to sort_social_task array
        const getTypeOrder = (type) => {
            if (type === "daily_rewards") return sort_social_task.indexOf("daily");
            if (type === "weekly_task") return sort_social_task.indexOf("weekly");
            if (type === "social_task") return sort_social_task.indexOf("social");
            return sort_social_task.indexOf("game"); // default for other types
        };

        formatted_data.sort((a, b) => {
            const orderA = getTypeOrder(a.type);
            const orderB = getTypeOrder(b.type);
            return orderA - orderB;
        });

        await transaction.commit();
        return res.status(200).json({
            "data": formatted_data,
            "status": 200,
            "msg": `OK`,
            "error": false,
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": await common.util_helper.handleErrorMessageAPI(user_id, error),
            "error": true,
        });
    }
})

router.post("/daily-check-in", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();

    try {
        let social_task = await query.models.social_task_lookup.findOne({ where: { type: "daily_rewards", is_delete: 0 }, transaction: transaction })
        if (!social_task) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Task not found.",
                "error": true
            });
        }

        //Purely Check Caching Part
        let task_lookup_cache = helper.cache_helper.getCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP);
        let task_list = [];
        if (task_lookup_cache && task_lookup_cache.data && task_lookup_cache.timestamp) {
            if (((current_time - task_lookup_cache.timestamp) / 3600) < common.config.cache_config.duration_hour) {
                task_list = task_lookup_cache.data.filter(task => task.is_delete === false);
            }
        }
        if (!task_list || task_list.length === 0) {
            task_list = await query.models.social_task_lookup.findAll({
                where: {
                    is_delete: 0,
                },
                transaction: transaction
            })
            if (task_list && task_list.length > 0) {
                task_lookup_cache = {
                    data: task_list,
                    timestamp: current_time
                };
                helper.cache_helper.setCache(common.enum_key.CACHE_KEYS.TASK_LOOKUP, task_lookup_cache);
            }
        }

        //Start Checking Daily Checkin DB record
        const user_task = await query.models.user_social_task.findOne({
            where: {
                user_id: user_id,
                social_task_id: social_task.id,
                is_delete: 0
            },
            transaction: transaction
        })
        let user_daily_reward;
        if (!user_task) {
            user_daily_reward = await query.models.user_social_task.create({
                user_id: user_id,
                social_task_id: social_task.id,
                is_done: true,
                created_at: common.util_helper.getCurrentEpochTime(),
                updated_at: null,
            }, {
                transaction: transaction,
            })
        } else {
            user_daily_reward = user_task;
        }
        if (user_daily_reward.updated_at) {
            const is_after = common.moment().startOf('day').unix() > parseInt(user_daily_reward.updated_at);
            if (!is_after) {
                await transaction.rollback();
                return res.status(400).json({
                    data: {},
                    status: 400,
                    msg: "Daily check-in already completed. Cannot check-in again.",
                    error: true
                });
            }
        }
        let [test_user_daily_reward, is_test_user_daily_reward_created] = await query.models.user_social_task.findOrCreate({
            where: { user_id: user_id, social_task_id: social_task.id, is_delete: 0 },
            defaults: {
                user_id: user_id,
                social_task_id: social_task.id,
                is_done: true,
                created_at: common.util_helper.getCurrentEpochTime(),
                updated_at: null,
                is_delete: 0,
            },
            lock: transaction.LOCK.UPDATE,
            transaction: transaction,
        });
        user_daily_reward = test_user_daily_reward;
        await user_daily_reward.update({ updated_at: common.moment().startOf('day').unix() }, { lock: transaction.LOCK.UPDATE, transaction: transaction });
        //End Checking Daily Checkin DB record


        //Start distribute rewards
        const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
            social_task.reward_point,
            common.enum_key.WALLET_TRANSACTION_TYPE.IN,
            null,
            "social_task",
            transaction
        );
        //End distribute rewards

        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id} --> wallet_id: ${wallet_id} --> wallet_address: ${wallet_address}`, error),
            error: true
        });
    }
})

router.post("/:task_id", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    const task_id = req.params.task_id;
    try {
        let find_task = await query.models.social_task_lookup.findOne({ where: { id: task_id, is_delete: 0 }, transaction: transaction })
        if (!find_task) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Task not found.",
                "error": true
            });
        }
        if (find_task.type !== "social_task") {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Invalid social task action.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        }


        //Start checking task attempt
        let find_user_task = await query.models.user_social_task.findOne({ where: { user_id: user_id, social_task_id: task_id }, lock: transaction.LOCK.UPDATE, transaction: transaction });
        if (!find_user_task) {
            find_user_task = await query.models.user_social_task.create({
                user_id: user_id,
                social_task_id: find_task.id,
                is_done: 1,
                created_at: common.util_helper.getCurrentEpochTime(),
                updated_at: common.util_helper.getCurrentEpochTime(),
            }, {
                transaction: transaction,
            })
        } else if (find_user_task.is_done === true) {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Social task already completed.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        } else {
            await find_user_task.update({
                is_done: 1,
                updated_at: common.util_helper.getCurrentEpochTime()
            }, {
                lock: transaction.LOCK.UPDATE,
                transaction: transaction,
            })
        }
        //End checking task attempt


        //Start distribute rewards
        const wallet_transaction_processor = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
            find_task.reward_point,
            common.enum_key.WALLET_TRANSACTION_TYPE.IN,
            null,
            "social_task",
            transaction
        );
        //End distribute rewards

        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id} --> wallet_id: ${wallet_id} --> wallet_address: ${wallet_address}`, error),
            error: true
        });
    }
})

export default router;