
import config from "../../../config.json" assert { type: "json" };
import { Sequelize, DataTypes, Op, fn, col } from "sequelize";
const sequelize = new Sequelize(
    config.database.mysql.dbName,
    config.database.mysql.dbUser,
    config.database.mysql.dbPassword,
    config.database.mysql,
);

import _user from "./model/account/user.js"
import _credit from "./model/account/credit.js"
import _user_referral from "./model/account/user_referral.js"
import _wallet from "./model/account/wallet.js"
import _social_task_lookup from "./model/tasks/social_task_lookup.js"
import _user_social_task from "./model/tasks/user_social_task.js"
import _wallet_transaction from "./model/transactions/wallet_transaction.js"
import _notice_lookup from "./model/notice/notice_lookup.js"
import _user_notice_reward from "./model/notice/user_notice_reward.js"
import _referral_reward_lookup from "./model/referral/referral_reward_lookup.js"
import _user_referral_reward from "./model/referral/user_referral_reward.js"
import _system_setting from "./model/platform/system_setting.js"
import _user_login_session from "./model/platform/user_login_session.js"
import _stage from "./model/presale/stage.js"
import _stage_purchase from "./model/presale/stage_purchase.js"
import _bundle from "./model/presale/bundle.js"
import _bundle_reward from "./model/presale/bundle_reward.js"
import _bundle_purchase from "./model/presale/bundle_purchase.js"

//v1
import _instrument_lookup from "./model/v1/instrument/instrument_lookup.js"
import _rarity_lookup from "./model/v1/instrument/rarity_lookup.js"
import _shop_card_lookup from "./model/v1/shop/shop_card_lookup.js"
import _user_shop_card from "./model/v1/user/user_shop_card.js"
import _user_reset_card from "./model/v1/user/user_reset_card.js"
import _shop_card_items_lookup from "./model/v1/shop/shop_card_items_lookup.js"
import _shop_item_lookup from "./model/v1/shop/shop_item_lookup.js"
import _user_shop_item from "./model/v1/user/user_shop_item.js"
import _song_lookup from "./model/v1/song/song_lookup.js"
import _advance_genre_lookup from "./model/v1/song/advance_genre_lookup.js"
import _user_song_selected_lookup from "./model/v1/user/user_song_selected_lookup.js"

function initMysql() {
    const user = _user(sequelize, DataTypes);
    const credit = _credit(sequelize, DataTypes);
    const user_referral = _user_referral(sequelize, DataTypes);
    const wallet = _wallet(sequelize, DataTypes);
    const social_task_lookup = _social_task_lookup(sequelize, DataTypes);
    const user_social_task = _user_social_task(sequelize, DataTypes);
    const wallet_transaction = _wallet_transaction(sequelize, DataTypes);
    const notice_lookup = _notice_lookup(sequelize, DataTypes);
    const user_notice_reward = _user_notice_reward(sequelize, DataTypes);
    const referral_reward_lookup = _referral_reward_lookup(sequelize, DataTypes);
    const user_referral_reward = _user_referral_reward(sequelize, DataTypes);
    const system_setting = _system_setting(sequelize, DataTypes);
    const user_login_session = _user_login_session(sequelize, DataTypes);
    const instrument_lookup = _instrument_lookup(sequelize, DataTypes);
    const rarity_lookup = _rarity_lookup(sequelize, DataTypes);
    const shop_card_lookup = _shop_card_lookup(sequelize, DataTypes);
    const user_shop_card = _user_shop_card(sequelize, DataTypes);
    const user_reset_card = _user_reset_card(sequelize, DataTypes);
    const shop_card_items_lookup = _shop_card_items_lookup(sequelize, DataTypes);
    const shop_item_lookup = _shop_item_lookup(sequelize, DataTypes);
    const user_shop_item = _user_shop_item(sequelize, DataTypes);
    const song_lookup = _song_lookup(sequelize, DataTypes);
    const advance_genre_lookup = _advance_genre_lookup(sequelize, DataTypes);
    const user_song_selected_lookup = _user_song_selected_lookup(sequelize, DataTypes);
    const stage = _stage(sequelize, DataTypes);
    const stage_purchase = _stage_purchase(sequelize, DataTypes);
    const bundle = _bundle(sequelize, DataTypes);
    const bundle_reward = _bundle_reward(sequelize, DataTypes);
    const bundle_purchase = _bundle_purchase(sequelize, DataTypes);

    user.hasMany(credit, { foreignKey: "user_id" });
    credit.belongsTo(user, { foreignKey: "user_id" });

    user.hasOne(user_referral, { foreignKey: "user_id" });
    user_referral.belongsTo(user, { foreignKey: "user_id" });

    user.hasMany(user_social_task, { foreignKey: "user_id" })
    user_social_task.belongsTo(user, { foreignKey: "user_id" })

    credit.hasMany(wallet_transaction, { foreignKey: "credit_id" });
    wallet_transaction.belongsTo(credit, { foreignKey: "credit_id" });

    user.hasMany(user_notice_reward, { foreignKey: "user_id" });
    user_notice_reward.belongsTo(user, { foreignKey: "user_id" })

    user.hasMany(user_referral_reward, { foreignKey: "user_id" })
    user_referral_reward.belongsTo(user, { foreignKey: "user_id" })

    // --- Rarity <--> Shop Card ---
    // A rarity can have MANY shop cards
    rarity_lookup.hasMany(shop_card_lookup, { foreignKey: "rarity_id" });
    shop_card_lookup.belongsTo(rarity_lookup, { foreignKey: "rarity_id" });

    // --- User <--> User Shop Card ---
    user.hasMany(user_shop_card, { foreignKey: "user_id" });
    user_shop_card.belongsTo(user, { foreignKey: "user_id" });

    // --- Rarity <--> User Shop Card ---
    rarity_lookup.hasMany(user_shop_card, { foreignKey: "rarity_id" });
    user_shop_card.belongsTo(rarity_lookup, { foreignKey: "rarity_id" });

    // --- Instrument <--> User Shop Card ---
    instrument_lookup.hasMany(user_shop_card, { foreignKey: "instrument_id" });
    user_shop_card.belongsTo(instrument_lookup, { foreignKey: "instrument_id" });

    // --- User <--> User Reset Card ---
    user.hasMany(user_reset_card, { foreignKey: "user_id" });
    user_reset_card.belongsTo(user, { foreignKey: "user_id" });

    // --- User <--> User Shop Item ---
    user.hasMany(user_shop_item, { foreignKey: "user_id" });
    user_shop_item.belongsTo(user, { foreignKey: "user_id" });

    // --- Item <--> User Shop Item ---
    shop_item_lookup.hasMany(user_shop_item, { foreignKey: "item_id" });
    user_shop_item.belongsTo(shop_item_lookup, { foreignKey: "item_id" });

    // --- Instrument <--> Advance Genre ---
    advance_genre_lookup.belongsTo(instrument_lookup, {
        foreignKey: 'boost_instrument_one_id',
        as: 'boost_instrument_one'
    });
    advance_genre_lookup.belongsTo(instrument_lookup, {
        foreignKey: 'boost_instrument_two_id',
        as: 'boost_instrument_two'
    });
    advance_genre_lookup.belongsTo(instrument_lookup, {
        foreignKey: 'boost_instrument_three_id',
        as: 'boost_instrument_three'
    });
    advance_genre_lookup.belongsTo(instrument_lookup, {
        foreignKey: 'boost_instrument_four_id',
        as: 'boost_instrument_four'
    });

    // --- User <--> User Song Selected ---
    user_song_selected_lookup.belongsTo(user, { foreignKey: 'user_id' });
    user.hasMany(user_song_selected_lookup, { foreignKey: 'user_id' });

    user_song_selected_lookup.belongsTo(song_lookup, { foreignKey: 'song_id' });
    song_lookup.hasMany(user_song_selected_lookup, { foreignKey: 'song_id' });

    user_song_selected_lookup.belongsTo(advance_genre_lookup, { foreignKey: 'genre_id' });
    advance_genre_lookup.hasMany(user_song_selected_lookup, { foreignKey: 'genre_id' });

    user_song_selected_lookup.belongsTo(user_shop_card, { foreignKey: 'boost_instrument_shop_card_one_id', as: 'boost_card_one' });
    user_song_selected_lookup.belongsTo(user_shop_card, { foreignKey: 'boost_instrument_shop_card_two_id', as: 'boost_card_two' });
    user_song_selected_lookup.belongsTo(user_shop_card, { foreignKey: 'boost_instrument_shop_card_three_id', as: 'boost_card_three' });
    user_song_selected_lookup.belongsTo(user_shop_card, { foreignKey: 'boost_instrument_shop_card_four_id', as: 'boost_card_four' });

    user_song_selected_lookup.belongsTo(user_shop_item, { foreignKey: 'exp_booster_user_shop_item_id', as: 'exp_booster' });
    user_song_selected_lookup.belongsTo(user_shop_item, { foreignKey: 'token_booster_user_shop_item_id', as: 'token_booster' });
    user_song_selected_lookup.belongsTo(user_shop_item, { foreignKey: 'tap_booster_user_shop_item_id', as: 'tap_booster' });

    stage_purchase.belongsTo(stage, { foreignKey: "stage_id" });
    stage_purchase.belongsTo(user, { foreignKey: "user_id" });
    stage_purchase.belongsTo(wallet, { foreignKey: "wallet_id" });

    bundle.hasMany(bundle_reward, { foreignKey: "bundle_id" });
    bundle_reward.belongsTo(bundle, { foreignKey: "bundle_id" });

    bundle_purchase.belongsTo(bundle, { foreignKey: "bundle_id" });
    bundle_purchase.belongsTo(user, { foreignKey: "user_id" });
    bundle_purchase.belongsTo(wallet, { foreignKey: "wallet_id" });

    wallet.belongsTo(user, { foreignKey: "user_id" });
    user.hasMany(wallet, { foreignKey: "user_id" });

    return {
        models: {
            user,
            credit,
            user_referral,
            wallet,
            social_task_lookup,
            user_social_task,
            wallet_transaction,
            notice_lookup,
            user_notice_reward,
            referral_reward_lookup,
            user_referral_reward,
            system_setting,
            user_login_session,
            stage,
            stage_purchase,
            bundle,
            bundle_reward,
            bundle_purchase,

            //v1
            instrument_lookup,
            rarity_lookup,
            shop_card_lookup,
            user_shop_card,
            user_reset_card,
            shop_card_items_lookup,
            shop_item_lookup,
            user_shop_item,
            song_lookup,
            advance_genre_lookup,
            user_song_selected_lookup,
        },
        sequelize,
        Op,
        fn,
        col,
        SQF: Sequelize,
    };
}

export default initMysql;
export { initMysql };
