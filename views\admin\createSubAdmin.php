<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use yii\widgets\Breadcrumbs;

$this->title = Yii::t('app', 'Create Subadmin');

?>

<div class="card card-default">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i>{link} / </i> </li>\n",
    'links' => [
      ['label' => 'Manage Subadmin', 'url' => ['admin/sub-admin-mgmt']],
      'Sign Up Subadmin',
    ],
  ]);
  ?>

  <?php $form = ActiveForm::begin([
    'id'     => 'your-form',
    'method' => 'post',
    'options' => [
      'data-pjax' => true,
    ],
    'fieldConfig' => [
      'inputOptions' => [
        'class' => 'input-sm form-control',
      ],
    ],
  ]);
  ?>
    <div class="card-body">
      <?= $this->render('/site/_alert_flash', []) ?>
      <div class="row">
        <div class="col-md-12">
          <div class="signup-userinfo">
            <legend><?= Yii::t('app', 'User Information') ?></legend>
            <div class="form-group">
              <div class="col-md-12">
                <?= $form->field($model, 'username')->textInput(['placeholder' => Yii::t('app', 'Username')])->label(true); ?>
              </div>
            </div>
          </div>
          <hr class="isolate-invisible mt-4 mb-3">
          <div class="signup-contact">
            <legend class="pull-left"><?= Yii::t('app', 'Contact Information') ?></legend>
            <div class="form-group">
              <div class="col-sm-12">
                <?= $form->field($model, 'email')->textInput(['placeholder' => Yii::t('app', 'Email'), 'autocomplete' => 'off'])->label(Yii::t('app', 'Email')); ?>
              </div>
            </div>
          </div>
          <hr class="isolate-invisible mt-4 mb-3">
          <div class="signup-security">
            <legend><?= Yii::t('app', 'Security') ?></legend>
            <div class="form-group">
              <div class="col-md-12">
                <?= $form->field($model, 'password')->passwordInput(['placeholder' => Yii::t('app', 'Password'), 'autocomplete' => 'new-password'])->label(true); ?>
              </div>
              <div class="col-md-12">
                <?= $form->field($model, 'repeat_password')->passwordInput(['placeholder' => Yii::t('app', 'Retype Password'), 'autocomplete' => 'off'])->label(true); ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <?= Html::a(Yii::t('app', 'Back'), 'sub-admin-mgmt', ['class' => 'btn btn-default']); ?>
      <?= Html::submitButton('Create', ['class' => 'btn btn-success float-right', 'name' => 'login-button']) ?>
    </div>
  <?php ActiveForm::end(); ?>
</div>
