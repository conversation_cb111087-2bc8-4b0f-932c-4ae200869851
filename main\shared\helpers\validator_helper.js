import { validationResult } from "express-validator";

export const exp_validator = (schemas) => {
    return async (req, res, next) => {
        await Promise.all(schemas.map((schema) => schema.run(req)));

        const result = validationResult(req);
        if (result.isEmpty()) {
            return next();
        }
        // only return the first error
        const errors = result.array();
        return res.status(400).send({
            status: 400,
            error: true,
            // msg: errors[0].msg,
            msg: errors.map(err => err.msg),
            data: {},
        });
    };
};