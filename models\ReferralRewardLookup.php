<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "referral_reward_lookup".
 *
 * @property int $id
 * @property int $invite_tier_count
 * @property string|null $reward_type
 * @property int|null $reward_amount
 * @property string|null $special_tier_title
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property UserReferralReward[] $userReferralRewards
 */
class ReferralRewardLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'referral_reward_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['invite_tier_count', 'created_at'], 'required'],
            [['invite_tier_count', 'reward_amount', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['reward_type', 'special_tier_title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'invite_tier_count' => 'Invite Tier Count',
            'reward_type' => 'Reward Type',
            'reward_amount' => 'Reward Amount',
            'special_tier_title' => 'Special Tier Title',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[UserReferralRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserReferralRewards()
    {
        return $this->hasMany(UserReferralReward::className(), ['referral_reward_id' => 'id']);
    }

    /**
     * Gets all referral titles which are not null and not deleted.
     * 
     * @return array An array of referral titles.
     */
    public static function getAllReferralTierTitleList()
    {
        $query = self::find();
        $query->select('special_tier_title');
        $query->andWhere(['IS NOT', 'special_tier_title', null]);
        $query->andWhere(['is_delete' => 0]);
        $result = $query->all();

        return ArrayHelper::map($result, 'special_tier_title', function ($model) {
            return $model->special_tier_title;
        });
    }
}
