import { TonClient } from "@ton/ton";
import BigNumber from "bignumber.js";
import { getHttpEndpoint } from "@orbs-network/ton-access";
import config from "../../../config.json" assert { type: "json" };

class TonClientHelper {
    /**
     * Gets the chain object
     * @param {String} rpc : string rpc url of the chain
     * @param {String} apikey : string api key of the chain
     */
    constructor(rpc, apikey) {
        this.rpc = rpc;
        this.apikey = apikey;
        this.ton = null;
        this.contracts = {};
    }

    static fromObj(obj) {
        return new this(obj.rpc);
    }

    async wait(mili = 500) {
        await new Promise(resolve => setTimeout(resolve, mili));
    }

    usdtToNano(usdt) {
        const power_six = 1e6;
        return {
            power: power_six,
            nano_num: BigInt(Math.floor(parseFloat(usdt) * power_six)),
        };
    }

    tonToNano(ton) {
        const power_nine = 1e9;
        return {
            power: power_nine,
            nano_num: BigInt(Math.floor(parseFloat(ton) * power_nine)),
        };
    }

    fromNanoUsdt(usdt_nano) {
        const power_six = 1e6;
        return {
            power: power_six,
            nano_num: new BigNumber(usdt_nano).dividedBy(power_six).toString(),
        };
    }

    fromNanoTon(ton_nano) {
        const power_nine = 1e9;
        return {
            power: power_nine,
            nano_num: new BigNumber(ton_nano).dividedBy(power_nine).toString(),
        };
    }

    /**
     * Get the TonClient instance for ton chain
     * @returns {Promise<TonClient>}
     */
    async getTonClient() {
        if (this.ton) return this.ton;
        try {
            if (!this.rpc)
                this.rpc = await getHttpEndpoint({
                    network: config.ton.is_mainnet ? "mainnet" : "testnet",
                });
        } catch (error) {
            console.log(error);
            this.rpc = `${config.ton.rpc}api/v2/jsonRPC`;
        }
        this.ton = new TonClient({
            endpoint: this.rpc,
            apiKey: this.apikey,
        });
        return this.ton;
    }

    /**
     * @param {String} walletAddress
     * @returns {Promise<String>}
     */
    async getTonBalance(_walletAddress) {
        try {
            const tonClient = await this.getTonClient();
            return [
                new BigNumber(await tonClient.getBalance(_walletAddress))
                    .dividedBy(new BigNumber(10).pow(9))
                    .toString(),
                null,
            ];
        } catch (e) {
            console.log(e);
            return [null, e.message];
        }
    }
}

export { TonClientHelper as Chain };
