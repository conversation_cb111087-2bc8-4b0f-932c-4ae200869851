export default function (sequelize, DataTypes) {
    return sequelize.define('user_notice_reward', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        notice_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        is_claim: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'user_notice_reward',
        timestamps: false,
    })
}