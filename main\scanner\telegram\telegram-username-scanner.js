import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";
import { Api } from "grammy";
const api = new Api(common.config.telegram.token);

async function getUserList() {
    let users = await query.models.user.findAll({
        where: { is_username_checked: 0, user_telegram_id: { [query.Op.ne]: null }, is_delete: 0 },
    });

    return users;
}

async function getTeleUserName(user_telegram_id) {
    try {
        const user_profile = await api.getChat(user_telegram_id);
        const username = user_profile.username ?? null;
        const first_last_name = `${user_profile.first_name ?? ""} ${user_profile.last_name ?? ""}`.trim();
        return {
            username: username,
            first_last_name: first_last_name
        };
    } catch (error) {
        console.log(error)
        return null;
    }
}

async function main(retry_delay) {
    while (true) {
        try {
            const user_list = await getUserList();

            for (const element of user_list) {
                const transaction = await query.sequelize.transaction();
                try {
                    console.log("User ID : ", element.user_telegram_id)
                    const user_name = await getTeleUserName(element.user_telegram_id);
                    console.log("User Name getter: ", user_name)
                    if (user_name) {
                        await query.models.user.update({
                            username: user_name.username,
                            first_last_name: user_name.first_last_name,
                            is_username_checked: true,
                            updated_at: common.util_helper.getCurrentEpochTime()
                        }, {
                            where: {
                                id: element.id
                            },
                            transaction: transaction
                        });
                    } else {
                        common.util_helper.spacer(2)
                        console.log("User Name not found, so reset is_username_checked to true")
                        await query.models.user.update({
                            is_username_checked: true,
                            updated_at: common.util_helper.getCurrentEpochTime()
                        }, {
                            where: {
                                id: element.id
                            },
                            transaction: transaction
                        });
                    }
                    common.util_helper.spacer(2)
                    await transaction.commit();
                    await common.util_helper.wait_stopper(1000)
                    console.log(`#@#@#@#@#@#@#@#@#@#@#@#@# FINISHED with User ID : ${element.id}. Continue in 2 seconds @#@#@#@#@#@#@#@#@#@#@#@#@#`)
                    await common.util_helper.wait_stopper(2000)
                } catch (error) {
                    await transaction.rollback();
                    throw new Error(`Error occured in loop with : ${error.message}`);
                }
            }

            common.util_helper.spacer(2)
            if (user_list.length === 0) {
                console.log(`============ No user with username check, Continue new loop in ${retry_delay / 1000} seconds =============`);
            }

            console.log(`============ Finish Loop, new process start in ${retry_delay / 1000} seconds =============`);
            await common.util_helper.wait_stopper(retry_delay)
        } catch (error) {
            console.error(error);
            console.error(`Error occured (${common.moment().format('YYYY-MM-DD HH:mm:ss')}) : `, error);
            console.log(`============ Error, Retrying in ${retry_delay / 1000} seconds =============`);
            await common.util_helper.wait_stopper(retry_delay)
        }
    }
}

main(1000 * 60 * 3);