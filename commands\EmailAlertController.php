<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\helpers\Html;
use app\models\CreditWithdrawalTransaction;
use app\models\AlertEmail;
use app\forms\EmailForm;

class EmailAlertController extends Controller
{
    public function actionWithdrawEmail()
    {
        try {
            $start_time = date('Y-m-d H:i:s');

            echo "Starting Withdraw Email Scan at {$start_time}\n";

            $withdraw_records = CreditWithdrawalTransaction::find()
            ->where([
                'status' => 'manual_approval_required',
                'is_delete' => 0
            ])
            ->andWhere(['in', 'withdrawal_credit_type', ['game_credit', 'platform_token']])
            ->all();

            $count = count($withdraw_records);

            echo "Total Records: {$count}\n";

            if ($count > 0) {
                $admins = AlertEmail::find()
                ->where([
                    'is_active' => 1,
                    'is_delete' => 0
                ])
                ->asArray()
                ->all();

                $credit_record = [];
                $tokenCreditRecords = [];

                foreach ($withdraw_records as $record) {
                    if ($record->withdrawal_credit_type == 'game_credit') {
                        $credit_record[] = $record;
                    }
                    if ($record->withdrawal_credit_type == 'platform_token') {
                        $tokenCreditRecords[] = $record;
                    }
                }

                $contentHtml = '';
                $contentHtml .= Html::tag('p', 'You have pending withdrawals that require manual approval:', ['style' => 'text-align: center;']);
                $contentHtml .= Html::tag(
                    'ul',
                    implode('', [
                        Html::tag('li', 'Credit Withdrawals: ' . Html::encode(count($credit_record))),
                        Html::tag('li', 'Token Withdrawals: ' . Html::encode(count($tokenCreditRecords)))
                    ]),
                    ['style' => 'text-align: left; display: inline-block;']
                );

                $content_params =  [
                    'subject'      => 'Pending Withdrawal Request',
                    'company_name' => Yii::$app->params['company_name'],
                    'title'        => 'Pending Withdrawal Request',
                    'content'      => $contentHtml,
                    'year'         => date('Y'),
                ];



                $content = Yii::$app->controller->renderPartial('@app/mail/general_content', $content_params);

                $emails = [];
                foreach ($admins as $admin) {
                    $emails[] = [
                        'email' => $admin['email_address'],
                        'type'  => 'to'  // Set the recipient type to 'to'
                    ];
                }

                $model = new EmailForm;
                $model->email = $emails;

                $model->subject = 'Withdrawal Request Approval';
                $model->content = $content;

                if (!$model->validate()) {
                throw new \Exception(current($model->getFirstErrors()));
                }

                echo $model->sendToMultipleEmail() . "\n";
            } else {
                echo "No pending withdrawal requests found.\n";
            }

            $end_time = date('Y-m-d H:i:s');

            echo "End of Withdraw Email Scan at {$end_time}\n";
        } catch (\Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }
}