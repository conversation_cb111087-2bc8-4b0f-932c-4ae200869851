<?php

use yii\db\Migration;

class m250826_064058_init_rbac extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->executeMigrations('@yii/rbac/migrations');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250826_064058_init_rbac cannot be reverted.\n";

        return false;
    }

    protected function executeMigrations($migrationPath)
    {
        $oldApp = \Yii::$app;
        $consoleConfig = require \Yii::getAlias('@app/config/console.php');
        $consoleApp = new \yii\console\Application($consoleConfig);
        \Yii::$app->runAction('migrate/up', [
            'migrationPath' => $migrationPath,
            'interactive' => false
        ]);
        \Yii::$app = $oldApp;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250826_064058_init_rbac cannot be reverted.\n";

        return false;
    }
    */
}
