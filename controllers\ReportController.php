<?php

namespace app\controllers;

use Yii;
use yii\helpers\Url;
use yii\filters\AccessControl;
use app\forms\AuditLogForm;
use app\forms\UserLoginLogForm;
use app\forms\WalletTransactionHistoryForm;
use app\forms\ExportJobForm;
use app\forms\UserSongHistoryForm;
use app\forms\UserSocialTaskHistoryForm;
use app\forms\UserInventoryHistoryForm;
use app\forms\StagePurchaseReportForm;

class ReportController extends Controller
{
  public function behaviors()
  {
    return [
      'access' => [
        'class' => AccessControl::className(),
        'rules' => [
          [
            'allow'   => true,
            'roles'   => ['report'],
            'actions' => [
              'wallet-transaction-history',
              'export-list',
              'audit-log',
              'user-login-log',
              'user-song-history',
              'user-social-task-history',
              'user-card-history',
              'user-item-history',
              'stage-purchase-report',
            ],
          ],
        ],
      ],
    ];
  }

  /**
   * Renders the wallet transaction history page
   * 
   * declare model > load data > validate > render walletTransactionHistory
   *
   * @return mixed
   */
  public function actionWalletTransactionHistory()
  {
    $model = new WalletTransactionHistoryForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('walletTransactionHistory', compact('model'));
  }

  /**
   * Renders the export list page
   *
   * declare model > get user id > get ref no > load data > get data > export flag is present 
   * > data not empty & filepath not empty > redirect filepath
   *
   * @param string $ref_no
   * @return mixed
   */
  public function actionExportList($ref_no = null)
  {
    $model          = new ExportJobForm;
    $model->user_id = Yii::$app->user->identity->id;
    $model->ref_no  = $ref_no;
    $model->load(Yii::$app->request->get());

    $data = $model->getExportFile();

    if (!empty(Yii::$app->request->get('export', 0))) {
      if (empty($data)) {
        return $this->redirect(['report/export-list']);
      }

      if (!empty($data['filepath'])) {
        return $this->redirect($data['filepath']);
      }
    }

    return $this->render('exportList', compact('model'));
  }

  /**
   * Renders the audit log page
   * 
   * declare model > load data > validate > render auditLog
   *
   * @return mixed
   */
  public function actionAuditLog()
  {
    $model = new AuditLogForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('auditLog', compact('model'));
  }

  /**
   * Renders the user login log page
   * 
   * declare model > load > validate > get last log date > render userLoginLog
   *
   * @return mixed
   */
  public function actionUserLoginLog()
  {
    $model = new UserLoginLogForm;
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    $last_log = $model->getLastLogDate();

    return $this->render('userLoginLog', compact('model', 'last_log'));
  }

/**
 * Renders the user song history page
 *
 * declare model > load data > validate > render userSongHistory
 *
 * @return mixed
 */
  public function actionUserSongHistory()
  {
    $model = new UserSongHistoryForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('userSongHistory', compact('model'));
  }

/**
 * Renders the user social task history page
 *
 * @return mixed
 */
  public function actionUserSocialTaskHistory()
  {
    $model = new UserSocialTaskHistoryForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('userSocialTaskHistory', compact('model'));
  }

/**
 * Renders the user card history page
 *
 * declare model > load data > validate > render userCardHistory
 *
 * @return mixed
 */
  public function actionUserCardHistory()
  {
    $model = new UserInventoryHistoryForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('userCardHistory', compact('model'));
  }

/**
 * Renders the user item history page
 *
 * declare model > load data > validate > render userItemHistory
 *
 * @return mixed
 */
  public function actionUserItemHistory()
  {
    $model = new UserInventoryHistoryForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    return $this->render('userItemHistory', compact('model'));
  }

  public function actionStagePurchaseReport()
  {
    $model = new StagePurchaseReportForm();
    $model->load(Yii::$app->request->get());

    if (!$model->validate()) {
      throw new \Exception(current($model->getFirstErrors()));
    }

    $summary = $model->getSummary();

    return $this->render('stagePurchaseReport', compact('model', 'summary'));
  }
}