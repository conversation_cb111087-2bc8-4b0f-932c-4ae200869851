<?php

use yii\db\Migration;

class m251008_093620_create_shop_card_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%shop_card_lookup}}', [
            'id' => $this->primaryKey(),
            'rarity_id' => $this->integer()->notNull(),
            'card_type' => $this->string()->notNull(),
            'card_price' => $this->decimal(10, 2)->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`rarity_id`) references `rarity_lookup` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByRarityId` (`rarity_id`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%shop_card_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093620_create_shop_card__lookup cannot be reverted.\n";

        return false;
    }
    */
}
