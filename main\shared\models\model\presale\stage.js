export default function (sequelize, DataTypes) {
    return sequelize.define('stage', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        price_per_token: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        token_available: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        total_fund: {
            type: DataTypes.DECIMAL(36, 18),
            allowNull: false,
        },
        should_end_date: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: "queuing",
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'stage',
        timestamps: false,
    })
}