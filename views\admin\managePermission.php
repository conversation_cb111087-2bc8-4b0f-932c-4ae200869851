<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\widgets\Breadcrumbs;
use yii\bootstrap4\ActiveForm;

$this->title = Yii::t('app', 'Permission Management');

$this->registerCss("
  .checkbox-desc {
    margin-top: -18px;
    margin-bottom: 12px;
  }
");

?>

<h3 class="text-right"><?= "(" . $user->email . ")" ?></h3>

<div class="card">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i> {link} / </i> </li>\n",
    'links' => [
      ['label' => 'Manage Sub Admin', 'url' => ['admin/sub-admin-mgmt']],
      'Permission Management',
    ],
  ]); ?>

  <?php $form = ActiveForm::begin([
    'id'     => 'your-form',
    'method' => 'post',
    'options' => [
      'data-pjax' => true,
    ],
    'fieldConfig' => [
      'inputOptions' => [
        'class' => 'input-sm form-control',
      ],
    ],
  ]);
  ?>
  <div class="card-body">
    <?= $this->render('/site/_alert_flash', []) ?>

    <h3 class="mb-3"><?= Yii::t('app', 'Permission List') ?></h3>
    <?php foreach ($perm as $pe) : ?>
      <?= $form->field($model, "permissions[{$pe['name']}]", [
        'template' => '{input}<div class="form-check-label">{label}</div>',
      ])
        ->label($model->getAttributeLabel($pe['name']))
        ->checkbox([
          'class' => 'form-check-input'
        ], false) ?>

      <div class="checkbox-desc"><?= $pe['desc'] ?></div>
    <?php endforeach; ?>
  </div>
  <div class="card-footer">
    <?= Html::a(Yii::t('app', 'Back'), Url::to('sub-admin-mgmt'), ['class' => 'btn btn-default']); ?>
    <?= Html::submitButton(Yii::t('app', 'Update'), ['class' => 'btn btn-success float-right']) ?>
  </div>
  <?php ActiveForm::end(); ?>
</div>