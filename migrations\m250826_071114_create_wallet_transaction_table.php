<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%wallet_transaction}}`.
 */
class m250826_071114_create_wallet_transaction_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%wallet_transaction}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'wallet_id' => $this->integer(),
            'credit_id' => $this->integer(),
            'tx_id' => $this->string(),
            'type' => "ENUM('IN', 'OUT') NOT NULL",
            'amount' => $this->decimal(36,8)->notNull(),
            'before_balance' => $this->decimal(36,8)->notNull(),
            'after_balance' => $this->decimal(36,8)->notNull(),
            'description' => $this->string(),
            'remark' => $this->string(),
            'category' => $this->string()->notNull(),
            'is_ticket_distributed' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`wallet_id`) references `wallet` (`id`)',
            'foreign key (`credit_id`) references `credit` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByWalletId` (`wallet_id`, `is_delete`)',
            'key `findByCreditId` (`credit_id`, `is_delete`)',
            'key `findByTxId` (`tx_id`)',
            'key `findByUserType` (`user_id`, `type`, `is_delete`)',
            'key `findByTicketDistributed` (`user_id`, `is_ticket_distributed`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%wallet_transaction}}');
    }
}
