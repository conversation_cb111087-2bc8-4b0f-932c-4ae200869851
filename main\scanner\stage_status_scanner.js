/**
* Stage Status Scanner
*
* Monitors presale stage lifecycle transitions within the TON blockchain-based presale system.
* Ensures exactly one stage is live at any given time by:
* - Synchronizing contract active round state with database stage status
* - Monitoring funding goals and automatically transitioning stages when targets are met
* - Enforcing stage deadlines regardless of funding completion
* - Coordinating sequential stage activation and deactivation with atomic transitions
*/

import common from "../shared/imports/common.js";
import query from "../shared/imports/query.js";
import config from "../../config.json" assert { type: "json" };
import { ABIParser } from "./abi_parser.js";
import { Chain as TonClientHelper } from "../shared/helpers/ton_helper.js";
import { initializeAdminWallet } from "../shared/helpers/admin_wallet_helper.js";
import tonPkg from "@ton/ton";
const { internal } = tonPkg;
import { Address, beginCell, toNano } from "@ton/core";

// Initialize TON Helper (singleton)
const TonHelper = new TonClientHelper(
    `${config.ton.rpc}api/v2/jsonRPC`,
    config.ton.apiKey
);

// Configuration
const CONTRACT_ADDRESS = config.contracts.general_presale;
const ABI_FILE_NAME = 'general_presale.json';

const GAS_AMOUNT_FAST = toNano('0.1'); // Fast mode gas fee (0.1 TON)
const CHAIN_DELAY = 2000; // Delay between chained transactions (2 seconds)
const MIN_BALANCE_WARNING = 1.0; // Warn if balance below 1 TON
const MIN_BALANCE_CRITICAL = 0.5; // Halt if balance below 0.5 TON

// Error handling constants
const MAX_CONTRACT_RETRIES = 3; // Maximum retries for contract calls
const RETRY_DELAY_BASE = 2000; // Base delay for exponential backoff (2 seconds)
const RETRY_DELAY_500_ERROR = 10000; // Delay for 500 errors (10 seconds)
const PERSISTENT_ERROR_THRESHOLD = 3; // Consecutive 500 errors before failing

let tonClient = null;
let contract = null;
// Admin wallet instance (initialized at startup)
let adminWallet = null;

// Error tracking for persistent failures
let consecutiveContractErrors = 0;
let lastContractErrorType = null;


/**
 * Check if error is retryable
 * @param {Error} error - Error object
 * @returns {boolean} True if error is retryable
 */
function isRetryableError(error) {
    const errorMessage = error.message?.toLowerCase() || '';

    // Retryable conditions
    return errorMessage.includes('timeout') ||
           errorMessage.includes('econnrefused') ||
           errorMessage.includes('econnreset') ||
           errorMessage.includes('network') ||
           errorMessage.includes('429') ||
           errorMessage.includes('rate limit');
}

/**
 * Check if error is a server error (500)
 * @param {Error} error - Error object
 * @returns {boolean} True if 500 error
 */
function is500Error(error) {
    const errorMessage = error.message?.toLowerCase() || '';
    return errorMessage.includes('500') || errorMessage.includes('internal server error');
}

/**
 * Safe contract call with retry logic
 * @param {Function} contractMethod - Contract method to call
 * @param {string} methodName - Method name for logging
 * @param {Array} args - Method arguments
 * @returns {Promise<any>} Method result or null on failure
 */
async function safeContractCall(contractMethod, methodName, args = []) {
    let retries = 0;

    while (retries < MAX_CONTRACT_RETRIES) {
        try {
            const result = await contractMethod(...args);

            // Reset error tracking on success
            if (lastContractErrorType === methodName) {
                consecutiveContractErrors = 0;
                lastContractErrorType = null;
            }

            return result;
        } catch (error) {
            // Check if error is due to null/empty response (graceful handling)
            const errorMessage = error.message?.toLowerCase() || '';
            const isNullResponse = errorMessage.includes('eof') ||
                                   errorMessage.includes('not a number') ||
                                   errorMessage.includes('stack: []') ||
                                   errorMessage.includes('failed to read optional marker');

            // For null responses, return null without logging as error
            if (isNullResponse) {
                console.log(`ℹ️  ${methodName} returned null/empty (no data available)`);
                // Reset error tracking for null responses (not an error)
                if (lastContractErrorType === methodName) {
                    consecutiveContractErrors = 0;
                    lastContractErrorType = null;
                }
                return null;
            }

            const is500 = is500Error(error);
            const isRetryable = isRetryableError(error);

            console.error(`❌ Contract call failed [${methodName}]: ${error.message}`);

            // Track consecutive errors
            if (is500) {
                if (lastContractErrorType === methodName) {
                    consecutiveContractErrors++;
                } else {
                    consecutiveContractErrors = 1;
                    lastContractErrorType = methodName;
                }

                // Fail immediately if persistent 500 errors
                if (consecutiveContractErrors >= PERSISTENT_ERROR_THRESHOLD) {
                    console.error(`❌ Persistent 500 errors detected for ${methodName} (${consecutiveContractErrors} consecutive) - failing`);
                    throw new Error(`Persistent contract error: ${methodName} returned 500 ${consecutiveContractErrors} times`);
                }
            }

            // Retry logic
            if (is500 || isRetryable) {
                retries++;
                if (retries < MAX_CONTRACT_RETRIES) {
                    // Use 10 second delay for 500 errors, exponential backoff for others
                    const delay = is500 ? RETRY_DELAY_500_ERROR : RETRY_DELAY_BASE * Math.pow(2, retries - 1);
                    console.log(`Retrying ${methodName} (${retries}/${MAX_CONTRACT_RETRIES}) in ${delay}ms...`);
                    await common.util_helper.wait_stopper(delay);
                    continue;
                }
            }

            // Non-retryable or max retries reached
            console.error(`❌ ${methodName} failed after ${retries} retries`);
            return null;
        }
    }

    return null;
}

/**
 * Get current active stage from database
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<Object|null>} Active stage or null
 */
async function getCurrentActiveStage(transaction) {
    const liveStages = await query.models.stage.findAll({
        where: {
            status: 'live',
            is_delete: false
        },
        order: [['id', 'ASC']],
        lock: transaction.LOCK.UPDATE,
        transaction
    });

    if (liveStages.length === 0) {
        return null;
    }

    if (liveStages.length > 1) {
        console.warn(`WARNING: Multiple live stages detected [${liveStages.map(s => s.id).join(', ')}] - auto-repairing`);

        // Deactivate all except the first one
        for (let i = 1; i < liveStages.length; i++) {
            await liveStages[i].update({
                status: 'queuing',
                updated_at: common.util_helper.getCurrentEpochTime()
            }, { transaction });

            console.log(`Deactivated stage ${liveStages[i].id} (constraint violation repair)`);
        }

        return liveStages[0];
    }

    return liveStages[0];
}

/**
 * Activate first queuing stage
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<Object|null>} Activated stage or null
 */
async function activateFirstStage(transaction) {
    const firstStage = await query.models.stage.findOne({
        where: {
            status: 'queuing',
            is_delete: false
        },
        order: [['id', 'ASC']],
        lock: transaction.LOCK.UPDATE,
        transaction
    });

    if (!firstStage) {
        console.log('No queuing stages available to activate');
        return null;
    }

    await firstStage.update({
        status: 'live',
        updated_at: common.util_helper.getCurrentEpochTime()
    }, { transaction });

    console.log(`✅ Activated first stage: ${firstStage.id} "${firstStage.name}"`);

    return firstStage;
}

/**
 * Synchronize contract state with database
 * @param {Object} activeStage - Current active stage from DB (or null)
 * @param {Object} contract - Contract caller instance
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<boolean>} True if sync successful, false otherwise
 */
async function syncContractState(activeStage, contract, transaction) {
    try {
        const maxRoundId = await safeContractCall(
            contract.getMaxRoundId.bind(contract),
            'getMaxRoundId'
        );

        if (maxRoundId === null) {
            console.warn('⚠️  Failed to fetch maxRoundId from contract - skipping sync');
            return false;
        }

        const latestRound = await safeContractCall(
            contract.getLatestActiveRound.bind(contract),
            'getLatestActiveRound'
        );

        if (latestRound == null && maxRoundId > 0n) {
            console.log('ℹ️  No active round on contract (maxRoundId exists but no active round)');
            // This is a valid state - contract has rounds but none are active
        }

        let activationRoundId = 0;
        let activationRoundState = false;

        if (maxRoundId === 0n && (!latestRound || latestRound === null)) { // No rounds created yet
            console.log('No rounds on contract yet - activating first stage...');
            activationRoundId = activeStage.id;
            activationRoundState = true;
        } else if (maxRoundId > 0n && (!latestRound || latestRound === null)) { // Rounds exist but none active
            console.log('Rounds exist but none active - activating first stage...');
            activationRoundId = activeStage.id;
            activationRoundState = true;
        } else if (latestRound && Number(latestRound.roundId) !== activeStage.id) { // Contract has different active round than DB
            console.log(`Contract round ${latestRound.roundId} does not match DB stage ${activeStage.id} - synchronizing`);
            activationRoundId = activeStage.id;
            activationRoundState = true;
        } else if (latestRound && Number(latestRound.roundId) === activeStage.id && !latestRound.isActive) { // Contract round is inactive
            console.log(`Contract round ${latestRound.roundId} is inactive - activating`);
            // possible is admin temporarily deactivated it, log warning message without auto-activating
            console.warn(`⚠️  Contract round ${latestRound.roundId} is inactive - manual activation required`);
        } else {
            // Contract and DB are in sync
            console.log(`✅ Contract round ${latestRound.roundId} synchronized with DB stage ${activeStage.id}`);
        }

        if (activationRoundId > 0) {
            const txHash = await sendContractTransaction('SetActiveRound', {
                roundId: activationRoundId,
                active: activationRoundState
            }, transaction);

            if (!txHash) {
                console.error('❌ Failed to send SetActiveRound transaction - sync incomplete');
                return false;
            }

            await common.util_helper.wait_stopper(CHAIN_DELAY);
            return true;
        }

        return true;
    } catch (error) {
        console.error('❌ Error synchronizing contract state:', error.message);
        return false;
    }
}

/**
 * Transition to next stage
 * @param {Object} currentStage - Current stage to end
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<boolean>} True if transition successful, false otherwise
 */
async function transitionToNextStage(currentStage, transaction) {
    try {
        // Mark current stage as fulfilled
        await currentStage.update({
            status: 'fulfilled',
            updated_at: common.util_helper.getCurrentEpochTime()
        }, { transaction });

        const totalFunding = await getStageFunding(currentStage.id, transaction);
        console.log(`✅ Stage ${currentStage.id} ended - Status: fulfilled (funding: ${totalFunding}/${currentStage.total_fund})`);

        // Find next queuing stage
        const nextStage = await query.models.stage.findOne({
            where: {
                id: { [query.Op.gt]: currentStage.id },
                status: 'queuing',
                is_delete: false
            },
            order: [['id', 'ASC']],
            lock: transaction.LOCK.UPDATE,
            transaction
        });

        if (nextStage) {
            // Activate next stage
            await nextStage.update({
                status: 'live',
                updated_at: common.util_helper.getCurrentEpochTime()
            }, { transaction });

            console.log(`Transitioning: ${currentStage.name} → ${nextStage.name}`);

            const syncSuccess = await syncContractState(nextStage, contract, transaction);
            if (!syncSuccess) {
                console.error('❌ Failed to sync contract state for next stage');
                return false;
            }

            console.log(`✅ Stage transition complete: ${nextStage.name} now live`);
            return true;
        } else {
            console.log('No next stage available - presale complete');
        }

        return true;
    } catch (error) {
        console.error('❌ Error transitioning to next stage:', error.message);
        return false;
    }
}

/**
 * Send contract transaction with fast gas fee and error handling
 * @param {string} methodName - Contract method name
 * @param {Object} params - Method parameters
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<string|null>} Transaction hash or null on failure
 */
async function sendContractTransaction(methodName, params, transaction) {
    let retries = 0;

    while (retries < MAX_CONTRACT_RETRIES) {
        try {
            // Build message body based on method
            let messageBody;
            let expectedOpcode = 0n;

            if (methodName === 'SetActiveRound') {
                expectedOpcode = BigInt(1359463884);
                messageBody = beginCell()
                    .storeUint(expectedOpcode, 32) // Method header
                    .storeUint(BigInt(params.roundId), 64) // roundId as BigInt
                    .storeBit(params.active ? 1n : 0n) // active (boolean as bit)
                    .endCell();
            } else {
                throw new Error(`Unsupported method: ${methodName}`);
            }

            // Create internal message
            const internalMessage = internal({
                to: Address.parse(CONTRACT_ADDRESS),
                value: GAS_AMOUNT_FAST,
                bounce: true,
                body: messageBody
            });

            // Open wallet contract with provider
            const walletContract = tonClient.open(adminWallet.contract);

            // Get current seqno
            const seqno = await walletContract.getSeqno();

            // Create transfer
            const transfer = adminWallet.contract.createTransfer({
                seqno,
                secretKey: adminWallet.keyPair.secretKey,
                messages: [internalMessage]
            });

            // Send transaction
            await tonClient.sendExternalMessage(adminWallet.contract, transfer);

            // Wait for wallet tx to appear (initial poll)
            await common.util_helper.wait_stopper(3000);

            const transactions = await tonClient.getTransactions(adminWallet.contract.address, {
                limit: 5
            });

            let walletTxHash = null;
            for (const tx of transactions) {
                if (tx.inMessage?.info?.type === 'external-in' &&
                    tx.description?.type === 'generic' &&
                    tx.now > Math.floor(Date.now() / 1000) - 30) { // Shorter recent window

                    walletTxHash = tx.hash().toString('hex');
                    console.log(`Wallet tx sent: ${walletTxHash} (Gas: 0.1 TON)`);
                    break;
                }
            }

            if (!walletTxHash) {
                console.warn(`⚠️ Wallet tx hash not found after wait - tx may still process`);
                walletTxHash = 'unknown'; // Placeholder for logging
            }

            // Wait for contract confirmation (simplified polling)
            let contractTxHash = null;
            let contractRetries = 0;
            const maxContractRetries = 20;

            while (contractRetries < maxContractRetries && !contractTxHash) {
                const contractTxs = await tonClient.getTransactions(
                    Address.parse(CONTRACT_ADDRESS),
                    { limit: 5 }
                );

                for (const tx of contractTxs) {
                    if (tx.inMessage?.info?.src?.equals(adminWallet.address) &&
                        tx.inMessage.body?.beginParse()?.loadUint(32) === expectedOpcode &&
                        tx.now > Math.floor(Date.now() / 1000) - 60) { // 1 min window

                        const body = tx.inMessage.body;
                        if (body) {
                            const parser = body.beginParse();
                            parser.loadUint(32); // Skip op code
                            const txRoundId = parser.loadUint(64);
                            const txActive = parser.loadBit() === 1n;

                            if (txRoundId === BigInt(params.roundId) && txActive === params.active) {
                                contractTxHash = tx.hash().toString('hex');
                                console.log(`✅ Contract tx confirmed: ${contractTxHash} (from wallet: ${walletTxHash})`);

                                // Reset error tracking on success
                                consecutiveContractErrors = 0;
                                lastContractErrorType = null;

                                break;
                            }
                        }
                    }
                }

                if (!contractTxHash) {
                    await common.util_helper.wait_stopper(3000); // Shorter poll interval
                    contractRetries++;
                }
            }

            if (!contractTxHash) {
                console.error(`❌ Contract confirmation failed for ${methodName} (roundId: ${params.roundId}, active: ${params.active}) - Check wallet tx: ${walletTxHash}`);
                return walletTxHash;
            }

            return contractTxHash;
        } catch (error) {
            const is500 = is500Error(error);
            const isRetryable = isRetryableError(error);

            console.error(`❌ Error sending transaction for ${methodName}:`, error.message);

            // Track consecutive errors
            if (is500) {
                consecutiveContractErrors++;
                lastContractErrorType = methodName;

                // Fail immediately if persistent 500 errors
                if (consecutiveContractErrors >= PERSISTENT_ERROR_THRESHOLD) {
                    console.error(`❌ Persistent 500 errors for transaction ${methodName} - aborting`);
                    return null;
                }
            }

            // Retry logic
            if (is500 || isRetryable) {
                retries++;
                if (retries < MAX_CONTRACT_RETRIES) {
                    // Use 10 second delay for 500 errors, exponential backoff for others
                    const delay = is500 ? RETRY_DELAY_500_ERROR : RETRY_DELAY_BASE * Math.pow(2, retries - 1);
                    console.log(`Retrying transaction ${methodName} (${retries}/${MAX_CONTRACT_RETRIES}) in ${delay}ms...`);
                    await common.util_helper.wait_stopper(delay);
                    continue;
                }
            }

            // Non-retryable or max retries reached
            console.error(`❌ Transaction ${methodName} failed after ${retries} retries`);
            return null;
        }
    }

    return null;
}

/**
 * Get total funding for a stage from purchases
 * @param {number} stageId - Stage ID
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<string>} Total USD amount
 */
async function getStageFunding(stageId, transaction) {
    const result = await query.models.stage_purchase.findOne({
        attributes: [
            [query.sequelize.fn('SUM', query.sequelize.col('usd_amount')), 'total_usd']
        ],
        where: {
            stage_id: stageId,
            is_delete: false
        },
        transaction,
        raw: true
    });

    return result?.total_usd || '0';
}

/**
 * Check if stage funding goal is reached
 * @param {Object} stage - Stage object
 * @param {Object} transaction - Sequelize transaction
 * @returns {Promise<boolean>} True if funding goal reached
 */
async function isFundingGoalReached(stage, transaction) {
    const totalFunding = await getStageFunding(stage.id, transaction);
    const totalFundingBN = new common.BigNumber(totalFunding);
    const goalBN = new common.BigNumber(stage.total_fund.toString());

    return totalFundingBN.isEqualTo(goalBN);
}

/**
 * Check if stage deadline has passed
 * @param {Object} stage - Stage object
 * @returns {boolean} True if deadline passed
 */
function isDeadlineReached(stage) {
    const currentTime = common.util_helper.getCurrentEpochTime();
    return currentTime >= stage.should_end_date;
}

// Main scanner function
async function stageStatusScanner(delayTime, retryTime) {
    tonClient = await TonHelper.getTonClient();
    const abiParser = new ABIParser(ABI_FILE_NAME);
    contract = abiParser.createCaller(tonClient, CONTRACT_ADDRESS);

    // Initialize admin wallet
    adminWallet = await initializeAdminWallet(tonClient);

    const nonBounceableAddress = adminWallet.addressNonBounceable;
    const [activeBalance, balanceError] = await TonHelper.getTonBalance(nonBounceableAddress);

    if (balanceError) {
        console.warn(`WARNING: Could not fetch balance: ${balanceError}`);
    } else {
        console.log(`Non-bounceable address balance: ${activeBalance} TON`);
    }

    // Check balance meets minimum requirements
    const balanceNum = parseFloat(activeBalance || '0');
    if (balanceNum < MIN_BALANCE_CRITICAL) {
        throw new Error(`CRITICAL: Admin wallet balance (${activeBalance} TON) below minimum threshold (${MIN_BALANCE_CRITICAL} TON)`);
    }

    if (balanceNum < MIN_BALANCE_WARNING) {
        console.warn(`WARNING: Admin wallet balance (${activeBalance} TON) below recommended threshold (${MIN_BALANCE_WARNING} TON)`);
    }

    // Verify admin permissions on contract
    const isAdmin = await safeContractCall(
        contract.isAdmin.bind(contract),
        'isAdmin',
        [Address.parse(nonBounceableAddress)]
    );

    if (isAdmin === null) {
        throw new Error(`Failed to verify admin permissions for ${nonBounceableAddress} on contract ${CONTRACT_ADDRESS}`);
    }

    if (!isAdmin) {
        throw new Error(`Admin ${nonBounceableAddress} does not have permissions on contract ${CONTRACT_ADDRESS}`);
    } else {
        console.log(`✅ Admin ${nonBounceableAddress} has permissions on contract ${CONTRACT_ADDRESS}`);
    }

    console.log(`Contract: ${CONTRACT_ADDRESS}`);
    console.log(`Polling Interval: ${delayTime}ms`);
    console.log(`Gas Mode: Fast (0.1 TON per transaction)`);
    common.util_helper.spacer(1);

    let iterationCount = 0;

    while (true) {
        const transaction = await query.sequelize.transaction();
        try {
            iterationCount++;

            // Check wallet balance periodically (every 100 iterations)
            if (iterationCount % 100 === 0) {
                const [balance] = await TonHelper.getTonBalance(adminWallet.activeAddress);
                if (balance && parseFloat(balance) < MIN_BALANCE_WARNING) {
                    console.warn(`WARNING: Admin wallet balance low: ${balance} TON (threshold: ${MIN_BALANCE_WARNING} TON)`);
                }
                if (balance && parseFloat(balance) < MIN_BALANCE_CRITICAL) {
                    throw new Error(`CRITICAL: Admin wallet balance (${balance} TON) below minimum threshold`);
                }
            }

            // Get current active stage
            let activeStage = await getCurrentActiveStage(transaction);

            // If no active stage, try to activate first one
            if (!activeStage) {
                console.log('No live stage found - attempting to activate first stage');
                activeStage = await activateFirstStage(transaction);
            }

            // call contract to sync state
            const syncSuccess = await syncContractState(activeStage, contract, transaction);

            if (!syncSuccess) {
                console.warn('⚠️  Contract sync failed - will retry next iteration');
                await transaction.rollback();
                console.log(`Retrying in ${delayTime / 1000}s...`);
                common.util_helper.spacer(1);
                await common.util_helper.wait_stopper(delayTime);
                continue;
            }

            // Monitor active stage (if exists)
            if (activeStage) {
                console.log(`Monitoring ${activeStage.name}...`);

                const totalFunding = await getStageFunding(activeStage.id, transaction);
                const fundingGoalReached = await isFundingGoalReached(activeStage, transaction);
                const deadlineReached = isDeadlineReached(activeStage);

                const fundingProgress = new common.BigNumber(totalFunding)
                    .dividedBy(new common.BigNumber(activeStage.total_fund.toString()))
                    .multipliedBy(100)
                    .toFixed(2);

                const timeRemaining = activeStage.should_end_date - common.util_helper.getCurrentEpochTime();
                const timeRemainingHours = Math.max(0, Math.floor(timeRemaining / 3600));

                console.log(`   Funding: ${totalFunding}/${(activeStage.total_fund * 1)} USD (${fundingProgress}%)`);
                console.log(`   Time remaining: ${timeRemainingHours}h`);

                // Check if stage should end
                if (fundingGoalReached) {
                    console.log(`${activeStage.name} funding goal reached!`);
                    const transitionSuccess = await transitionToNextStage(activeStage, transaction);
                    if (!transitionSuccess) {
                        console.error('❌ Stage transition failed - rolling back');
                        await transaction.rollback();
                        console.log(`Retrying in ${retryTime / 1000}s...`);
                        common.util_helper.spacer(1);
                        await common.util_helper.wait_stopper(retryTime);
                        continue;
                    }
                } else if (deadlineReached) {
                    console.log(`${activeStage.name} deadline reached!`);
                    const transitionSuccess = await transitionToNextStage(activeStage, transaction);
                    if (!transitionSuccess) {
                        console.error('❌ Stage transition failed - rolling back');
                        await transaction.rollback();
                        console.log(`Retrying in ${retryTime / 1000}s...`);
                        common.util_helper.spacer(1);
                        await common.util_helper.wait_stopper(retryTime);
                        continue;
                    }
                } else {
                    console.log(`✅ ${activeStage.name} active - no action needed`);
                }
            } else {
                await transaction.commit();
                console.log('✅ No active stage - system idle');
                common.util_helper.spacer(1);
                await common.util_helper.wait_stopper(delayTime);
                continue;
            }

            await transaction.commit();
            console.log(`============ Finished, next scan in ${delayTime / 1000}s =============`);
            common.util_helper.spacer(2);

            await common.util_helper.wait_stopper(delayTime);
        } catch (error) {
            if (!transaction.finished) {
                await transaction.rollback();
            }

            // Check if error is due to contract 500 error
            const is500 = is500Error(error);
            const retryDelay = is500 ? RETRY_DELAY_500_ERROR : retryTime;

            console.error('❌ Scanner error:', error.message);
            console.error(error);
            console.log(`============ Error, retrying in ${retryDelay / 1000}s =============`);
            common.util_helper.spacer(2);
            await common.util_helper.wait_stopper(retryDelay);
        }
    }
}



// Start scanner
const delayTime = 6000; // 6 seconds
const retryTime = 4000; // 4 seconds

stageStatusScanner(delayTime, retryTime);
