<?php

use yii\db\Migration;

class m251021_070723_add_column_into_wallet extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('wallet', 'type', $this->string()->defaultValue('token')->after('wallet_address'));
        $this->addColumn('wallet', 'balance', $this->decimal(36, 18)->defaultValue(0)->after('type'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('wallet', 'balance');
        $this->dropColumn('wallet', 'type');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251021_070723_add_column_into_wallet cannot be reverted.\n";

        return false;
    }
    */
}
