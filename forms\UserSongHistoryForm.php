<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\User;
use app\models\UserSongSelectedLookup;

class UserSongHistoryForm extends Model
{
  public $username;
  public $user_telegram_id;
  public $first_last_name;
  public $wallet_address;
  public $song_name;
  public $genre_name;
  public $genre_type;
  public $tap_count;
  public $is_active;
  public $date_range;
  public $date_start;
  public $date_end;

  public function rules()
  {
    return [
      [[
        'username', 'user_telegram_id', 'first_last_name', 'wallet_address', 'song_name', 'genre_name', 'genre_type', 
        'tap_count', 'is_active', 'date_range', 'date_start', 'date_end'
      ], 'safe'],
      [['username', 'user_telegram_id', 'wallet_address', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Get the query for data provider
   *
   * Select * from user_song_selected_lookup as ussl
   * left join song s on s.id = ussl.song_id
   * left join genre g on g.id = ussl.genre_id
   * left join user u on u.id = ussl.user_id
   * left join boost_instrument_shop_card_one bishc1 on bishc1.id = ussl.boost_instrument_shop_card_one_id
   * left join boost_instrument_shop_card_two bishc2 on bishc2.id = ussl.boost_instrument_shop_card_two_id
   * left join boost_instrument_shop_card_three bishc03 on bishc03.id = ussl.boost_instrument_shop_card_three_id
   * left join boost_instrument_shop_card_four bishc4 on bishc4.id = ussl.boost_instrument_shop_card_four_id
   * left join exp_booster_user_shop_item ebushi on ebushi.id = ussl.exp_booster_user_shop_item_id
   * left join token_booster_user_shop_item tbushi on tbushi.id = ussl.token_booster_user_shop_item_id
   * left join tap_booster_user_shop_item tbushi on tbushi.id = ussl.tap_booster_user_shop_item_id
   * where u.id = :user_id
   * and s.song_id = :song_id
   * and g.genre_id = :genre_id
   * and created_at between :date_start and :date_end
   * and ussl.is_delete = 0
   * 
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = UserSongSelectedLookup::find()
    ->alias('ussl')
    ->joinWith('song s')
    ->joinWith('genre g')
    ->joinWith('user u')
    ->joinWith(['boostInstrumentShopCardOne bishc1' => function ($q) {
        $q->joinWith(['rarity r1', 'instrument in1']);
    }])
    ->joinWith(['boostInstrumentShopCardTwo bishc2' => function ($q) {
        $q->joinWith(['rarity r2', 'instrument in2']);
    }])
    ->joinWith(['boostInstrumentShopCardThree bishc3' => function ($q) {
        $q->joinWith(['rarity r3', 'instrument in3']);
    }])
    ->joinWith(['boostInstrumentShopCardFour bishc4' => function ($q) {
        $q->joinWith(['rarity r4', 'instrument in4']);
    }])
    ->joinWith(['expBoosterUserShopItem ebushi' => function ($q) {
        $q->joinWith(['item i1']);
    }])
    ->joinWith(['tokenBoosterUserShopItem tobushi' => function ($q) {
        $q->joinWith(['item i2']);
    }])
    ->joinWith(['tapBoosterUserShopItem tabushi' => function ($q) {
        $q->joinWith(['item i3']);
    }]);

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->user_telegram_id)) {
      $query->andFilterWhere(['u.user_telegram_id' => $this->user_telegram_id]);
    }

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }

    if (!empty($this->song_name)) {
      $query->andFilterWhere(['like','s.song_name',"%".$this->song_name."%", false]);
    }

    if (!empty($this->genre_name)) {
      $query->andWhere(['g.genre_name' => $this->genre_name]);
    }

    if (!empty($this->genre_type)) {
      $query->andWhere(['g.genre_type' => $this->genre_type]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'ussl.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['ussl.is_delete' => 0]);

    return $query;
  }


/**
 * Gets the data provider for the user song history.
 *
 * @return ActiveDataProvider The data provider for the user song history.
 */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

}
