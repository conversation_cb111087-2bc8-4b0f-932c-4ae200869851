import authController from "../../apis/controllers/auth.js"
import userController from "../../apis/controllers/user.js"
import taskController from "../../apis/controllers/task.js"
import profileController from "../../apis/controllers/profile.js"
import noticeController from "../../apis/controllers/notice.js"
import referralController from "../../apis/controllers/referral.js"
import platformController from "../../apis/controllers/platform.js"
import cacheController from "../../apis/controllers/cache.js"
import presaleController from "../../apis/controllers/presale.js"

//v1
import tapController from "../../apis/controllers/v1/controllers/tap.js"
import shopController from "../../apis/controllers/v1/controllers/shop.js"
import leaderboardController from "../../apis/controllers/v1/controllers/leaderboard.js"
import soundkeepController from "../../apis/controllers/v1/controllers/soundkeep.js"
import songController from "../../apis/controllers/v1/controllers/song.js"

export default {
    authController,
    userController,
    taskController,
    profileController,
    noticeController,
    referralController,
    platformController,
    cacheController,
    presaleController,

    //v1
    tapController,
    shopController,
    leaderboardController,
    soundkeepController,
    songController,
};