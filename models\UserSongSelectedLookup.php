<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_song_selected_lookup".
 *
 * @property int $id
 * @property int $user_id
 * @property int $song_id
 * @property int $genre_id
 * @property int $tap_count
 * @property int|null $boost_instrument_shop_card_one_id
 * @property int|null $boost_instrument_shop_card_two_id
 * @property int|null $boost_instrument_shop_card_three_id
 * @property int|null $boost_instrument_shop_card_four_id
 * @property int|null $exp_booster_user_shop_item_id
 * @property int|null $token_booster_user_shop_item_id
 * @property int|null $tap_booster_user_shop_item_id
 * @property int $is_active
 * @property int $is_used
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property UserShopItem $tapBoosterUserShopItem
 * @property SongLookup $song
 * @property AdvanceGenreLookup $genre
 * @property UserShopCard $boostInstrumentShopCardOne
 * @property UserShopCard $boostInstrumentShopCardTwo
 * @property UserShopCard $boostInstrumentShopCardThree
 * @property UserShopCard $boostInstrumentShopCardFour
 * @property UserShopItem $expBoosterUserShopItem
 * @property UserShopItem $tokenBoosterUserShopItem
 */
class UserSongSelectedLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_song_selected_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'song_id', 'genre_id', 'created_at'], 'required'],
            [['user_id', 'song_id', 'genre_id', 'tap_count', 'boost_instrument_shop_card_one_id', 'boost_instrument_shop_card_two_id', 'boost_instrument_shop_card_three_id', 'boost_instrument_shop_card_four_id', 'exp_booster_user_shop_item_id', 'token_booster_user_shop_item_id', 'tap_booster_user_shop_item_id', 'is_active', 'is_used', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['user_id' => 'id']],
            [['tap_booster_user_shop_item_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopItem::className(), 'targetAttribute' => ['tap_booster_user_shop_item_id' => 'id']],
            [['song_id'], 'exist', 'skipOnError' => true, 'targetClass' => SongLookup::className(), 'targetAttribute' => ['song_id' => 'id']],
            [['genre_id'], 'exist', 'skipOnError' => true, 'targetClass' => AdvanceGenreLookup::className(), 'targetAttribute' => ['genre_id' => 'id']],
            [['boost_instrument_shop_card_one_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopCard::className(), 'targetAttribute' => ['boost_instrument_shop_card_one_id' => 'id']],
            [['boost_instrument_shop_card_two_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopCard::className(), 'targetAttribute' => ['boost_instrument_shop_card_two_id' => 'id']],
            [['boost_instrument_shop_card_three_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopCard::className(), 'targetAttribute' => ['boost_instrument_shop_card_three_id' => 'id']],
            [['boost_instrument_shop_card_four_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopCard::className(), 'targetAttribute' => ['boost_instrument_shop_card_four_id' => 'id']],
            [['exp_booster_user_shop_item_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopItem::className(), 'targetAttribute' => ['exp_booster_user_shop_item_id' => 'id']],
            [['token_booster_user_shop_item_id'], 'exist', 'skipOnError' => true, 'targetClass' => UserShopItem::className(), 'targetAttribute' => ['token_booster_user_shop_item_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'song_id' => 'Song ID',
            'genre_id' => 'Genre ID',
            'tap_count' => 'Tap Count',
            'boost_instrument_shop_card_one_id' => 'Boost Instrument Shop Card One ID',
            'boost_instrument_shop_card_two_id' => 'Boost Instrument Shop Card Two ID',
            'boost_instrument_shop_card_three_id' => 'Boost Instrument Shop Card Three ID',
            'boost_instrument_shop_card_four_id' => 'Boost Instrument Shop Card Four ID',
            'exp_booster_user_shop_item_id' => 'Exp Booster User Shop Item ID',
            'token_booster_user_shop_item_id' => 'Token Booster User Shop Item ID',
            'tap_booster_user_shop_item_id' => 'Tap Booster User Shop Item ID',
            'is_active' => 'Is Active',
            'is_used' => 'Is Used',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }

    /**
     * Gets query for [[TapBoosterUserShopItem]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTapBoosterUserShopItem()
    {
        return $this->hasOne(UserShopItem::className(), ['id' => 'tap_booster_user_shop_item_id']);
    }

    /**
     * Gets query for [[Song]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSong()
    {
        return $this->hasOne(SongLookup::className(), ['id' => 'song_id']);
    }

    /**
     * Gets query for [[Genre]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getGenre()
    {
        return $this->hasOne(AdvanceGenreLookup::className(), ['id' => 'genre_id']);
    }

    /**
     * Gets query for [[BoostInstrumentShopCardOne]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentShopCardOne()
    {
        return $this->hasOne(UserShopCard::className(), ['id' => 'boost_instrument_shop_card_one_id']);
    }

    /**
     * Gets query for [[BoostInstrumentShopCardTwo]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentShopCardTwo()
    {
        return $this->hasOne(UserShopCard::className(), ['id' => 'boost_instrument_shop_card_two_id']);
    }

    /**
     * Gets query for [[BoostInstrumentShopCardThree]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentShopCardThree()
    {
        return $this->hasOne(UserShopCard::className(), ['id' => 'boost_instrument_shop_card_three_id']);
    }

    /**
     * Gets query for [[BoostInstrumentShopCardFour]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoostInstrumentShopCardFour()
    {
        return $this->hasOne(UserShopCard::className(), ['id' => 'boost_instrument_shop_card_four_id']);
    }

    /**
     * Gets query for [[ExpBoosterUserShopItem]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getExpBoosterUserShopItem()
    {
        return $this->hasOne(UserShopItem::className(), ['id' => 'exp_booster_user_shop_item_id']);
    }

    /**
     * Gets query for [[TokenBoosterUserShopItem]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTokenBoosterUserShopItem()
    {
        return $this->hasOne(UserShopItem::className(), ['id' => 'token_booster_user_shop_item_id']);
    }
}
