<?php

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Referral Management');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_referral', [
      'model'   => $model,
      'page'    => 'referral-mgmt',
      'pjax_id' => "#pjax-referral-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','Username'),
          'format' => 'raw',
          'value'  => function ($model) {
            $user = $model->user;

            $_name = empty($user->username) ? $user->first_last_name : $user->username;
            $display = "";

            $display .= "Username : ".$_name;

            return $display;
          },
        ],
        [
          'label'  => Yii::t('app','Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->user->wallet_address)) {
              return '-';
            }

            $link = Yii::$app->formatHelper->formatTon('address', $model->user->wallet_address, 5);

            return $link;
          },
        ],
        [
          'label' => Yii::t('app','Referral Code'),
          'value' => function ($model) {
            return $model->referral_code;
          },
        ],
        [
          'label'  => Yii::t('app','Referral Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->referral_user_id) || empty($model->referralUser->wallet_address)) {
              return '-';
            }

            $link = Yii::$app->formatHelper->formatTon('address', $model->referralUser->wallet_address, 5);

            return $link;
          },
        ],
        [
          'label'  => Yii::t('app','Total Referral'),
          'format' => 'raw',
          'value'  => function ($model) {
            $info = $model->getReferralInfo();

            return 'Total Invited : ' . $info['total'] . '<br> Today Invited : ' . $info['today'];
          },
        ],
        [
          'label'  => Yii::t('app','Login Info'),
          'format' => 'raw',
          'value'  => function ($model) {
            return 'Accumulated Login : ' . $model->user->accumulate_login . '<br> Continuous Login : ' . $model->user->continuous_login;
          },
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
        [
          'class'    => 'yii\grid\ActionColumn',
          'template' => '
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                Action <span class="dropdown-icon"></span>
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu" role="menu">
                {view}
              </div>
            </div>
          ',
          'buttons'  => [
            'view' => function ($url, $model) {
              return Html::a(Yii::t('app','View'), ['view-referral', 'user_id' => $model->id], [
                'class' => 'dropdown-item',
              ]);
            },
          ],
        ]
      ]
    ]); ?>
  </div>
</div>
