<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Tier List Management');

?>

<div class="card">
    <div class="card-header">
        <?= $this->render('/site/_alert_flash', []) ?>
    </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','Tiers'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->special_tier_title)) {
              return '-';
            }

            return $model->special_tier_title;
          },
        ],
        [
          'label'  => Yii::t('app','Referral Count'),
          'format' => 'raw',
          'value'  => function ($model) {
            return $model->invite_tier_count;
          }
        ],
        [
          'label' => Yii::t('app','Referral Reward (AleXs)'),
          'value' => function ($model) {
            return $model->reward_amount;
          }
        ],
        // [
        //     'class'    => 'yii\grid\ActionColumn',
        //     'template' => '
        //         <div class="btn-group">
        //             <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
        //                 Action <span class="dropdown-icon"></span>
        //                 <span class="sr-only">Toggle Dropdown</span>
        //             </button>
        //             <div class="dropdown-menu" role="menu">
        //                 {update}
        //                 {active}
        //             </div>
        //         </div>
        //     ',
        //     'buttons' => [
        //         'update' => function ($url, $model) {
        //             return Html::a(Yii::t('app', 'Update'), ['report/update-rank', 'id' => $model->id], [
        //                 'class' => 'dropdown-item'
        //             ]);
        //         },
        //         'active' => function ($url, $model) {
        //         return Html::a($model->is_doc_active ? Yii::t('app', 'Deactivate') : Yii::t('app', 'Activate'), ['report/activate-rank-url', 'id' => $model->id], [
        //             'class' => 'dropdown-item',
        //             'data'  => [
        //             'confirm' => $model->is_doc_active ? Yii::t('app', 'Are you sure you want to deactivate this Rank URL?') : Yii::t('app', 'Are you sure you want to activate this Rank URL?'),
        //             'method'  => 'post',
        //             ],
        //         ]);
        //     },
        //   ],
        // ]
      ]
    ]); ?>
  </div>
</div>
