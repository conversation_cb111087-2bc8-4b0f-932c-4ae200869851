<?php

$user = Yii::$app->user->identity;

?>

<aside class="main-sidebar sidebar-dark-primary elevation-4">
  <a href="<?= \yii\helpers\Url::home() ?>" class="brand-link">
    <span class="brand-text font-weight-bold pl-1 ml-1"><?= Yii::$app->name ?></span>
  </a>

  <div class="sidebar">
    <div class="user-panel mt-3 pb-3 mb-3 d-flex">
      <div class="image">
        <i class="far fa-user" style="
						width: 100%;
						background: #fff;
						padding: 6px;
						border-radius: 50%;
						font-size: 1.1rem;
				"></i>
      </div>
      <div class="info">
        <p class="mb-0 text-white"><?= Yii::t('app', 'Welcome,') ?></p>
        <p class="mb-0 text-white">
          <?php
					$maxLength = 25;

					$email = $user->email;
					$text = '';

					if (mb_strlen($email) > $maxLength) {
						$text = mb_strimwidth($email, 0, $maxLength, "...");
					} else {
						$text = $email;
					}

					echo $text;
					?>
        </p>
      </div>
    </div>

    <?= \hail812\adminlte\widgets\Menu::widget([
			'items' => [
				[
					"label"   => Yii::t('app', 'User'),
					"url"     => "javascript::void(0)",
					"icon"    => "users",
					"visible" => Yii::$app->user->can('user-management'),
					"items"   => [
						[
							'label' => Yii::t('app', 'Management'),
							'url'   => ['admin/user-mgmt'],
						],
					]
				],
				[
					"label"   => Yii::t('app', 'Referral Management'),
					"url"     => ['admin/referral-mgmt'],
					"icon"    => "user-friends",
					"visible" => Yii::$app->user->can('referral-management'),
				],
				[
					'label'   => Yii::t('app', 'Social Task Management'),
					'url'     => ["admin/social-task-mgmt"],
					"icon"    => "tasks",
					'visible' => Yii::$app->user->can('social-task-management'),
				],
				[
					'label'	  => Yii::t('app', 'Notice Management'),
					'url'	  => ["admin/notice-mgmt"],
					'icon' 	  => "bell",
					'visible' => Yii::$app->user->can('notice-management'),
				],
				[
					'label'   => Yii::t('app', 'Tier List Management'),
					'url'     => ["admin/tier-list-mgmt"],
					'icon'	  => "medal",
					'visible' => Yii::$app->user->can('tier-list-management'),
				],
				[
					"label"   => Yii::t('app', 'Pre Sales Mgmt'),
					"url"     => "javascript::void(0)",
					"icon"    => "money-bill-alt",
					"visible" => Yii::$app->user->can('stage-management') || Yii::$app->user->can('bundle-management'),
					"items"   => [
						[
							'label' => Yii::t('app', 'Bundle Management'),
							'url'   => ['admin/bundle-mgmt'],
							'visible' => Yii::$app->user->can('bundle-management'),
						],
						[
							'label' => Yii::t('app', 'Stage Management'),
							'url'   => ['admin/stage-mgmt'],
							'visible' => Yii::$app->user->can('stage-management'),
						],
					]
				],
				[
					'label'   => Yii::t('app', 'Report'),
					'url'     => "javascript::void(0)",
					"icon"    => "chart-bar",
					'visible' => Yii::$app->user->can('report'),
					'items'   => [
						[
							'label'   => Yii::t('app', 'Wallet Transaction'),
							'url'     => ["report/wallet-transaction-history"],
							'visible' => Yii::$app->user->can('report'),
						],
						[
							'label'   => Yii::t('app', 'Stage Purchase Report'),
							'url'     => ["report/stage-purchase-report"],
							'visible' => Yii::$app->user->can('report'),
						],
						[
							'label'   => Yii::t('app', 'User Inventory History'),
							'url'     => "javescript::void(0)",
							'visible' => Yii::$app->user->can('report'),
							'items'   => [
								[
									'label'   => Yii::t('app', 'User Card History'),
									'url'     => ["report/user-card-history"],
									'visible' => Yii::$app->user->can('report'),
								],
								[
									'label'   => Yii::t('app', 'User Item History'),
									'url'     => ["report/user-item-history"],
									'visible' => Yii::$app->user->can('report'),
								]
							]
						],
						[
							'label'   => Yii::t('app', 'User Song History'),
							'url'     => ["report/user-song-history"],
							'visible' => Yii::$app->user->can('report'),
						],
						[
							'label'   => Yii::t('app', 'User Social Task History'),
							'url'     => ["report/user-social-task-history"],
							'visible' => Yii::$app->user->can('report'),
						],
						[
							'label'   => Yii::t('app', 'Export List'),
							'url'     => ["report/export-list"],
							'visible' => Yii::$app->user->can('report'),
						],
                        [
							'label'   => Yii::t('app', 'Audit Log'),
							'url'     => ["report/audit-log"],
							'visible' => Yii::$app->user->can('report') && Yii::$app->user->identity->role == 'admin',
						],
						[
							'label'   => Yii::t('app', 'User Login History'),
							'url'     => ["report/user-login-log"],
							'visible' => Yii::$app->user->can('report'),
						],
					]
				],
				// [
				//  	'label'   => Yii::t('app', 'Alert Email Management'),
				//  	'url'     => ['admin/alert-email-mgmt'],
				//  	"icon"    => "envelope",
				//  	'visible' => Yii::$app->user->can('alert-email-management'),
				// ],
				// [
				// 	'label'   => Yii::t('app', 'Translation'),
				// 	'url'     => ["admin/manage-translation"],
				// 	"icon"    => "file-code",
				// 	"visible" => Yii::$app->user->can('translation-management'),
				// ],
				[
					'label'   => Yii::t('app', 'System Settings'),
					'url'     => ["admin/manage-setting"],
					"icon"    => "cogs",
					'visible' => Yii::$app->user->can('system-setting'),
				],
				[
					'label'   => Yii::t('app', 'Subadmin Account'),
					'url'     => "javascript::void(0)",
					"icon"    => "user-plus",
					'visible' => Yii::$app->user->can('sub-admin') && in_array(Yii::$app->user->identity->role, ['admin']),
					'items'   => [
						[
							'label'   => Yii::t('app', 'List'),
							'url'     => ['admin/sub-admin-mgmt'],
							'visible' => Yii::$app->user->can('sub-admin'),
						],
						[
							'label'   => Yii::t('app', 'Create Subadmin'),
							'url'     => ['admin/create-sub-admin'],
							'visible' => Yii::$app->user->can('sub-admin'),
						],
					],
				],
				[
					'label'   => Yii::t('app', 'Account'),
					'url'     => "javascript::void(0)",
					"icon"    => "user-circle",
					'items'   => [
						[
							'label'   => Yii::t('app', 'Change Password'),
							'url'     => ['admin/change-password'],
							'visible' => !Yii::$app->user->isGuest,
						],
					],
				],
			]
		]); ?>
  </div>
</aside>