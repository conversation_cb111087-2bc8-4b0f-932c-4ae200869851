<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_referral_reward}}`.
 */
class m250828_084023_create_user_referral_reward_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_referral_reward}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'referral_reward_id' => $this->integer()->notNull(),
            'is_claim' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`referral_reward_id`) references `referral_reward_lookup` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByReferralRewardId` (`referral_reward_id`, `is_delete`)',
            'key `findByUserIsClaim` (`user_id`, `is_claim`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_referral_reward}}');
    }
}
