<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\httpclient\Client;

class API extends Component
{
	const METHOD_POST   = 'post';
	const METHOD_GET    = 'get';
	const METHOD_DELETE = 'delete';

	/**
	 * Make a request to API.
	 *
	 * @param string $url API endpoint
	 * @param string $method API method. Supported POST, GET, DELETE
	 * @param array $data Request data
	 *
	 * @return array API response
	 *
	 * @throws \Exception
	 */
	public function request($url, $method, $data=[])
	{
		try{
			// only support post, get and delete
			$method = strtolower($method);
			if(!in_array($method, [self::METHOD_POST, self::METHOD_GET, self::METHOD_DELETE])){
				throw new \Exception(Yii::t('app', 'API supported POST, GET, DELETE'));
			}

			$result = [];
			$client = new Client();
			$timeout = 30;

			if($method == self::METHOD_POST){ // POST request
				$response = $client->createRequest()
					->setFormat(Client::FORMAT_JSON)
					->setMethod($method)
					->setUrl([$url, "lang" => strtolower(self::getLanguage())])
					->setData($data)
					->setOptions([
						'timeout' => $timeout
					])
					->send();
			}else if($method == self::METHOD_DELETE){ // DELETE request
				$response = $client->createRequest()
					->setFormat(Client::FORMAT_JSON)
					->setMethod($method)
					->setUrl([$url, "lang" => strtolower(self::getLanguage())])
					->setData($data)
					->setOptions([
						'timeout' => $timeout
					])
					->send();
			}else{ // GET request
				$full_url = $url . '?' . http_build_query($data);
				$response = $client->createRequest()
					->setFormat(Client::FORMAT_JSON)
					->setMethod($method)
					->setUrl([$full_url, "lang" => strtolower(self::getLanguage())])
					->setOptions([
						'timeout' => $timeout
					])
					->send();
			}

			if($response->isOk) {
				$result = $response->data;

				// Error handling {error code, details, message}
				if(!empty($result['error'])){
					if(!empty($result['code']) && array_key_exists("data", $result)){
						// throw new \Exception(var_export($result['message'], true), 777777); // Show raw details

						// if(!empty($result['message'])){
						// 	throw new \Exception($result['message'], $result['code']);
						// }else{
						// 	throw new \Exception(Yii::$app->error->message($result['code'], "", "API Error"), $result['code']);
						// }
					}
					if(!empty($result['details'])){
						$er_code = !empty($result['code']) ? $result['code'] : 0;
						throw new \Exception($result['details'][0]['message'], $er_code);
					}
					if(!empty($result['message'])){
						$er_code = !empty($result['code']) ? $result['code'] : 0;
						throw new \Exception($result['message'], $er_code);
					}

					throw new \Exception(Yii::$app->error->message(0));
				}
			}
		}catch(\Exception $e)
		{
			throw $e;
		}

		return $result;
	}

	/**
	 * Gets the language from the request cookie, or guesses it from the
	 * `Accept-Language` header.
	 *
	 * @return string the language code, e.g. "en", "zh-CN".
	 */
	public static function getLanguage()
	{
		if (Yii::$app instanceof \yii\console\Application)
			return "en";

		$lang = Yii::$app->request->cookies->getValue('language', null);

		if (is_null($lang)) {
            $langs = Yii::$app->request->acceptableLanguages;
            foreach ($langs as $l) {
                if (preg_match('/^([a-zA-Z]+)?/', $l, $matches)) {
                    // only scan for zh
                    if ($matches[1] == 'zh') {
                        $lang = 'zh-CN';
                        break ;
                    } else if ($matches[1] == 'en') {
                        $lang = 'en';
                        break ;
                    }
                }
            }
        }

        return $lang;
	}
}
