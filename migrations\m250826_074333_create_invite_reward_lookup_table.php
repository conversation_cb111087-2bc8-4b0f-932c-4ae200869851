<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%invite_reward_lookup}}`.
 */
class m250826_074333_create_invite_reward_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%invite_reward_lookup}}', [
            'id' => $this->primaryKey(),
            'reward_label' => $this->string()->notNull(),
            'invite_qualify_count' => $this->integer()->notNull(),
            'reward_amount' => $this->integer()->notNull(),
            'icon_image_url' => $this->text()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%invite_reward_lookup}}');
    }
}
