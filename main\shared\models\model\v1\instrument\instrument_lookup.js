export default function (sequelize, DataTypes) {
    return sequelize.define('instrument_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        instrument_name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        instrument_image_normal: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        instrument_image_rare: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        instrument_image_legendary: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        instrument_image_mythic: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        instrument_price_normal: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        instrument_price_rare: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        instrument_price_legendary: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        instrument_price_mythic: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        instrument_is_active: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'instrument_lookup',
        timestamps: false,
    })
}