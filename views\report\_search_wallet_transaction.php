<?php

use yii\web\View;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;

use app\models\WalletTransaction;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'username')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Username')) ?>
      <?= $form->field($model, 'address')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Wallet Address')) ?>
      <?= $form->field($model, 'type')->dropDownList([
        ''    => Yii::t('app', 'All'),
        'IN'  => Yii::t('app', 'IN'),
        'OUT' => Yii::t('app', 'OUT'),
      ])->label(Yii::t('app', 'Type')) ?>
      <?= $form->field($model, 'category')->dropDownList(WalletTransaction::getAllType(),[
        'prompt' => Yii::t('app', 'Select Category'),
      ])->label(Yii::t('app', 'Category')) ?>
      <?= $form->field($model, 'tx_id')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Transaction ID')) ?>
      <?= $form->field($model, 'date_range')->widget(DateRangePicker::className(), [
        'model'         => $model,
        'attribute'     => 'date_range',
        'convertFormat' => true,
        'pluginOptions' => [
          'timePicker'  => false,
          'locale' => [
            'format' => 'Y-m-d'
          ]
        ],
        'options' => [],
      ])->label(Yii::t('app', 'Date')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-success float-right']) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>