<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "stage_purchase".
 *
 * @property int $id
 * @property int $user_id
 * @property int $wallet_id
 * @property int $stage_id
 * @property int|null $log_id
 * @property string|null $tx_id
 * @property string|null $payment_jetton
 * @property string|null $contract_address
 * @property float $amount
 * @property float $usd_amount
 * @property float $token_amount
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property Wallet $wallet
 * @property Stage $stage
 */
class StagePurchase extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'stage_purchase';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'wallet_id', 'stage_id', 'created_at'], 'required'],
            [['user_id', 'wallet_id', 'stage_id', 'log_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['amount', 'usd_amount', 'token_amount'], 'number'],
            [['tx_id', 'payment_jetton', 'contract_address'], 'string', 'max' => 255],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['user_id' => 'id']],
            [['wallet_id'], 'exist', 'skipOnError' => true, 'targetClass' => Wallet::className(), 'targetAttribute' => ['wallet_id' => 'id']],
            [['stage_id'], 'exist', 'skipOnError' => true, 'targetClass' => Stage::className(), 'targetAttribute' => ['stage_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'wallet_id' => 'Wallet ID',
            'stage_id' => 'Stage ID',
            'log_id' => 'Log ID',
            'tx_id' => 'Tx ID',
            'payment_jetton' => 'Payment Jetton',
            'contract_address' => 'Contract Address',
            'amount' => 'Amount',
            'usd_amount' => 'Usd Amount',
            'token_amount' => 'Token Amount',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Wallet]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::className(), ['id' => 'wallet_id']);
    }

    /**
     * Gets query for [[Stage]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getStage()
    {
        return $this->hasOne(Stage::className(), ['id' => 'stage_id']);
    }
}
