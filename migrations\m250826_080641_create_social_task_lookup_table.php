<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%social_task_lookup}}`.
 */
class m250826_080641_create_social_task_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%social_task_lookup}}', [
            'id' => $this->primaryKey(),
            'task_name' => $this->string()->notNull(),
            'task_label_name' => $this->string()->notNull(),
            'type' => $this->string()->notNull(),
            'message_key' => $this->string(),
            'label_message_key' => $this->string(),
            'redirect_url' => $this->string(),
            'icon_image_url' => $this->text()->notNull(),
            'reward_point' => $this->integer()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%social_task_lookup}}');
    }
}
