<?php

use yii\db\Migration;

class m251008_092859_create_instrument_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%instrument_lookup}}', [
            'id' => $this->primaryKey(),
            'instrument_name' => $this->string()->notNull(),
            'instrument_image_normal' => $this->text()->notNull(),
            'instrument_image_rare' => $this->text()->notNull(),
            'instrument_image_legendary' => $this->text()->notNull(),
            'instrument_image_mythic' => $this->text()->notNull(),
            'instrument_price_normal' => $this->decimal(10, 2)->notNull(),
            'instrument_price_rare' => $this->decimal(10, 2)->notNull(),
            'instrument_price_legendary' => $this->decimal(10, 2)->notNull(),
            'instrument_price_mythic' => $this->decimal(10, 2)->notNull(),
            'instrument_is_active' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'secondary_updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%instrument_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_092859_create_instrument_lookup cannot be reverted.\n";

        return false;
    }
    */
}
