<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\helpers\ArrayHelper;

use app\models\User;
use app\models\Wallet;
use app\models\UserLoginSession;
use app\models\SystemSetting;

class UserController extends Controller
{
    /**
     * Logs user login every day at 00:00:00
     * 
     * It will update user accumulated days and continuous login
     * If user not login, it will reset continuous login to 0
     */
  public function actionLogUserLogin()
  {
    $this->log("Begin logging user login");

    $yesterday_start = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
    $yesterday_end = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

    // find or create the latest checked time of user login
    $last_check = SystemSetting::findOrCreate(SystemSetting::LAST_CHECKED_TIME, 0);

    // if last checked time is same as yesterday end, it means already logged, skip it
    if ($last_check->value == $yesterday_end) {
      $_end = date('Y-m-d 23:59:59', $yesterday_end);
      $this->log("Already logged for {$_end}");
      return;
    }

    /* Currently wallet table is not used, all action related to wallet table will be commented out */
    
    // $list = UserLoginSession::find()
    //   ->alias('uls')
    //   ->leftJoin('wallet w', 'w.id = uls.wallet_id')
    //   ->andWhere(['between', 'uls.created_at', $yesterday_start, $yesterday_end])
    //   ->andWhere(['w.is_delete' => 0])
    //   ->andWhere(['uls.is_delete' => 0]);

    // get all login user yesterday
    $list = UserLoginSession::find()
      ->alias('uls')
      ->andWhere(['between', 'uls.created_at', $yesterday_start, $yesterday_end])
      ->andWhere(['uls.is_delete' => 0]);

    $count = $list->count();

    if ($count <= 0) {
      $this->log("No user login found");
      // reset all user continuous login to 0 if no user login found
      echo "Reset all user continuous login\n";
      User::updateAll(
        ['continuous_login' => 0],
        ['and', ['>', 'continuous_login', 0], ['is_delete' => 0]]
      );
      //echo "Reset all user wallet continuous login\n";
      // Wallet::updateAll(
      //   ['continuous_login' => 0],
      //   ['and', ['>', 'continuous_login', 0], ['is_delete' => 0]]
      // );
      return;
    }

    // get all login wallet
    // $wallet_ids = ArrayHelper::getColumn($list->all(), 'wallet_id');

    // get all login user id
    $login_user_ids = ArrayHelper::getColumn($list->all(), 'user_id');

    // get all user that are in the login user id list
    $_list = User::find();
    // $_list->andWhere(['in', 'id', $wallet_ids]);
    $_list->andWhere(['in', 'id', $login_user_ids]);
    $_list->andWhere(['is_delete' => 0]);
    $user_ids = ArrayHelper::getColumn($_list->all(), 'id');
    echo "Found " . count($user_ids) . " user(s)\n";

    $db = Yii::$app->db->beginTransaction();

    try {
      echo "Begin update user accumulated days + continuous login \n";

      // update all user continuous login and accumulated login that are in the login user id list
      User::updateAll(
        ['accumulate_login' => new \yii\db\Expression('accumulate_login + 1'), 'continuous_login' => new \yii\db\Expression('continuous_login + 1')],
        ['and', ['in', 'id', $user_ids], ['is_delete' => 0]]
      );

      echo "User accumulated days + continuous login updated\n";

      echo "Begin update inactive continuous login\n";

      // reset all user continuous login that are not in the login user id list
      User::updateAll(
        ['continuous_login' => 0],
        ['and', ['not in', 'id', $user_ids], ['is_delete' => 0]]
      );

      echo "Inactive user continuous login updated\n";

      //echo "Begin update wallet accumulated days + continuous login \n";

      // Wallet::updateAll(
      //   ['accumulate_login' => new \yii\db\Expression('accumulate_login + 1'), 'continuous_login' => new \yii\db\Expression('continuous_login + 1')],
      //   ['and', ['in', 'id', $wallet_ids], ['is_delete' => 0]]
      // );

      //echo "Wallet accumulated days + continuous login updated\n";

      // echo "Begin update inactive wallet continuous login\n";

      // Wallet::updateAll(
      //   ['continuous_login' => 0],
      //   ['and', ['not in', 'id', $wallet_ids], ['is_delete' => 0]]
      // );

      // echo "Inactive wallet continuous login updated\n";

      // update last checked time
      $last_check->value      = $yesterday_end;
      $last_check->updated_at = time();

      if ($last_check->update(false, ['value', 'updated_at']) === false) {
        throw new \Exception("Failed to update last checked time");
      }

      $db->commit();
      $this->log("End logging user login");
    } catch (\Exception $e) {
      $db->rollback();
      $this->log("Error: {$e->getMessage()}", 'error');
      return;
    }

    $this->log("{$count} user(s) logged for {$yesterday_end}");
  }

/**
 * Log a message to the console.
 *
 * @param string $comment The message to log
 * @param string $category The category of the log message. Defaults to 'info'
 */
  protected function log($comment, $category = 'info')
  {
    $datetime = date('Y-m-d H:i:s');
    echo "[{$datetime}][{$category}] {$comment}\n";
  }
}