import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, Keyboard, InlineKeyboard } from "grammy"
import { run } from "@grammyjs/runner"
import common from "../shared/imports/common.js";
import setupHomeMenu from "./menu/home.js";

const bot = new Bot(common.config.telegram.token);
const api = new Api(common.config.telegram.token);
const menus = {}
const { showMenu } = setupHomeMenu(menus, bot, api);

const handleErrorMessage = async (ctx, error) => {
    const error_message =
        error.message || "Something went wrong, please contact support";
    console.log({
        user_id: ctx.from.id,
        error: error.message,
    });
    await ctx.reply(error_message, { parse_mode: "HTML" });
};

bot.use(async (ctx, next) => {
    const user = ctx.from?.id;
    const chatType = ctx.chat?.type;
    const text = ctx.message?.text || ctx.callbackQuery?.data || "";

    console.log(`[${new Date().toISOString()}] Received update in ${chatType} chat from user ${user}. Text: "${text}"`);
    if (!ctx.from || ctx.from.is_bot) {
        console.log(`[${new Date().toISOString()}] Update ignored: from a bot or no user info.`);
        return; // Stop processing if it's a bot
    }

    await next();
})

bot.command("start", async (ctx) => {
    try {
        await showMenu(ctx)
    } catch (e) {
        await handleErrorMessage(ctx, e)
    }
})

bot.catch((err) => {
    const ctx = err.ctx;
    console.error(`Error while handling update ${ctx.update.update_id}:`);
    const e = err.error;
    if (e instanceof GrammyError) {
        console.error("Error in request:", e.description);
    } else if (e instanceof HttpError) {
        console.error("Could not contact Telegram:", e);
    } else {
        console.error("Unknown error:", e);
    }
});

async function startBot() {
    const botInfo = await bot.api.getMe();
    common.util_helper.spacer(20)
    console.log(`[${new Date().toISOString()}] Bot starting...`);
    run(bot);
    console.log(`[${new Date().toISOString()}] Bot @${botInfo.username} is now online!`);
    common.util_helper.spacer(10)
}

// Run the startup function
startBot().catch((err) => {
    console.error("Failed to start bot:", err);
});