<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%stage_purchase}}`.
 */
class m251021_041243_create_stage_purchase_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%stage_purchase}}', [
            'id'               => $this->primaryKey(),
            'user_id'          => $this->integer()->notNull(),
            'wallet_id'        => $this->integer()->notNull(),
            'stage_id'         => $this->integer()->notNull(),
            'log_id'           => $this->integer(),
            'tx_id'            => $this->string(255),
            'payment_jetton'   => $this->string(255),
            'contract_address' => $this->string(255),
            'amount'           => $this->decimal(36, 18)->defaultValue(0)->notNull(),
            'usd_amount'       => $this->decimal(36, 18)->defaultValue(0)->notNull(),
            'token_amount'     => $this->decimal(36, 18)->defaultValue(0)->notNull(),
            'created_at'       => $this->bigInteger()->notNull(),
            'updated_at'       => $this->bigInteger(),
            'is_delete'        => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`wallet_id`) references `wallet` (`id`)',
            'foreign key (`stage_id`) references `stage` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findTxId` (`tx_id`)',
            'key `findByStageId` (`stage_id`)',
            'key `findByUserId` (`user_id`)',
            'key `findByWalletId` (`wallet_id`)',
            'key `findByLogId` (`log_id`)',
            'key `findByDate` (`created_at`)',
            'key `findByPaymentJetton` (`payment_jetton`)',
            'key `findByContractAddress` (`contract_address`)'
        ], "engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%stage_purchase}}');
    }
}
