import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router()

function ucwordsFromUnderscores(str) {
    // Step 1: Replace '_' with ' ' (using replaceAll for modern JS)
    const withSpaces = str.replaceAll('_', ' ');

    // Step 2: ucwords - capitalize each word
    return withSpaces
        .toLowerCase()  // Normalize to lowercase
        .split(' ')     // Split on spaces
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))  // Capitalize first letter
        .join(' ');     // Join back
}

// ROUTE: GET /presale/stage-list
router.get("/stage-list", async (req, res) => {
    const transaction = await query.sequelize.transaction();

    try {
        const stage_list = await query.models.stage.findAll({
            where: {
                is_delete: false
            },
            order: [
                ['id', 'ASC']
            ],
            transaction
        })

        await transaction.commit()
        return res.status(200).json({
            data: stage_list.map((_stage) => {
                let _status = ""

                if (common.util_helper.getCurrentEpochTime() > _stage.should_end_date) {
                    _status = "ended"
                } else {
                    _status = _stage.status
                }

                return {
                    id: _stage.id,
                    name: _stage.name,
                    price_per_token: _stage.price_per_token * 1,
                    token_available: _stage.token_available,
                    token_sold: 0,
                    should_end_date: _stage.should_end_date,
                    available_usd: 0,
                    status: _status,
                }
            }),
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        if (!transaction.finished) {
            await transaction.rollback()
        }
        return res.status(400).json({
            data: {},
            status: 400,
            msg: error.message,
            error: true
        });
    }
})

// ROUTE: GET /presale/bundle-list
router.get("/bundle-list", async (req, res) => {
    const transaction = await query.sequelize.transaction();

    try {
        const bundle_list = await query.models.bundle.findAll({
            where: {
                is_delete: false
            },
            order: [
                ['id', 'ASC']
            ],
            include: [
                {
                    model: query.models.bundle_reward,
                    as: 'bundle_rewards',
                    where: {
                        is_delete: false
                    }
                }
            ],
            transaction
        })

        await transaction.commit()
        return res.status(200).json({
            data: bundle_list.map((_bundle) => {
                return {
                    id: _bundle.id,
                    usd_price: _bundle.usd_price * 1,
                    token_allocation: _bundle.token_allocation,
                    total_supply: _bundle.total_supply,
                    total_sold: 0,
                    status: _bundle.status,
                    reward_list: _bundle.bundle_rewards.map((_reward) => {
                        return {
                            label: ucwordsFromUnderscores(_reward.type),
                            type: _reward.type,
                            amount: _reward.amount
                        }
                    })
                }
            }),
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        if (!transaction.finished) {
            await transaction.rollback()
        }
        return res.status(400).json({
            data: {},
            status: 400,
            msg: error.message,
            error: true
        });
    }
})

export default router;
