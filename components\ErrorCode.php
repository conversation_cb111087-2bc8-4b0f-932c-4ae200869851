<?php

namespace app\components;

use Yii;
use yii\base\Component;

class ErrorCode extends Component
{
	/**
	 * Return error message based on error code
	 *
	 * @param int $code error code
	 * @param int $user_id user id
	 * @param string $remark additional remark
	 * 
	 * @return string error message
	 */
	public function message($code, $user_id=null, $remark=null)
	{
		// Mandrill Error Code
		// -1 => Invalid API key
		// 02 => Service Temporarily Unavailable
		// 11 => No message exists
		// 12 => No subaccount exists

		$default_msg = "Unknown error occured";

		$content = [
			101 => Yii::t('app', "Currently email service is not supported"),
			102 => Yii::t('app', 'Access token is invalid'),
		];

		if (!empty($content[$code]))
			$default_msg = $content[$code];

		return $default_msg;
	}
}
