<?php

use yii\db\Migration;

class m250828_084516_update_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('user', 'current_referral_ranking', $this->string()->after('account_type'));
        $this->addColumn('user', 'accumulate_login', $this->integer()->defaultValue(0)->after('current_referral_ranking')); 
        $this->addColumn('user', 'continuous_login', $this->integer()->defaultValue(0)->after('accumulate_login'));
        $this->addColumn('user', 'is_aleko_verified', $this->boolean()->defaultValue(false)->after('is_verified'));

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('user', 'current_referral_ranking');
        $this->dropColumn('user', 'accumulate_login');
        $this->dropColumn('user', 'continuous_login');
        $this->dropColumn('user', 'is_aleko_verified');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250828_084516_update_user_table cannot be reverted.\n";

        return false;
    }
    */
}
