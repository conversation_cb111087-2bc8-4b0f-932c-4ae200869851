<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "translation_lookup".
 *
 * @property int $id
 * @property string|null $message_key
 * @property string|null $en_us
 * @property string|null $zh_cn
 * @property string|null $ja_jp
 * @property string|null $ko_kr
 * @property string|null $ms_my
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class TranslationLookup extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'translation_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['message_key', 'en_us', 'zh_cn', 'ja_jp', 'ko_kr', 'ms_my', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['created_at'], 'required'],
            [['created_at', 'updated_at', 'is_delete'], 'integer'],
            [['message_key'], 'string', 'max' => 50],
            [['en_us', 'zh_cn', 'ja_jp', 'ko_kr', 'ms_my'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'message_key' => 'Message Key',
            'en_us' => 'En Us',
            'zh_cn' => 'Zh Cn',
            'ja_jp' => 'Ja Jp',
            'ko_kr' => 'Ko Kr',
            'ms_my' => 'Ms My',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

}
