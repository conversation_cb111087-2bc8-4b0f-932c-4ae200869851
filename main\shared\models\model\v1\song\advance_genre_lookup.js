export default function (sequelize, DataTypes) {
    return sequelize.define('advance_genre_lookup', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        genre_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        boost_instrument_one_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_two_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_three_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        boost_instrument_four_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        is_instrument_boost_allow: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        genre_type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "advanced"
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'advance_genre_lookup',
        timestamps: false,
    });
}