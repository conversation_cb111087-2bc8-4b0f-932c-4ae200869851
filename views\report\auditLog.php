<?php

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Audit Log');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_audit_log', [
      'model'   => $model,
      'page'    => 'audit-log',
      'pjax_id' => "#pjax-audit-log",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app', 'Email'),
          'value' => function ($model) {
            if (!empty($model->action_by)) {
              return $model->actionBy->email;
            } else {
              return '';
            }
          }
        ],
        [
          'label' => Yii::t('app', 'Action'),
          'value' => function ($model) {
            return $model->function;
          }
        ],
        [
          'label' => Yii::t('app', 'Previous Value'),
          'value' => function ($model) {
            if ($model->remark == 'Maintenance Mode') {
              if ($model->old_value == "true") {
                return Yii::t('app', 'Enable');
              } else {
                return Yii::t('app', 'Disable');
              }
            } else {
              return $model->old_value;
            }
          }
        ],
        [
          'label' => Yii::t('app', 'Updated Value'),
          'value' => function ($model) {
            if ($model->remark == 'Maintenance Mode') {
              if ($model->value == "true") {
                return Yii::t('app', 'Enable');
              } else {
                return Yii::t('app', 'Disable');
              }
            } else {
              return $model->value;
            }
          }
        ],
        [
          'label' => Yii::t('app', 'Remark'),
          'value' => function ($model) {
            return $model->remark;
          }
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ]
      ]
    ]); ?>
  </div>
</div>
