<?php

use yii\db\Migration;

class m251008_093953_create_user_song_selected_lookup extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_song_selected_lookup}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'song_id' => $this->integer()->notNull(),
            'genre_id' => $this->integer()->notNull(),
            'tap_count' => $this->integer()->notNull()->defaultValue(0),
            'boost_instrument_shop_card_one_id' => $this->integer(),
            'boost_instrument_shop_card_two_id' => $this->integer(),
            'boost_instrument_shop_card_three_id' => $this->integer(),
            'boost_instrument_shop_card_four_id' => $this->integer(),
            'exp_booster_user_shop_item_id' => $this->integer(),
            'token_booster_user_shop_item_id' => $this->integer(),
            'tap_booster_user_shop_item_id' => $this->integer(),
            'is_active' => 'tinyint(1) DEFAULT 1 NOT NULL',
            'is_used' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`song_id`) references `song_lookup` (`id`)',
            'foreign key (`genre_id`) references `advance_genre_lookup` (`id`)',
            'foreign key (`boost_instrument_shop_card_one_id`) references `user_shop_card` (`id`)',
            'foreign key (`boost_instrument_shop_card_two_id`) references `user_shop_card` (`id`)',
            'foreign key (`boost_instrument_shop_card_three_id`) references `user_shop_card` (`id`)',
            'foreign key (`boost_instrument_shop_card_four_id`) references `user_shop_card` (`id`)',
            'foreign key (`exp_booster_user_shop_item_id`) references `user_shop_item` (`id`)',
            'foreign key (`token_booster_user_shop_item_id`) references `user_shop_item` (`id`)',
            'foreign key (`tap_booster_user_shop_item_id`) references `user_shop_item` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findBySongId` (`song_id`, `is_delete`)',
            'key `findByGenreId` (`genre_id`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_song_selected_lookup}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m251008_093953_create_user_song_item cannot be reverted.\n";

        return false;
    }
    */
}
