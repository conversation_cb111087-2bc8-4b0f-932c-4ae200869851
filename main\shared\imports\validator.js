import { exp_validator } from "../helpers/validator_helper.js";
import authValidator from "../validator/auth.js"
import tapValidator from "../validator/v1/tap.js"
import shopValidator from "../validator/v1/shop.js"
import soundkeepValidator from "../validator/v1/soundkeep.js"

export default {
    exp_validator,
    items_validator: {
        authValidator,
        tapValidator,
        shopValidator,
        soundkeepValidator,
    }
}