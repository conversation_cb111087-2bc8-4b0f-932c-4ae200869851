<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%referral_reward_lookup}}`.
 */
class m250828_083449_create_referral_reward_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%referral_reward_lookup}}', [
            'id' => $this->primaryKey(),
            'invite_tier_count' => $this->integer()->notNull(),
            'reward_type' => $this->string(),
            'reward_amount' => $this->integer(),
            'special_tier_title' => $this->string(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%referral_reward_lookup}}');
    }
}
