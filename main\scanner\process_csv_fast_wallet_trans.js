import fs from "fs";
import path from "path";
import { fileURLToPath } from 'url';
import csv from "csv-parser";

import server from "../shared/imports/server.js";
import common from "../shared/imports/common.js";
import query from "../shared/imports/query.js"
import validator from "../shared/imports/validator.js"
import helper from "../shared/imports/helper.js";


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function sanitizeRow(row) {
    const cleanedData = {
        id: parseInt(row.id, 10),
        user_id: parseInt(row.user_id, 10),
        is_verified: row.verified.toUpperCase() === 'TRUE',
        referred_by: row.referred_by ? parseInt(row.referred_by, 10) : null,
        rewards_earned: parseInt(row.rewards_earned, 10),
        referrals: parseInt(row.referrals, 10),
        username: row.user_name,
    };
    return cleanedData;
}

async function processUserCSV(file_path) {
    if (!fs.existsSync(file_path)) {
        console.error(`Error: File not found at ${file_path}`);
        return;
    }

    console.log(`Starting to process file: ${file_path}`);
    // PHASE 1: LOOP PROCESS CSV FILE AND CREATE USER_MAP & DOWNLINE_MAP
    // ----------------------------------------
    const all_users_data_list = [];
    const all_user_map = new Map();
    const downline_map = new Map();
    await new Promise((resolve, reject) => {
        fs.createReadStream(file_path)
            .pipe(csv())
            .on('data', (row) => {
                // console.log(row);
                const cleaned_row = sanitizeRow(row);
                all_users_data_list.push(cleaned_row);
                console.log(cleaned_row)
                all_user_map.set(cleaned_row.user_id, cleaned_row);

                //If this user has a upline, record the relationship
                if (cleaned_row.referred_by !== null) {
                    const upline_id = cleaned_row.referred_by;
                    if (!downline_map.has(upline_id)) {
                        downline_map.set(upline_id, []);
                    }
                    downline_map.get(upline_id).push(cleaned_row.user_id);
                }
            })
            .on('end', resolve)
            .on('error', reject);
        // .on('end', () => {
        //     console.log('CSV file successfully processed.');
        //     console.log(`Total rows read and cleaned: ${all_users_data_list.length}`);
        // })
        // .on('error', (err) => {
        //     console.error('Error while processing CSV:', err.message);
        // });
    });


    console.log('CSV file successfully processed.');
    console.log(`Total rows read and cleaned: ${all_users_data_list.length}`);
    processReferralSystem(all_users_data_list, all_user_map, downline_map);
}

async function processReferralSystem(all_users_data_list, all_user_map, downline_map) {
    // PHASE 2: IDENTIFY UPPEST/ROOTS AND CREATE ORDERED INSERTION LIST
    // ----------------------------------------
    const ordered_insert_list = [];
    const roots = [];

    // Find all the "root" users (those without an upline)
    for (const user of all_user_map.values()) {
        if (user.referred_by === null) {
            roots.push(user);
        }
    }
    console.log(`Found ${roots.length} root users (top of the hierarchy).`);

    //Queue with Breadth-First Search (BFS) traversal
    const queue = [...roots];
    const processed_user_ids = new Set(roots.map(u => u.user_id));

    while (queue.length > 0) {
        const current_user = queue.shift(); //take the first user in the queue and pop from the queue
        ordered_insert_list.push(current_user);

        const direct_downlines = downline_map.get(current_user.user_id) || [];

        for (const downline_id of direct_downlines) {
            if (!processed_user_ids.has(downline_id)) {
                processed_user_ids.add(downline_id);
                const downline_user = all_user_map.get(downline_id);
                if (downline_user) {
                    queue.push(downline_user);
                }
            } else {
                console.warn(`Warning: Detected a potential duplicate entry involving user ID: ${downline_id}. Skipping.`);
            }
        }
    }

    // Sanity check: If already process every user?
    if (ordered_insert_list.length !== all_user_map.size) {
        console.error(`\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!`);
        console.error(`ERROR: Mismatch in user count! Processed: ${ordered_insert_list.length}, Total: ${all_user_map.size}.`);
        console.error('This is likely due to "orphan" users whose referrer does not exist in the CSV.');

        // Find and log the missing users
        const missing_users = [];
        for (const [userId, userObject] of all_user_map.entries()) {
            if (!processed_user_ids.has(userId)) {
                missing_users.push(userObject);
            }
        }

        if (missing_users.length > 0) {
            console.error('\n--- The following users were NOT processed: ---');
            for (const user of missing_users) {
                console.error(`  - User: "${user.username}" (ID: ${user.user_id})`);
                console.error(`    Reason: Their upline referrer ID "${user.referred_by}" does not exist in the user list.`);
            }
        }
        console.error(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n`);
        // We will stop the process here because the data is inconsistent.
        return;
    } else {
        console.log(`Phase 2 Complete: Created an ordered list of ${ordered_insert_list.length} users.`);
    }

    // PHASE 3: SEQUENTIAL DATABASE INSERTION
    // ----------------------------------------
    processUserCreation(ordered_insert_list)
}

async function processUserCreation(ordered_insert_list) {
    try {
        await query.sequelize.transaction(async (t) => {

            const now = common.util_helper.getCurrentEpochTime();

            // --- STEP 1 & 2: BULK INSERT USERS AND GET THEIR NEW IDs ---
            console.log("Step 1 & 2: Bulk creating users and building ID map...");
            const users_to_create = ordered_insert_list.map(user => ({
                user_telegram_id: user.user_id.toString(),
                is_premium: false,
                first_last_name: `${''} ${''}`,
                username: user.username.toString(),
                // temp_referral_user_id: user.referred_by,
                profile_pic: "",
                is_aleko_verified: user.is_verified,
                created_at: now,
                updated_at: now,
                is_delete: false,
            }));
            const createdUserInstances = await query.models.user.bulkCreate(users_to_create, {
                transaction: t,
                returning: true,
            });

            const telegram_id_to_db_id_map = new Map();
            for (const instance of createdUserInstances) {
                telegram_id_to_db_id_map.set(parseInt(instance.user_telegram_id, 10), instance.id);
            }
            console.log("...User creation and ID mapping complete.");

            // --- STEP 3: PREPARE REFERRAL & CREDIT DATA (HANDLING GROUP_TAG DEPENDENCY) ---
            console.log("Step 3: Preparing referral and credit data with group_tag dependency...");

            const referrals_to_create = [];
            const alex_credits_to_create = [];
            const energy_credits_to_create = [];
            const wallet_transaction_to_create = [];

            // This new map is crucial for tracking generated group_tags in memory.
            const db_id_to_referral_data_map = new Map();

            // Loop through the hierarchically sorted list. 
            for (const [index, user] of ordered_insert_list.entries()) {
                const new_user_db_id = telegram_id_to_db_id_map.get(user.user_id);
                if (!new_user_db_id) continue;

                // A. Generate this user's independent referral data
                const new_hash_id = helper.referral_helper.generateHashId(new_user_db_id);
                const upline_db_id = user.referred_by ? telegram_id_to_db_id_map.get(user.referred_by) : null;
                const upline_group_tag = upline_db_id ? (db_id_to_referral_data_map.get(upline_db_id) || {}).group_tag : null;

                const new_group_tag = helper.referral_helper.buildReferralTag(new_hash_id, upline_group_tag);
                db_id_to_referral_data_map.set(new_user_db_id, { group_tag: new_group_tag });
                referrals_to_create.push({
                    user_id: new_user_db_id,
                    hash_id: new_hash_id,
                    group_tag: new_group_tag,
                    referral_user_id: upline_db_id,
                    referral_code: helper.referral_helper.referralCodeGenerator(),
                    created_at: now,
                    updated_at: now,
                    is_delete: 0,
                });
            }
            await query.models.user_referral.bulkCreate(referrals_to_create, { transaction: t });
            console.log("...Referral creation complete.");



            // --- TIER 2 (PART B): BULK INSERT CREDITS ---
            console.log("Tier 2b: Preparing and bulk creating credits...");
            const credits_to_create = [];
            for (const user of ordered_insert_list) {
                const new_user_db_id = telegram_id_to_db_id_map.get(user.user_id);
                if (!new_user_db_id) continue;
                credits_to_create.push({ user_id: new_user_db_id, type: "alex", balance: user.rewards_earned, created_at: now, updated_at: now, is_delete: 0 });
                credits_to_create.push({ user_id: new_user_db_id, type: "energy", balance: 0.0, created_at: now, updated_at: now, is_delete: 0 });
            }

            const created_credit_instances = await query.models.credit.bulkCreate(credits_to_create, {
                transaction: t, returning: true,
            });

            // --- MAP 2: USER PRIMARY KEY + TYPE -> CREDIT PRIMARY KEY ---
            console.log("Building in-memory Credit ID map...");
            const credit_id_map = new Map();
            for (const creditInstance of created_credit_instances) {
                // Create a unique key for each credit record, e.g., "123_alex"
                const mapKey = `${creditInstance.user_id}_${creditInstance.type}`;
                credit_id_map.set(mapKey, creditInstance.id);
            }

            // --- TIER 3: BULK INSERT WALLET TRANSACTIONS ---
            console.log("Tier 3: Preparing and bulk creating wallet transactions...");
            const wallet_transactions_to_create = [];


            for (const user of ordered_insert_list) {
                const new_user_db_id = telegram_id_to_db_id_map.get(user.user_id);
                if (!new_user_db_id) continue;

                // We only create a transaction if there are rewards earned
                if (user.rewards_earned > 0) {
                    // Use Map 2 to find the correct credit_id for this user's 'alex' credit
                    const credit_map_key = `${new_user_db_id}_alex`;
                    const alex_credit_id = credit_id_map.get(credit_map_key);

                    if (alex_credit_id) {
                        wallet_transactions_to_create.push({
                            user_id: new_user_db_id,
                            credit_id: alex_credit_id,
                            type: 'IN',
                            amount: parseFloat(user.rewards_earned),
                            before_balance: 0.0,
                            after_balance: parseFloat(user.rewards_earned),
                            category: 'alex',
                            description: 'Intial Migrated Alex Credit',
                            remark: `initial_migrated_alex_credit`,
                            created_at: now,
                            updated_at: now,
                            is_delete: 0,
                        });
                    }
                }

            }
            if (wallet_transactions_to_create.length > 0) {
                await query.models.wallet_transaction.bulkCreate(wallet_transactions_to_create, { transaction: t });
            }
            console.log("...Wallet transaction creation complete.");
        });
        console.log(`\nTransaction committed successfully!`);
        console.log(`--- Phase 3 Complete: All ${ordered_insert_list.length} users and their referrals have been inserted. ---`);
    } catch (error) {
        console.error('\n--- An error occurred during the transaction! ---');
        console.error('TRANSACTION ROLLED BACK. No data was saved.');
        console.error('Error details:', error.message);
    }
}

const csvFilePath = path.join(__dirname, '..', 'shared', 'assets', 'user_list_sep10_v2.csv');
processUserCSV(csvFilePath)