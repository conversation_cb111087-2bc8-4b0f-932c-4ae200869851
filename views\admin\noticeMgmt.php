<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;

use app\models\NoticeLookup;

$this->title = Yii::t('app', 'Notice Management');

$this->registerCss('
  .alert-info label {
    margin-bottom: 0;
  }
  ul {
    margin-bottom: 0;
  }
');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_notice', [
      'model'   => $model,
      'page'    => 'notice-mgmt',
      'pjax_id' => "#pjax-notice-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="text-right mb-3">
      <?= Html::a(Yii::t('app', 'Create'), ['admin/create-notice'], [
        'class' => 'btn btn-success',
      ]); ?>
    </div>

    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app','Notice Name'),
          'value' => function ($model) {
            return $model->notice_label;
          },
        ],
        [
          'label' => Yii::t('app','Notice Description'),
          'value' => function ($model) {
            if (empty($model->notice_description)) {
              return '-';
            }
            return $model->notice_description;
          },
        ],
        // [
        //   'label' => Yii::t('app','Reward AleXs'),
        //   'value' => function ($model) {
        //     if (empty($model->alex_reward_amount)) {
        //       return 0;
        //     }
        //     return $model->alex_reward_amount * 1;
        //   },
        // ],
        // [
        //   'label' => Yii::t('app','Reward Boost'),
        //   'value' => function ($model) {
        //     if (empty($model->boost_reward_amount)) {
        //       return 0;
        //     }
        //     return $model->boost_reward_amount * 1;
        //   },
        // ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
        [
          'class'    => 'yii\grid\ActionColumn',
          'template' => '
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                Action <span class="dropdown-icon"></span>
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu" role="menu">
                {update}
                {delete}
              </div>
            </div>
          ',
          'buttons' => [
            'update' => function ($url, $model) {
              return Html::a(Yii::t('app','Update'), ['admin/update-notice', 'id' => $model->id], [
                'class' => 'dropdown-item',
              ]);
            },
            'delete' => function ($url, $model) {
              return Html::a(Yii::t('app','Delete'), ['admin/delete-notice', 'id' => $model->id], [
                'class' => 'dropdown-item',
                'data'  => [
                  'confirm' => Yii::t('app', 'Are you sure you want to delete this notice?'),
                  'method'  => 'post',
                ],
              ]);
            },
          ],
        ],
      ]
    ]); ?>
  </div>
</div>
