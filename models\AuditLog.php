<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "audit_log".
 *
 * @property int $id
 * @property string|null $function
 * @property string|null $old_value
 * @property string|null $value
 * @property string|null $remark
 * @property int|null $action_by
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Admin $actionBy
 */
class AuditLog extends \yii\db\ActiveRecord
{
    const SUSPEND_ADMIN         = 'SUSPEND ADMIN';
    const FUNC_CHANGE_PASSWORD  = 'CHANGE PASSWORD';
    const UPDATE_SYSTEM_SETTING = 'UPDATE SYSTEM SETTING';
    const MANAGE_SOCIAL_TASK    = 'MANAGE SOCIAL TASK';
    const MANAGE_NOTICE         = 'MANAGE NOTICE';
    const MANAGE_STAGE          = 'MANAGE STAGE';
    const MANAGE_BUNDLE         = 'MANAGE BUNDLE';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'audit_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['function', 'old_value', 'value', 'remark', 'action_by', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['old_value', 'value', 'remark'], 'string'],
            [['action_by', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['created_at'], 'required'],
            [['function'], 'string', 'max' => 255],
            [['action_by'], 'exist', 'skipOnError' => true, 'targetClass' => Admin::class, 'targetAttribute' => ['action_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'function' => 'Function',
            'old_value' => 'Old Value',
            'value' => 'Value',
            'remark' => 'Remark',
            'action_by' => 'Action By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[ActionBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActionBy()
    {
        return $this->hasOne(Admin::class, ['id' => 'action_by']);
    }

    /**
     * Creates a new AuditLog model.
     * 
     * @param array $params
     * 
     * @return AuditLog
     */
    public static function create($params)
    {
        $attribute = [
            'function'  => null,
            'old_value' => null,
            'value'     => null,
            'remark'    => null,
            'action_by' => null,
        ];

        foreach ($attribute as $key => $value) {
            if (isset($params[$key])) {
                $attribute[$key] = $params[$key];
            }
        }

        $model             = new AuditLog;
        $model->function   = $attribute['function'];
        $model->old_value  = $attribute['old_value'];
        $model->value      = $attribute['value'];
        $model->remark     = $attribute['remark'];
        $model->action_by  = $attribute['action_by'];
        $model->created_at = time();
        $model->save(false);

        return $model;
    }

}
