<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_shop_card".
 *
 * @property int $id
 * @property int $user_id
 * @property int $rarity_id
 * @property int $instrument_id
 * @property int $is_used
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property RarityLookup $rarity
 * @property InstrumentLookup $instrument
 * @property UserSongSelectedLookup[] $userSongSelectedLookups
 * @property UserSongSelectedLookup[] $userSongSelectedLookups0
 * @property UserSongSelectedLookup[] $userSongSelectedLookups1
 * @property UserSongSelectedLookup[] $userSongSelectedLookups2
 */
class UserShopCard extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_shop_card';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'rarity_id', 'instrument_id', 'created_at'], 'required'],
            [['user_id', 'rarity_id', 'instrument_id', 'is_used', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['user_id' => 'id']],
            [['rarity_id'], 'exist', 'skipOnError' => true, 'targetClass' => RarityLookup::className(), 'targetAttribute' => ['rarity_id' => 'id']],
            [['instrument_id'], 'exist', 'skipOnError' => true, 'targetClass' => InstrumentLookup::className(), 'targetAttribute' => ['instrument_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'rarity_id' => 'Rarity ID',
            'instrument_id' => 'Instrument ID',
            'is_used' => 'Is Used',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Rarity]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRarity()
    {
        return $this->hasOne(RarityLookup::className(), ['id' => 'rarity_id']);
    }

    /**
     * Gets query for [[Instrument]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInstrument()
    {
        return $this->hasOne(InstrumentLookup::className(), ['id' => 'instrument_id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['boost_instrument_shop_card_one_id' => 'id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups0()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['boost_instrument_shop_card_two_id' => 'id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups1]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups1()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['boost_instrument_shop_card_three_id' => 'id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups2]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups2()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['boost_instrument_shop_card_four_id' => 'id']);
    }
}
