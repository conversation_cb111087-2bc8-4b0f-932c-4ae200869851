<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user}}`.
 */
class m250826_064331_create_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user}}', [
            'id' => $this->primaryKey(),
            'user_telegram_id' => $this->string()->unique(),
            'is_premium' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'username' => $this->string(),
            'first_last_name' => $this->string()->notNull(),
            'profile_pic' => $this->text(),
            'email_address' => $this->string()->unique(),
            'password' => $this->text(),
            'jwt_token' => $this->text(),
            'temp_referral_user_id' => $this->integer(),
            'has_joined_channel' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'is_username_checked' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'account_type' => $this->string()->notNull()->defaultValue('telegram'),
            'is_verified' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'verification_token' => $this->text(),
            'verification_token_expired_date' => $this->bigInteger(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByTelegramIdPremium` (`user_telegram_id`, `is_premium`, `is_delete`)',
            'key `findByJoinedTelegramId` (`user_telegram_id`, `has_joined_channel`, `is_delete`)',
            'key `findByCheckedUsernameTelegramId` (`user_telegram_id`, `is_username_checked`, `is_delete`)',
            'key `findByUsername` (`username`, `first_last_name`, `is_delete`)',
            'key `findByVerifiedEmailAddress` (`email_address`, `is_verified`, `is_delete`)',
            'key `findByAccountType` (`account_type`, `is_delete`)',

        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user}}');
    }
}
