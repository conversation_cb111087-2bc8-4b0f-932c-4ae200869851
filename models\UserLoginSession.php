<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_login_session".
 *
 * @property int $id
 * @property int $wallet_id
 * @property string $time
 * @property string $day_month_year
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Wallet $wallet
 * @property User $user
 */
class UserLoginSession extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_login_session';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wallet_id', 'time', 'day_month_year', 'created_at'], 'required'],
            [['wallet_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['time', 'day_month_year'], 'string', 'max' => 255],
            [['wallet_id'], 'exist', 'skipOnError' => true, 'targetClass' => Wallet::className(), 'targetAttribute' => ['wallet_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'wallet_id' => 'Wallet ID',
            'time' => 'Time',
            'day_month_year' => 'Day Month Year',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Wallet]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::className(), ['id' => 'wallet_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }
}
