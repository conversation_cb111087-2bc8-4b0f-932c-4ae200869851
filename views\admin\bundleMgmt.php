<?php

use yii\helpers\Url;
use yii\web\View;
use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Bundle Management');

$this->registerCss('
  ul {
    margin-bottom: 0;
  }
  .grid-view table tbody td {
    vertical-align: middle;
  }
  .value-bold {
    font-weight: 700;
  }
  .status-badge {
    padding: 4px 12px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.85rem;
    display: inline-block;
    background-color: #f8f9fa;
    color: #495057;
  }
');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_bundle', [
      'model'   => $model,
      'page'    => 'bundle-mgmt',
      'pjax_id' => "#pjax-bundle-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{summary}{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','ID'),
          'value' => function($model) {
            return $model->id;
          },
        ],
        [
          'label'  => Yii::t('app','USD Price'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->usd_price, 2);
          },
        ],
        [
          'label'  => Yii::t('app','Token Allocation'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->token_allocation, 0);
          },
        ],
        [
          'label'  => Yii::t('app','Total Supply'),
          'format' => 'raw',
          'value'  => function($model) {
            return number_format($model->total_supply, 0);
          },
        ],
        [
          'label'  => Yii::t('app','Rewards'),
          'format' => 'raw',
          'value'  => function($model) {
            $rewards = $model->bundleRewards;

            $display = '';

            if (empty($rewards)) {
              $display .= '<div class="text-muted">' . Yii::t('app', 'No reward available') . '</div>';
            } else {
              $display .= '<div class="list-group">';
              foreach ($rewards as $reward) {
                $display .= '<div class="list-group-item">' . 'x'.number_format($reward->amount, 0).' '.ucwords(
                str_replace('_', ' ', $reward->type)). '</div>';
              }
              $display .= '</div>';
            }

            return $display;
          },
        ],
        [
          'label'  => Yii::t('app','Status'),
          'format' => 'raw',
          'value'  => function($model) {
            $status = strtolower($model->status);
            $emoji = '';
            switch($status) {
              case 'live':
                $emoji = '✅';
                break;
              case 'fulfilled':
                $emoji = '🏁';
                break;
              case 'queuing':
                $emoji = '⏳';
                break;
              default:
                $emoji = '📋';
            }
            return $emoji . ' ' . ucfirst($model->status);
          },
        ],
        // [
        //   'class'    => 'yii\grid\ActionColumn',
        //   'template' => '
        //     <div class="btn-group">
        //       <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
        //         Action <span class="dropdown-icon"></span>
        //         <span class="sr-only">Toggle Dropdown</span>
        //       </button>
        //       <div class="dropdown-menu" role="menu">
        //         {price}
        //       </div>
        //     </div>
        //   ',
        //   'buttons'  => [
        //     'price' => function ($url, $model) {
        //       return Html::a(Yii::t('app','Manage Price'), ['manage-bundle-price', 'id' => $model->id], [
        //         'class' => 'dropdown-item',
        //       ]);
        //     },
        //   ],
        // ]
      ]
    ]); ?>
  </div>
</div>
