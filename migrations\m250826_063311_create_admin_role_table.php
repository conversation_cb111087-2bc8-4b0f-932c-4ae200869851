<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%admin_role}}`.
 */
class m250826_063311_create_admin_role_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%admin_role}}', [
            "name"        => "varchar(100)",
            "label"       => "varchar(100)",
            "description" => "varchar(255)",
            'created_at'  => $this->bigInteger()->notNull(),
            'updated_at'  => $this->bigInteger(),
            'is_delete'   => 'tinyint(1) DEFAULT 0 NOT NULL',
            "primary key (`name`)",
        ], "engine = InnoDB default character set = utf8, default collate = utf8_general_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%admin_role}}');
    }
}
