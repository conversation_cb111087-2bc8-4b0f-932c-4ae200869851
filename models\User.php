<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user".
 *
 * @property int $id
 * @property string|null $user_telegram_id
 * @property int $is_premium
 * @property string|null $username
 * @property string $first_last_name
 * @property string|null $profile_pic
 * @property string|null $email_address
 * @property string|null $password
 * @property string|null $jwt_token
 * @property int|null $temp_referral_user_id
 * @property int $has_joined_channel
 * @property int $is_username_checked
 * @property string $account_type
 * @property string|null $current_referral_ranking
 * @property int|null $accumulate_login
 * @property int|null $continuous_login
 * @property int $is_verified
 * @property int|null $is_aleko_verified
 * @property string|null $verification_token
 * @property int|null $verification_token_expired_date
 * @property string|null $user_x_id
 * @property string|null $user_instagram_id
 * @property string|null $wallet_address
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Credit[] $credits
 * @property UserInviteReward[] $userInviteRewards
 * @property UserLoginSession[] $userLoginSessions
 * @property UserNoticeReward[] $userNoticeRewards
 * @property UserReferral[] $userReferrals
 * @property UserReferralReward[] $userReferralRewards
 * @property UserSocialTask[] $userSocialTasks
 * @property Wallet[] $wallets
 * @property WalletTransaction[] $walletTransactions
 */
class User extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['is_premium', 'temp_referral_user_id', 'has_joined_channel', 'is_username_checked', 'accumulate_login', 'continuous_login', 'is_verified', 'is_aleko_verified', 'verification_token_expired_date', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['first_last_name', 'created_at'], 'required'],
            [['profile_pic', 'password', 'jwt_token', 'verification_token', 'user_x_id', 'user_instagram_id', 'wallet_address'], 'string'],
            [['user_telegram_id', 'username', 'first_last_name', 'email_address', 'account_type', 'current_referral_ranking'], 'string', 'max' => 255],
            [['user_telegram_id'], 'unique'],
            [['email_address'], 'unique'],
            [['wallet_address'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_telegram_id' => 'User Telegram ID',
            'is_premium' => 'Is Premium',
            'username' => 'Username',
            'first_last_name' => 'First Last Name',
            'profile_pic' => 'Profile Pic',
            'email_address' => 'Email Address',
            'password' => 'Password',
            'jwt_token' => 'Jwt Token',
            'temp_referral_user_id' => 'Temp Referral User ID',
            'has_joined_channel' => 'Has Joined Channel',
            'is_username_checked' => 'Is Username Checked',
            'account_type' => 'Account Type',
            'current_referral_ranking' => 'Current Referral Ranking',
            'accumulate_login' => 'Accumulate Login',
            'continuous_login' => 'Continuous Login',
            'is_verified' => 'Is Verified',
            'is_aleko_verified' => 'Is Aleko Verified',
            'verification_token' => 'Verification Token',
            'verification_token_expired_date' => 'Verification Token Expired Date',
            'user_x_id' => 'User X ID',
            'user_instagram_id' => 'User Instagram ID',
            'wallet_address' => 'Wallet Address',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Credits]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCredits()
    {
        return $this->hasMany(Credit::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserInviteRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserInviteRewards()
    {
        return $this->hasMany(UserInviteReward::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserLoginSessions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserLoginSessions()
    {
        return $this->hasMany(UserLoginSession::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserNoticeRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserNoticeRewards()
    {
        return $this->hasMany(UserNoticeReward::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserReferrals]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserReferrals()
    {
        return $this->hasMany(UserReferral::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserReferralRewards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserReferralRewards()
    {
        return $this->hasMany(UserReferralReward::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[UserSocialTasks]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSocialTasks()
    {
        return $this->hasMany(UserSocialTask::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[Wallets]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::className(), ['user_id' => 'id']);
    }

    /**
     * Gets query for [[WalletTransactions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWalletTransactions()
    {
        return $this->hasMany(WalletTransaction::className(), ['user_id' => 'id']);
    }
}
