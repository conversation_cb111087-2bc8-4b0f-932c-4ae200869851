<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%notice_lookup}}`.
 */
class m250826_080135_create_notice_lookup_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%notice_lookup}}', [
            'id' => $this->primaryKey(),
            'notice_label' => $this->string()->notNull(),
            'notice_description' => $this->string(),
            'icon_image_url' => $this->text()->notNull(),
            'alex_reward_amount' => $this->integer(),
            'boost_reward_amount' => $this->integer(),
            'is_reward' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%notice_lookup}}');
    }
}
