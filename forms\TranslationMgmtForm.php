<?php

namespace app\forms;

use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\FileHelper;
use app\models\Language;
use app\models\TranslationLookup;

class TranslationMgmtForm extends AttributeChangeForm
{
  public $translation;
  public $id;
  public $en_us;

  public function rules()
  {
    return [
      [['en_us', 'translation'], 'safe'],
    ];
  }

  public function getQuery()
  {
    $query = TranslationLookup::find();
    if (!empty($this->en_us)) {
      $query->andFilterWhere(['like', 'en_us', "%" . $this->en_us . "%", false]);
    }
    $query->andWhere(['is_delete' => 0]);

    return $query;
  }

  public function search()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery()
    ]);

    return $dataProvider;
  }

  public function init()
  {
    parent::init();

    $lang_list = Language::getAvailableLanguage();

    $model = TranslationLookup::findOne([
      'id'        => $this->id,
      'is_delete' => 0,
    ]);

    if (!empty($model)) {
      foreach ($lang_list as $la) {
        $code = str_replace('-', '_', strtolower($la->code));
        $this->translation[$code] = $model->$code;
      }
    }

    $this->initAttributes();
  }

  public function save()
  {
    if (!$this->validate()) {
      return false;
    }

    $attributes = $this->getChangedAttributes();

    foreach ($attributes as $attr => $change) {
      foreach ($change as $data) {
        $model = TranslationLookup::findOne([
          'id'        => $this->id,
          'is_delete' => 0,
        ]);
        $lang_change = $data['change_type']; // general naming
        $model->$lang_change = $data['new'];
        $model->update(false, [$lang_change]);
      }
    }

    return true;
  }

  public function publishTranslation()
  {
    $lang_list = Language::findAll(['is_delete' => 0]);

    if (empty($lang_list)) {
      throw new \Exception('No language found');
    }

    foreach ($lang_list as $_lang) {
      if (Yii::$app->params['translation_mode'] == 'local') {
        TranslationLookup::exportLanguageToPath($_lang->code);
      } else if (Yii::$app->params['translation_mode'] == 'api') {
        $this->exportLangWithApi($_lang->code);
      } else {
        $this->exportLangToS3($_lang->code);
      }
    }

    return true;
  }

  public function exportLangWithApi($_lang)
  {
    $lang = Language::findOne(['code' => $_lang, 'is_delete' => 0]);

    if (empty($lang)) {
      throw new \Exception('Invalid language code');
    }

    $lang_array = [];
    $lower_case_key = str_replace("-","_", strtolower($selected_lang));
    $model = TranslationLookup::find();
    $model->andWhere(['not', ['message_key' => null]]);
    $model->andWhere(['is_delete' => 0]);
    $model = $model->all();

    foreach ($model as $m) {
      $lang_array[$m->message_key] = $m->{$lower_case_key};
    }

    $final_json = json_encode($lang_array);
    $url        = Yii::$app->params['api']["translationWrite"];

    $data = [
      'lang'      => $selected_lang,
      'transJson' => $final_json,
    ];

    $client  = new Client();
    $request = $client->createRequest();
    $request->setFormat(Client::FORMAT_JSON);
    $request->setUrl($url);
    $request->setData($data);
    $request->setMethod('POST');
    $request->addHeaders([
      "AUTHORIZATION" => "Bearer ".Yii::$app->params['translation_bearer'],
    ]);
    $request->setOptions([
      'timeout' => 30
    ]);

    $response = $client->send($request);
    $result   = $response->data;

    if ($result['error']) {
      throw new \Exception($result['message'],1000);
    }

    if ($response->isOk) {
      return true;
    }
  }

  public function exportLangToS3($_lang)
  {
    $lang = Language::findOne(['code' => $_lang, 'is_delete' => 0]);

    if (empty($lang)) {
      throw new \Exception('Invalid language code');
    }

    $lang_array = [];
    $lower_case_key = str_replace("-","_", strtolower($_lang));
    $model = TranslationLookup::find();
    $model->andWhere(['not', ['message_key' => null]]);
    $model->andWhere(['is_delete' => 0]);
    $model = $model->all();

    foreach ($model as $m) {
      $lang_array[$m->message_key] = $m->{$lower_case_key};
    }

    if (empty($lang_array)) {
      throw new \Exception('No translation found');
    }

    $final_json = json_encode($lang_array);

    $path = Yii::getAlias('@app/runtime/translation');

    if (!file_exists($path)) {
      FileHelper::createDirectory($path, $mode = 0777);
    }

    $local_path = Yii::getAlias("@app/runtime/translation/{$_lang}.json");
    file_put_contents($local_path, $final_json);

    $s3file = Yii::getAlias("@s3-translation/{$_lang}.json");
    $type   = 'application/json';

    $aws = Yii::$app->aws->client();
    $s3  = $aws->createS3();

    $result = $s3->putObject([
      'ACL'         => 'public-read',
      'Bucket'      => Yii::$app->params['s3_bucket'],
      'ContentType' => $type,
      'SourceFile'  => $local_path,
      'Key'         => $s3file,
    ]);

    $filepath = $result->get('ObjectURL');

    unlink($local_path);
    gc_collect_cycles();

    return $filepath;
  }
}
