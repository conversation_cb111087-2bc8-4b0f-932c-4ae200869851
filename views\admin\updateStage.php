<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use yii\web\View;
use yii\widgets\Breadcrumbs;
use yii\widgets\Pjax;
use kartik\datetime\DateTimePicker;

$this->title = Yii::t('app', 'Update Stage');

$main_page_url = 'stage-mgmt';

$this->registerCss('
  .datetimepicker > .datetimepicker-minutes {
    overflow: auto !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 300px;
  }
')

?>

<div class="card card-default">
  <?= Breadcrumbs::widget([
      'itemTemplate' => "<li> <i> {link} / </i> </li>\n",
      'links' => [
        ['label' => 'Stage Management', 'url' => [$main_page_url]],
        $this->title,
      ],
    ]);
  ?>

  <?php $form = ActiveForm::begin([
      'id'     => 'your-form',
      'method' => 'post',
      'options' => [
        'data-pjax' => true,
      ],
      'fieldConfig' => [
        'inputOptions' => [
          'class' => 'input-sm form-control',
        ],
      ],
    ]);
  ?>
  <div class="card-body">
    <?= $this->render('/site/_alert_flash', []) ?>
    <div class="row">
      <div class="col-md-6">
        <?= $form->field($model, 'name')->textInput(['disabled' => true])->label(Yii::t('app', 'Name')) ?>
      </div>
      <div class="col-md-6">
        <?= $form->field($model, 'price_per_token')->textInput([
            'style' => 'width:100%',
            'type'  => 'number',
            'step'  => '0.01',
            'disabled' => true,
          ])->label(Yii::t('app', 'Price Per Token'))
        ?>
      </div>
      <div class="col-md-6">
        <?= $form->field($model, 'token_available')->textInput([
            'style' => 'width:100%',
            'type'  => 'number',
            'step'  => '1',
            'min'   => '1',
            'disabled' => true,
          ])->label(Yii::t('app', 'Token Available'))
        ?>
      </div>
      <div class="col-md-6">
        <?= $form->field($model, 'total_fund')->textInput([
            'style' => 'width:100%',
            'type'  => 'number',
            'step'  => '1',
            'min'   => '1',
            'disabled' => true,
          ])->label(Yii::t('app', 'Total Fund'))
        ?>
      </div>
      <div class="col-md-6">
        <?= $form->field($model, 'should_end_date')->widget(DateTimePicker::classname(), [
          'attribute'     => 'should_end_date',
          'removeButton'  => false,
          'pickerButton'  => ['icon' => 'time'],
          'pluginOptions' => [
            'timePicker' => true,
            'format'     => 'yyyy-mm-dd hh:ii:59',
            'autoclose'  => true,
            'minuteStep' => 1,
            'showSeconds' => false,
          ],
          'options' => ['placeholder' => 'Enter stage end date ...'],
        ])->label(Yii::t('app', 'Stage End Date (GMT + 8)')) ?>
      </div>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::a(Yii::t('app', 'Back'), Url::to($main_page_url), ['class' => 'btn btn-default']); ?>
    <?= Html::submitButton(Yii::t('app', 'Update'), [
      'class' => 'btn btn-success float-right',
      'name'  => 'Update',
      'value' => 1,
    ]) ?>
  </div>
  <?php ActiveForm::end(); ?>
</div>
