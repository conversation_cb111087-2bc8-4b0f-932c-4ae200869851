<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "admin".
 *
 * @property int $id
 * @property string|null $username
 * @property string|null $email
 * @property string|null $otp_code
 * @property int|null $valid_time
 * @property string|null $password
 * @property string|null $password_key
 * @property string|null $access_token
 * @property string|null $role
 * @property int|null $is_suspend
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property AdminLoginSession[] $adminLoginSessions
 * @property AuditLog[] $auditLogs
 */
class Admin extends \yii\db\ActiveRecord implements \yii\web\IdentityInterface
{
    const JWT_USERID = 'user_id';
    const JWT_TOKEN  = 'access_token';

    public $authKey;

    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::className(),
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new \yii\db\Expression('UNIX_TIMESTAMP()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['username', 'email', 'otp_code', 'valid_time', 'password', 'password_key', 'access_token', 'role', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['valid_time', 'is_suspend', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['created_at'], 'required'],
            [['username', 'email', 'otp_code', 'password', 'password_key', 'access_token'], 'string', 'max' => 255],
            [['role'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'username' => 'Username',
            'email' => 'Email',
            'otp_code' => 'Otp Code',
            'valid_time' => 'Valid Time',
            'password' => 'Password',
            'password_key' => 'Password Key',
            'access_token' => 'Access Token',
            'role' => 'Role',
            'is_suspend' => 'Is Suspend',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Finds an identity by the given ID.
     *
     * @param int|string $id the ID to be looked for
     * 
     * @return \app\models\Admin the identity object that matches the given ID.
     * Null if such an identity cannot be found
     */
    public static function findIdentity($id)
    {
        $user = self::findOne(['id' => $id, 'is_delete' => 0]);

        if (empty($user))
            return null;

        return $user;
    }

    /**
     * Finds user by email.
     *
     * @param string $email the email to be looked for
     * 
     * @return \app\models\Admin the user object that matches the given email.
     * Null if such a user cannot be found
     */
    public static function findByUsername($email)
    {
        $query = self::find();
        $query->andWhere(['email' => $email]);
        $query->andWhere(['<>', 'role', 'user']);
        $query->andWhere(['is_delete' => 0]);

        $user = $query->one();

        if (!$user) {
            return null;
        }

        return $user;
    }

    /**
     * Finds an identity by the given access token.
     * 
     * @param string $token the access token to be looked for
     * @param string $type the type of the token. The value of this parameter depends on the implementation.
     * 
     * @return \app\models\Admin the identity object that matches the given access token.
     * Null if such an identity cannot be found.
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        if (empty($token)) {
            return null;
        }

        try {
            self::validateJwtToken($token);
            $resp = self::verifyJwtToken($token);
            if (!isset($resp[self::JWT_USERID]) || !isset($resp[self::JWT_TOKEN])) {
                return null;
            }
            $user = self::findIdentity($resp[self::JWT_USERID]);
            if (empty($user) || $user->access_token != $resp[self::JWT_TOKEN]) {
                return null;
            }
            return $user;
        } catch (\Exception $e) {
            return null;
        }
    }

    
    /**
     * Returns the ID of the user.
     * @return string the ID of the user.
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string the authentication key.
     */
    public function getAuthKey()
    {
        return $this->authKey;
    }


    /**
     * Validates the given auth token.
     *
     * @param string $authKey the given auth token
     * @return bool whether the token is valid
     */
    public function validateAuthKey($authKey)
    {
        return $this->authKey === $authKey;
    }

    /**
     * Validate the given JWT token.
     *
     * @param string $token the JWT token.
     *
     * @throws \Exception if the token is invalid.
     *
     * @return bool true if the token is valid.
     */
    private static function validateJwtToken($token)
    {
        $validate = new \Lcobucci\JWT\ValidationData();
        $parse = (new \Lcobucci\JWT\Parser())->parse($token);

        if (!$parse->validate($validate))
            throw new \Exception(Yii::$app->error->message(102), 102);
        return true;
    }

    /**
     * Verify the given JWT token.
     * 
     * lcobucci version used is 3.3.3
     *
     * @param string $token the JWT token.
     *
     * @throws \Exception if the token is invalid.
     *
     * @return array the payload of the token.
     */
    private static function verifyJwtToken($token)
    {
        $signer = new \Lcobucci\JWT\Signer\Hmac\Sha256(); // Create a new instance of the Hmac\Sha256 signer
        $parse = (new \Lcobucci\JWT\Parser())->parse($token); // Create an instance of \Lcobucci\JWT\Token

        if (!$parse->verify($signer, Yii::$app->params['jwt_secret'])) {
            throw new \Exception(Yii::$app->error->message(102), 102);
        }

        $resp = [];
        // returns an array of all claims as \Lcobucci\JWT\Claim objects from the \Lcobucci\JWT\Token. 
        $data  = $parse->getClaims();
        foreach ($data as $key => $data)
            // get the values based on the each key
            $resp[$key] = $data->getValue();
        return $resp;
    }

    /**
     * Validates the given password.
     *
     * @param string $password the password to be validated.
     *
     * @return boolean whether the password is valid.
     */
    public function validatePassword($password)
    {
        $raw = self::genPassWithKey($password, $this->password_key);
        $hash = self::hashPassword($password, $this->password_key);

        if (password_verify($raw, $this->password)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Validates the given OTP code.
     *
     * @param string $otp the OTP code to be validated.
     *
     * @return boolean whether the OTP code is valid.
     */
    public function validateOtp($otp)
    {
        $expire_time = $this->valid_time;
        $today_time  = strtotime("now");

        if ($expire_time < $today_time) {
            return false;
        }

        if ($this->otp_code != $otp) {
            return false;
        }

        return true;
    }

    /**
     * Generate a password key.
     *
     * @return string the password key.
     */
    public static function genPassKey()
    {
        return crypt(md5(date('YmdHis')), uniqid());
    }


    /**
     * Generates a string from given password and key.
     *
     * @param string $password the password to be hashed.
     * @param string $key the key used to hash the password.
     *
     * @return string the generated string.
     */
    public static function genPassWithKey($password, $key)
    {
        return $key . md5($password . $key) . $key;
    }

    /**
     * Hashes a password.
     *
     * This method takes a password and a key to generate a string that is then hashed.
     * The string is generated by concatenating the key, md5 of the password and key,
     * and the key again. This method is used to hash the password.
     *
     * @param string $password the password to be hashed.
     * @param string $key the key used to hash the password.
     *
     * @return string the hashed password.
     */
    public static function hashPassword($password, $key)
    {
        $string = self::genPassWithKey($password, $key);
        return password_hash($string, PASSWORD_DEFAULT);
    }

    /**
     * Create a new admin.
     *
     * @param array $params the parameters to use when creating the admin.
     * The parameters should contain the following keys:
     * - username: the username of the admin.
     * - email: the email of the admin.
     * - role: the role of the admin.
     * - password: the password of the admin.
     *
     * @return Admin the newly created admin.
     *
     * @throws \Exception if the admin could not be created.
     */
    public function createAdmin($params)
    {
        $attribute = [
            'username' => null,
            'email'    => null,
            'role'     => null,
            'password' => null,
        ];

        foreach ($attribute as $key => $value) {
            if (isset($params[$key])) {
                $attribute[$key] = $params[$key];
            }
        }

        $pass_key = self::genPassKey();

        $model               = new Admin;
        $model->username     = $attribute['username'];
        $model->email        = $attribute['email'];
        $model->password     = self::hashPassword($attribute['password'], $pass_key);
        $model->password_key = $pass_key;
        $model->role         = $attribute['role'];
        $model->created_at   = time();

        if (!$model->save()) {
            throw new \Exception(current($model->getFirstErrors()));
        }

        return $model;
    }

}
