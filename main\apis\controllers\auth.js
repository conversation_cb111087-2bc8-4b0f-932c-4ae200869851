import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

router.get("/tg/direct-link", validator.exp_validator(validator.items_validator.authValidator.tg_direct_link_validator), async (req, res) => {
    const { search_params } = req.query;
    const transaction = await query.sequelize.transaction();
    try {
        const init_data = new URLSearchParams(search_params);
        const hash = init_data.get("hash");
        const auth_date_exp = init_data.get('auth_date');
        if (!auth_date_exp) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Invalid Login. Please try again.",
                "error": true
            });
        }
        const auth_timestamp = parseInt(auth_date_exp, 10)
        const current_timestamp = common.moment().utc().unix()
        const time_difference = current_timestamp - auth_timestamp;
        const five_mins_in_seconds = 7 * 24 * 60 * 60;  //temp week in seconds
        if (time_difference > five_mins_in_seconds) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Expired login session. Please try again.",
                "error": true
            });
        }
        init_data.delete("hash");
        const data_check_string = Array.from(init_data.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}=${value}`)
            .join("\n");
        const secret_key = common.crypto
            .createHmac("sha256", "WebAppData")
            .update(common.config.telegram.token)
            .digest();
        const calculated_hash = common.crypto
            .createHmac("sha256", secret_key)
            .update(data_check_string)
            .digest("hex");
        if (hash !== calculated_hash) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Invalid Login. Please try again.",
                "error": true
            });
        }
        const input_string = data_check_string;
        const key_value_pairs = input_string.split('\n');
        const user_pair = key_value_pairs.find(pair => pair.startsWith('user='));
        const user_json_string = user_pair.replace('user=', '');
        const user_data_tele = JSON.parse(user_json_string);

        const user = await helper.user_helper.getOrCreateUserByTelegramID(user_data_tele.id.toString(), transaction)
        if (!user) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": 'User not found. Please try again.',
                "error": true
            });
        }

        //Signed JWT
        const signed_jwt = helper.auth_helper.signJWT(user, null, null, "LOGIN")
        await user.update(
            { jwt_token: signed_jwt, updated_at: common.util_helper.getCurrentEpochTime(), },
            { where: { id: user.id, }, transaction: transaction, },
        );

        await transaction.commit();
        return res.status(200).json({
            "data": { "jwt_token": signed_jwt },
            "status": 200,
            "msg": "Account login successfully",
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": common.util_helper.handleErrorMessageAPI("Unknown user", error),
            "error": true
        });
    }
})

router.get("/session-check", helper.auth_helper.authenticateJWT, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const token = helper.auth_helper.getBearerToken(req);
    const transaction = await query.sequelize.transaction();
    try {
        const find_user_token_db = await query.models.user.findOne({ where: { id: user_id, is_delete: 0 } });
        if (!find_user_token_db) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'User not found.',
                error: true
            });
        }
        if (token !== find_user_token_db.jwt_token) {
            await transaction.rollback();
            return res.status(400).json({
                data: {},
                status: 400,
                msg: 'Invalid Token. Please login again.',
                error: true
            });
        }
        await transaction.commit()
        return res.status(200).json({
            data: { token: find_user_token_db.jwt_token },
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(user_id, error),
            error: true
        });
    }
})

// router.post("/bind-wallet",
//     helper.auth_helper.authenticateJWT,
//     validator.exp_validator(validator.items_validator.authValidator.wallet_validator),
//     helper.auth_helper.checkIsTeleAccount,
//     async (req, res) => {
//         const { id: user_id } = req.user;
//         const { wallet_address } = req.body;
//         const transaction = await query.sequelize.transaction();
//         const current_time = common.util_helper.getCurrentEpochTime();
//         try {
//             let absolute_new_joiner = false;
//             const converted_wallet_address = helper.scanner_helper.hexToTonAddressConverted(wallet_address)
//             await transaction.commit();
//             return res.status(200).json({
//                 // data: { jwt_token: new_token, new_joiner: absolute_new_joiner, is_playable: is_playable },
//                 status: 200,
//                 msg: "OK",
//                 error: false
//             });
//         } catch (error) {
//             await transaction.rollback();
//             return res.status(400).json({
//                 data: {},
//                 status: 400,
//                 msg: await handleErrorMessageAPI(user_id, error),
//                 error: true
//             });
//         }
//     }
// )

export default router;