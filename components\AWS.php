<?php

namespace app\components;

use Yii;
use yii\base\Component;
use Aws\Sdk;
use Aws\S3\S3Client; 

class AWS extends Component
{
	public $key;
	public $secret;
	public $region;

	protected $_client;

	/**
	 * Get the AWS client.
	 * Refer to web.php component > aws for configuration.
	 *
	 * @return Sdk
	 */
	public function client()
	{
		if (isset($this->_client))
			return $this->_client;
		return $this->_client = new Sdk([
			'region' => $this->region,
			'version' => 'latest',
			'suppress_php_deprecation_warning' => true,
			'credentials' => [
				'key' => $this->key,
				'secret' => $this->secret,
			],
		]);
	}

	/**
	 * Return the full path to the given file in the S3 bucket.
	 * 
	 * @param string $filename
	 * 
	 * @return string
	 */
	public function getFilePath($filename)
	{
		return Yii::getAlias("@s3/{$filename}");
	}
}
