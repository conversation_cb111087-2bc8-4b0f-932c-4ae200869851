<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_notice_reward}}`.
 */
class m250826_080409_create_user_notice_reward_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_notice_reward}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'notice_id' => $this->integer()->notNull(),
            'wallet_id' => $this->integer(),
            'is_claim' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete' => 'tinyint(1) DEFAULT 0 NOT NULL',
            'foreign key (`user_id`) references `user` (`id`)',
            'foreign key (`notice_id`) references `notice_lookup` (`id`)',
            'foreign key (`wallet_id`) references `wallet` (`id`)',
            'key `findAll` (`is_delete`)',
            'key `findByUserId` (`user_id`, `is_delete`)',
            'key `findByNoticeId` (`notice_id`, `is_delete`)',
            'key `findByWalletId` (`wallet_id`, `is_delete`)',
            'key `findByUserIsClaim` (`user_id`, `is_claim`, `is_delete`)'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_notice_reward}}');
    }
}
