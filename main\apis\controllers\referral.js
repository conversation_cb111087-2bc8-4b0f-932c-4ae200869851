import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    try {
        const get_user_data = await query.models.user.findOne({
            include: [
                {
                    model: query.models.user_referral,
                    required: false,
                },
                {
                    model: query.models.user_referral_reward,
                    required: false,
                }
            ],
            where: {
                id: user_id,
                is_delete: false
            },
            transaction: transaction,
            nest: true
        })
        const tele_link = `
${common.config.telegram.bot_link}?start=ref-${get_user_data.user_referral.referral_code}
%0A%0A🎮Join me in Aleko GameFi, a rhythm-fueled SocialFi-powered Web3 world where your taps, missions, and community participation turn into real rewards!
%0A%0A💰Earn 1,000 Aleko Points as a new player
%0A%0A🎵Complete missions, invite friends, and climb the leaderboard for NFT rewards and airdrops — where gaming meets community growth!  
        `;

        const tele_link_no_format = `
${common.config.telegram.bot_link}?start=ref-${get_user_data.user_referral.referral_code}

🎮Join me in Aleko GameFi, a rhythm-fueled SocialFi-powered Web3 world where your taps, missions, and community participation turn into real rewards!

💰Earn 1,000 Aleko Points as a new player

🎵Complete missions, invite friends, and climb the leaderboard for NFT rewards and airdrops — where gaming meets community growth!  
`;

        const find_referral_rewards = await query.models.referral_reward_lookup.findAll({
            where: {
                is_delete: false
            },
            transaction: transaction
        })

        let formatted_referral_reward_list = [];
        find_referral_rewards.forEach(element => {
            const find_is_claim = get_user_data.user_referral_rewards.find(t => t.referral_reward_id === element.id);
            // 4. Use the concise ternary operator for status
            const claim_status = find_is_claim
                ? (find_is_claim.is_claim ? "claimed" : "claimable")
                : "go_complete";
            const data = {
                id: element.id,
                invite_count: element.invite_tier_count,
                label: `Invite ${element.invite_tier_count} friend`,
                reward_amount: element.reward_amount,
                reward_type: element.reward_type,
                tier_special_title: element.special_tier_title,
                status: claim_status
            }
            formatted_referral_reward_list.push(data)
        });

        const data = {
            referral_code: get_user_data.user_referral.referral_code,
            invitation_link: tele_link,
            invitation_link_no_format: tele_link_no_format,
            reward_list: formatted_referral_reward_list
        }

        await transaction.commit();
        return res.status(200).json({
            "data": data,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": await common.util_helper.handleErrorMessageAPI(user_id, error),
            "error": true
        });
    }
})

router.post("/:referral_reward_id", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    const referral_reward_id = req.params.referral_reward_id;

    try {
        let find_referral_reward = await query.models.referral_reward_lookup.findOne({ where: { id: referral_reward_id, is_delete: 0 }, transaction: transaction })
        if (!find_referral_reward) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Referral Reward not found.",
                "error": true
            });
        }

        let find_user_referral_reward = await query.models.user_referral_reward.findOne({
            where: {
                user_id: user_id,
                referral_reward_id: referral_reward_id,
                is_delete: false
            },
            transaction: transaction,
        })

        if (!find_user_referral_reward) {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Referral Reward Tier is not unlock yet.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        } else if (find_user_referral_reward.is_claim === 1) {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Referral Reward claim already completed.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        } else {
            await find_user_referral_reward.update({
                is_claim: 1,
                updated_at: common.util_helper.getCurrentEpochTime()
            }, {
                lock: transaction.LOCK.UPDATE,
                transaction: transaction,
            })
        }

        const wallet_transaction_alex = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            find_referral_reward.reward_type,
            find_referral_reward.reward_amount,
            common.enum_key.WALLET_TRANSACTION_TYPE.IN,
            null,
            "referral_reward_claim",
            transaction
        );

        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id} --> wallet_id: ${wallet_id} --> wallet_address: ${wallet_address}`, error),
            error: true
        });
    }
})

export default router;