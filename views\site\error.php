<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Breadcrumbs;

$this->title = $name;

$main_page_url = 'site/login';

$this->registerCss("
    p {
        margin-bottom: 0px;
    }

    .error-page {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }

    .error-content {
        line-height: normal;
        display: inline-block !important;
        vertical-align: middle;
        margin-left: 0px !important;
    }
");

?>

<div class="site-error">
    <section class="content">
        <div class="error-page">
            <h2 class="headline text-warning"> 404</h2>
            <div class="error-content">
                <h3><i class="fas fa-exclamation-triangle text-warning"></i> Oops! Page not found.</h3>
                <p>
                    We could not find the page you were looking for.
                    Meanwhile, you may return to <a href="<?= Url::to([$main_page_url]) ?>"> HOME </a> page.
                </p>
            </div>
        </div>
    </section>

</div>