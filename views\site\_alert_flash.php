<?php
	$base_url = Yii::$app->request->baseUrl;
 ?>
<div class="">
	<?php if (Yii::$app->session->hasFlash('success')): ?>
		<div class="alert alert-success"><?= Yii::t('app', Yii::$app->session->getFlash('success')) ?>
			<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
		</div>
	<?php endif; ?>

	<?php if (Yii::$app->session->hasFlash('error')): ?>
		<div class="alert alert-danger" role="alert"><?= Yii::t('app', Yii::$app->session->getFlash('error')) ?>
			<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
		</div>
	<?php endif; ?>

	<?php if (Yii::$app->session->hasFlash('warning')): ?>
		<div class="alert alert-warning">
			<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
			<?= Yii::t('app', Yii::$app->session->getFlash('warning')) ?>
		</div>
	<?php endif; ?>

	<?php if (Yii::$app->session->hasFlash('warning-table')): ?>
		<div class="alert" style="height: 200px;overflow: auto;border: 1px solid #49505745;padding: 12px;margin-bottom: 20px">
			<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
			<h5 style="color: red"><?= Yii::t('app', 'Failed import list') ?> :</h5>
			<table class="table table-bordered">
				<thead>
					<tr>
						<th><?= Yii::t('app', 'Row') ?></th>
						<th><?= Yii::t('app', 'Reason') ?></th>
					</tr>
				</thead>
				<tbody>
					<?php foreach (Yii::$app->session->getFlash('warning-table') as $key => $value): ?>
						<tr>
							<td><?= $key ?></td>
							<td><?= $value ?></td>
						</tr>
					<?php endforeach; ?>
				</tbody>
			</table>
		</div>
	<?php endif; ?>

	<?php if (Yii::$app->session->hasFlash('info')): ?>
		<div class="alert alert-info"><?= Yii::t('app', Yii::$app->session->getFlash('info')) ?>
			<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
		</div>
	<?php endif; ?>
</div>
