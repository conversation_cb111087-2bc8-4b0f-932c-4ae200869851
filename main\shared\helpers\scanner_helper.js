import axios from "axios";
import Ton<PERSON><PERSON> from "tonweb"
import { Address } from '@ton/ton';
import common from "../imports/common.js";
import config from "../../../config.json" assert { type: "json" };


const API = `${config.ton.rpc}api/v2`;

function hexToTonAddressConverted(hexAddress) {
    const address = new TonWeb.utils.Address(hexAddress);
    const bouncable_address = common.config.ton.is_mainnet ? address.toString(true, true, true, false) : address.toString(true, true, true, true);
    const unbouncable_address = common.config.ton.is_mainnet ? address.toString(true, true, false, false) : address.toString(true, true, false, true);
    return {
        hex: address.toString(false),
        bounceable: bouncable_address,
        non_bounceable: unbouncable_address,
    }
}

/**
 * Normalize TON address for API calls
 * @param {string|Address} address - Address in any format
 * @returns {string} Normalized address for API
 */
function normalizeAddress(address) {
    // Validate input
    if (!address) {
        throw new Error('Address is required');
    }

    // Handle Address object from @ton/ton
    if (address instanceof Address) {
        const isTestnet = address.workChain === -1;
        return address.toString({
            bounceable: true,
            testOnly: isTestnet
        });
    }

    // Handle object that might have toString or address property
    if (typeof address === 'object') {
        console.error('Received object instead of string:', address);

        // Try common properties
        if (address.address && typeof address.address === 'string') {
            address = address.address;
        } else if (address.toString && typeof address.toString === 'function') {
            address = address.toString();
        } else {
            throw new Error(`Invalid address type: received object ${JSON.stringify(address)}`);
        }
    }

    if (typeof address !== 'string') {
        throw new Error(`Address must be a string or Address object, got ${typeof address}`);
    }

    const trimmedAddress = address.trim();

    if (!trimmedAddress) {
        throw new Error('Address cannot be empty');
    }

    try {
        // Parse and normalize using TON library
        const parsedAddress = Address.parse(trimmedAddress);

        // Determine if this is a testnet address
        // kQ = testnet non-bounceable, kE = testnet bounceable
        // UQ = mainnet non-bounceable, EQ = mainnet bounceable
        const isTestnet = trimmedAddress.match(/^k[QE]/i) !== null;

        // Return address in friendly bounceable format
        const normalized = parsedAddress.toString({
            bounceable: true,
            testOnly: isTestnet
        });

        return normalized;
    } catch (error) {
        throw new Error(`Failed to parse address "${trimmedAddress}": ${error.message}`);
    }
}

/**
 * Get transactions from TonCenter API with pagination and timestamp filter
 * @param {string} contract_address - Contract address
 * @param {string|BigInt|null} to_lt - Logical time for pagination (optional)
 * @param {number|null} targetTimestamp - Unix timestamp to filter transactions (optional)
 * @param {Object} options - Configuration options
 * @param {number} options.limit - Transactions per page (default: 100)
 * @param {boolean} options.archival - Use archival nodes (default: true)
 * @param {number} options.maxRetries - Max retry attempts (default: 3)
 * @param {number} options.retryDelay - Base retry delay in ms (default: 2000)
 * @param {boolean} options.fetchAll - Fetch all pages automatically (default: false)
 * @param {number} options.maxTransactions - Max total transactions to fetch (default: unlimited)
 * @param {number} options.pageDelay - Delay between pages in ms (default: 500)
 * @param {number} options.timestampTolerance - Tolerance for timestamp matching in seconds (default: 60)
 * @returns {Promise<Array>} Array of transactions
 */
async function getTransactions(contract_address, to_lt = null, targetTimestamp = null, options = {}) {
    const {
        limit = 100,
        archival = true,
        maxRetries = 3,
        retryDelay = 2000,
        fetchAll = false,
        maxTransactions = Infinity,
        pageDelay = 500,
        timestampTolerance = 60
    } = options;

    const normalizedAddress = normalizeAddress(contract_address);
    console.log(`Fetching transactions for: ${normalizedAddress} (fetchAll: ${fetchAll}, targetTimestamp: ${targetTimestamp})`);

    const allTransactions = [];
    let currentLt = to_lt;
    let pageCount = 0;

    while (true) {
        let retries = 0;

        while (retries < maxRetries) {
            try {
                pageCount++;

                const params = {
                    address: normalizedAddress,
                    limit: limit,
                    archival: archival
                };

                if (currentLt) {  // String check
                    params.to_lt = currentLt;  // Direct string, no conversion
                }

                if (config.ton?.apiKey) {
                    params.api_key = config.ton.apiKey;
                }

                const response = await axios.get(`${API}/getTransactions`, {
                    params: params,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.ton?.apiKey ? { 'X-API-Key': config.ton.apiKey } : {})
                    },
                    timeout: 30000
                });

                if (response.data.error) {
                    throw new Error(response.data.error.message || response.data.error);
                }

                if (!response.data.ok) {
                    throw new Error(response.data.error || 'API error');
                }

                const rawTransactions = response.data.result || [];
                let transactions = [...rawTransactions];

                // Isolate filter processing with try-catch for type errors
                if (targetTimestamp !== null) {
                    try {
                        transactions = transactions.filter(tx => {
                            // Explicit Number conversion to handle potential BigInt
                            const txTime = Number(tx.utime || 0);
                            const targetTime = Number(targetTimestamp);
                            const diff = Math.abs(txTime - targetTime);
                            const isMatch = diff <= timestampTolerance;
                            return isMatch;
                        });
                    } catch (filterError) {
                        console.error(`Filter error (possible BigInt mix): ${filterError.message}. Skipping filter on this page.`);
                        transactions = [];  // Or rawTransactions if you want unfiltered
                    }
                }

                if (transactions.length === 0 && targetTimestamp !== null) {
                    // do nothing
                } else if (transactions.length > 0) {
                    allTransactions.push(...transactions);
                    console.log(`Retrieved ${transactions.length} matching txs (total: ${allTransactions.length})`);
                }

                // Isolate LT update with try-catch
                try {
                    const lastRawTx = rawTransactions[rawTransactions.length - 1];
                    if (lastRawTx && lastRawTx.transaction_id && lastRawTx.transaction_id.lt) {
                        currentLt = String(lastRawTx.transaction_id.lt);  // Keep as string, no BigInt
                    } else {
                        currentLt = null;
                    }
                } catch (ltError) {
                    console.error(`LT update error: ${ltError.message}. Continuing without update.`);
                    currentLt = null;
                }

                // Early exit on timestamp match
                if (targetTimestamp !== null && allTransactions.length > 0) {
                    console.log(`Found ${allTransactions.length} matching transactions`);
                    return allTransactions;
                }

                if (!fetchAll) {
                    return transactions;
                }

                if (allTransactions.length >= maxTransactions) {
                    return allTransactions.slice(0, maxTransactions);
                }

                if (rawTransactions.length < limit) {
                    return allTransactions;
                }

                break;

            } catch (error) {
                // Existing retry logic unchanged...
                const isRateLimit = error.response?.status === 429 || error.message?.includes('429') || error.message?.includes('Too Many Requests') || error.code === 'ECONNABORTED';
                const isNetworkError = error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.message?.includes('timeout');
                const isServerError = error.response?.status === 500;

                if ((isRateLimit || isNetworkError || isServerError) && retries < maxRetries - 1) {
                    retries++;
                    const delay = retryDelay * Math.pow(2, retries - 1);
                    console.warn(`${isRateLimit ? 'Rate limit' : isServerError ? 'Server error' : 'Network error'}, retrying in ${delay}ms (${retries}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    continue;
                }

                console.error(`Error on page ${pageCount}: ${error.message}`);
                if (allTransactions.length > 0) {
                    return allTransactions;
                }
                throw new Error(`Failed: ${error.message}`);
            }
        }

        if (retries >= maxRetries) {
            throw new Error(`Max retries exceeded on page ${pageCount}`);
        }

        if (fetchAll) {
            await new Promise(resolve => setTimeout(resolve, pageDelay));
        }
    }
}

export default {
    hexToTonAddressConverted,
    getTransactions
}