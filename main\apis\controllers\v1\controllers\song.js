import server from "../../../../shared/imports/server.js";
import common from "../../../../shared/imports/common.js";
import query from "../../../../shared/imports/query.js"
import validator from "../../../../shared/imports/validator.js"
import helper from "../../../../shared/imports/helper.js";

const router = server.express.Router();


router.get("/current", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const user_song_selected = await query.models.user_song_selected_lookup.findOne({
            where: {
                user_id,
                is_active: true,
                is_delete: false
            },
            include: [
                {
                    model: query.models.user,
                    required: true
                    // as: 'user'
                },
                {
                    model: query.models.song_lookup,
                    required: true
                    // as: 'song'
                },
                {
                    model: query.models.advance_genre_lookup,
                    required: true
                    // as: 'genre'
                },
                {
                    model: query.models.user_shop_card,
                    as: 'boost_card_one',
                    required: false,
                    include: [
                        {
                            model: query.models.instrument_lookup,
                            required: true
                            // as: 'instrument'
                        },
                        {
                            model: query.models.rarity_lookup,
                            required: true
                            // as: 'rarity'
                        }
                    ]
                },
                {
                    model: query.models.user_shop_card,
                    as: 'boost_card_two',
                    required: false,
                    include: [
                        {
                            model: query.models.instrument_lookup,
                            required: true
                            // as: 'instrument'
                        },
                        {
                            model: query.models.rarity_lookup,
                            required: true
                            // as: 'rarity'
                        }
                    ]
                },
                {
                    model: query.models.user_shop_card,
                    as: 'boost_card_three',
                    required: false,
                    include: [
                        {
                            model: query.models.instrument_lookup,
                            required: true
                            // as: 'instrument'
                        },
                        {
                            model: query.models.rarity_lookup,
                            required: true
                            // as: 'rarity'
                        }
                    ]
                },
                {
                    model: query.models.user_shop_card,
                    as: 'boost_card_four',
                    required: false,
                    include: [
                        {
                            model: query.models.instrument_lookup,
                            required: true
                            // as: 'instrument'
                        },
                        {
                            model: query.models.rarity_lookup,
                            required: true
                            // as: 'rarity'
                        }
                    ]
                },
                {
                    model: query.models.user_shop_item, // 1. Point to the user's inventory item
                    as: 'exp_booster',
                    required: false,
                    include: [
                        {
                            model: query.models.shop_item_lookup, // 2. Nest include to get the item's details
                            required: false
                        }
                    ]
                },
                {
                    model: query.models.user_shop_item,
                    as: 'token_booster',
                    required: false,
                    include: [
                        { model: query.models.shop_item_lookup, required: false }
                    ]
                },
                {
                    model: query.models.user_shop_item,
                    as: 'tap_booster',
                    required: false,
                    include: [
                        { model: query.models.shop_item_lookup, required: false }
                    ]
                }
            ],
            transaction
        });

        if (!user_song_selected) {
            return res.status(400).json({
                data: {},
                status: 400,
                msg: common.config.environment !== "production" ? "[Staging Only Message] No song is selected, only happened in staging, please add dummy song to test." : "Something went wrong. Please try again.",
                error: true
            });
        }

        const instrument_list = [
            user_song_selected.boost_card_one ? {
                user_inventory_card_id: user_song_selected.boost_card_one.id,
                instrument_id: user_song_selected.boost_card_one.instrument_lookup.id,
                instrument_name: user_song_selected.boost_card_one.instrument_lookup.instrument_name,
                rarity_id: user_song_selected.boost_card_one.rarity_lookup.id,
                rarity_name: user_song_selected.boost_card_one.rarity_lookup.rarity_name,
            } : null,
            user_song_selected.boost_card_two ? {
                user_inventory_card_id: user_song_selected.boost_card_two.id,
                instrument_id: user_song_selected.boost_card_two.instrument_lookup.id,
                instrument_name: user_song_selected.boost_card_two.instrument_lookup.instrument_name,
                rarity_id: user_song_selected.boost_card_two.rarity_lookup.id,
                rarity_name: user_song_selected.boost_card_two.rarity_lookup.rarity_name,
            } : null,
            user_song_selected.boost_card_three ? {
                user_inventory_card_id: user_song_selected.boost_card_three.id,
                instrument_id: user_song_selected.boost_card_three.instrument_lookup.id,
                instrument_name: user_song_selected.boost_card_three.instrument_lookup.instrument_name,
                rarity_id: user_song_selected.boost_card_three.rarity_lookup.id,
                rarity_name: user_song_selected.boost_card_three.rarity_lookup.rarity_name,
            } : null,
            user_song_selected.boost_card_four ? {
                user_inventory_card_id: user_song_selected.boost_card_four.id,
                instrument_id: user_song_selected.boost_card_four.instrument_lookup.id,
                instrument_name: user_song_selected.boost_card_four.instrument_lookup.instrument_name,
                rarity_id: user_song_selected.boost_card_four.rarity_lookup.id,
                rarity_name: user_song_selected.boost_card_four.rarity_lookup.rarity_name,
            } : null,
        ].filter(Boolean);
        const data = {
            user_data: {
                user_current_song_tap_count: user_song_selected.tap_count,
                // user_selected_instrument: {
                //     boost_instrument_one: user_song_selected.boost_card_one ? {
                //         instrument_id: user_song_selected.boost_card_one.instrument_lookup.id,
                //         instrument_name: user_song_selected.boost_card_one.instrument_lookup.instrument_name,
                //         rarity_id: user_song_selected.boost_card_one.rarity_lookup.id,
                //         rarity_name: user_song_selected.boost_card_one.rarity_lookup.rarity_name,
                //     } : null,
                //     boost_instrument_two: user_song_selected.boost_card_two ? {
                //         instrument_id: user_song_selected.boost_card_two.instrument_lookup.id,
                //         instrument_name: user_song_selected.boost_card_two.instrument_lookup.instrument_name,
                //         rarity_id: user_song_selected.boost_card_two.rarity_lookup.id,
                //         rarity_name: user_song_selected.boost_card_two.rarity_lookup.rarity_name,
                //     } : null,
                //     boost_instrument_three: user_song_selected.boost_card_three ? {
                //         instrument_id: user_song_selected.boost_card_three.instrument_lookup.id,
                //         instrument_name: user_song_selected.boost_card_three.instrument_lookup.instrument_name,
                //         rarity_id: user_song_selected.boost_card_three.rarity_lookup.id,
                //         rarity_name: user_song_selected.boost_card_three.rarity_lookup.rarity_name,
                //     } : null,
                //     boost_instrument_four: user_song_selected.boost_card_four ? {
                //         instrument_id: user_song_selected.boost_card_four.instrument_lookup.id,
                //         instrument_name: user_song_selected.boost_card_four.instrument_lookup.instrument_name,
                //         rarity_id: user_song_selected.boost_card_four.rarity_lookup.id,
                //         rarity_name: user_song_selected.boost_card_four.rarity_lookup.rarity_name,
                //     } : null,
                // },
                user_selected_instrument: instrument_list,
                is_instrument_allowed: user_song_selected.advance_genre_lookup.is_instrument_boost_allow,
                user_selected_booster: {
                    is_booster_allowed: user_song_selected.advance_genre_lookup.is_instrument_boost_allow,
                    exp_booster: user_song_selected.exp_booster ? {
                        user_inventory_booster_id: user_song_selected.exp_booster.id,
                        booster_id: user_song_selected.exp_booster.shop_item_lookup.id,
                        booster_name: user_song_selected.exp_booster.shop_item_lookup.item_name,
                        booster_type: user_song_selected.exp_booster.shop_item_lookup.item_type,
                        booster_category: user_song_selected.exp_booster.shop_item_lookup.item_category,
                        booster_icon_image: user_song_selected.exp_booster.shop_item_lookup.item_icon_image,
                        booster_percentage: user_song_selected.exp_booster.shop_item_lookup.item_boost_percentage,
                    } : null,
                    token_booster: user_song_selected.token_booster ? {
                        user_inventory_booster_id: user_song_selected.token_booster.id,
                        booster_id: user_song_selected.token_booster.id,
                        booster_name: user_song_selected.token_booster.shop_item_lookup.item_name,
                        booster_type: user_song_selected.token_booster.shop_item_lookup.item_type,
                        booster_category: user_song_selected.token_booster.shop_item_lookup.item_category,
                        booster_icon_image: user_song_selected.token_booster.shop_item_lookup.item_icon_image,
                        booster_percentage: user_song_selected.token_booster.shop_item_lookup.item_boost_percentage,
                    } : null,
                    tap_booster: user_song_selected.tap_booster ? {
                        user_inventory_booster_id: user_song_selected.tap_booster.id,
                        booster_id: user_song_selected.tap_booster.shop_item_lookup.id,
                        booster_name: user_song_selected.tap_booster.shop_item_lookup.item_name,
                        booster_type: user_song_selected.tap_booster.shop_item_lookup.item_type,
                        booster_category: user_song_selected.tap_booster.shop_item_lookup.item_category,
                        booster_icon_image: user_song_selected.tap_booster.shop_item_lookup.item_icon_image,
                        booster_percentage: user_song_selected.tap_booster.shop_item_lookup.item_boost_percentage,
                    } : null,
                }
            },
            song_data: {
                song_type: user_song_selected.song_lookup.song_type,
                genre_type: user_song_selected.advance_genre_lookup.genre_type,
                song_id: user_song_selected.song_lookup.id,
                song_name: user_song_selected.song_lookup.song_name,
                song_cover_image: user_song_selected.song_lookup.song_cover_image,
                song_description: user_song_selected.song_lookup.song_description,
                tapping_count_required: user_song_selected.song_lookup.tapping_count,
            },
            genre_data: {
                genre_id: user_song_selected.advance_genre_lookup.id,
                genre_name: user_song_selected.advance_genre_lookup.genre_name,
            }
        }

        await transaction.commit();
        return res.status(200).json({
            data: data,
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})


router.get("/advance-genre", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const genre = await query.models.advance_genre_lookup.findAll({
            where: {
                genre_type: "advanced",
                is_active: true,
                is_delete: false
            },
            include: [
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_one'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_two'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_three'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_four'
                }],
            transaction
        });
        const genre_with_boost_instrument = genre.map((item) => {
            const instruments = [
                item.boost_instrument_one,
                item.boost_instrument_two,
                item.boost_instrument_three,
                item.boost_instrument_four,
            ];
            const boost_instrument = instruments
                .filter(instrument => instrument)
                .map(instrument => {
                    return {
                        instrument_id: instrument.id,
                        instrument_name: instrument.instrument_name,
                        instrument_image: instrument.instrument_image_normal,
                    };
                });
            return {
                genre_id: item.id,
                genre_name: item.genre_name,
                boost_instrument: boost_instrument,
            };
        });

        await transaction.commit();
        return res.status(200).json({
            data: {
                genre: genre_with_boost_instrument
            },
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id } = req.user;
    const transaction = await query.sequelize.transaction();
    try {
        const song = await query.models.song_lookup.findAll({
            where: {
                song_type: "advanced",
                is_active: true,
                is_delete: false
            },
            transaction
        });

        const formatted_songs = song.map(s => {
            return {
                id: s.id,
                song_name: s.song_name,
                song_cover_image: s.song_cover_image,
                song_description: s.song_description,
                tapping_count: s.tapping_count,
                is_current_playing_song: false
            };
        });

        await transaction.commit();
        return res.status(200).json({
            data: {
                song: formatted_songs
            },
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id}`, error),
            error: true
        });
    }
})

export default router;



/*
user_song_selected_lookup
- id
- user_id
- song_id
- genre_id
- tap_count integer default 0
- boost_instrument_one nullable
- boost_instrument_two nullable
- boost_instrument_three nullable
- boost_instrument_four nullable
- exp_booster_id nullable
- token_booster_id nullable
- tap_booster_id nullable
- is_active
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/


/*
const genre = await query.models.advance_genre_lookup.findAll({
            where: {
                is_active: true,
                is_delete: false
            },
            include: [
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_one'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_two'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_three'
                },
                {
                    model: query.models.instrument_lookup,
                    as: 'boost_instrument_four'
                }],
            transaction
        });
*/


/*
advance_genre_lookup
- id
- genre_name
- boost_instrument_one nullable
- boost_instrument_two nullable
- boost_instrument_three nullable
- boost_instrument_four nullable
- is_active
- created_at integer
- updated_at integer
- is_delete boolean not null default 0


song_lookup
- id
- song_name
- song_cover_image
- song_description
- is_active
- tapping_count
- created_at integer
- updated_at integer
- is_delete boolean not null default 0
*/