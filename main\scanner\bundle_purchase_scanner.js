/**
 * Optimized Bundle Purchase Scanner
 *
 * Monitors TON blockchain for new purchase logs from BundlePreSale contract
 * and stores them in the database. Optimized for rate limits and large batches (e.g., 1,000 logs).
 */

import { Address } from '@ton/ton';
import Decimal from 'decimal.js';
import common from "../shared/imports/common.js";
import query from "../shared/imports/query.js";
import helper from "../shared/imports/helper.js";
import config from "../../config.json" assert { type: "json" };
import { ABIParser } from "./abi_parser.js";
import { Chain as TonClientHelper } from "../shared/helpers/ton_helper.js";

// Configure Decimal.js for high precision
Decimal.set({ precision: 50, rounding: Decimal.ROUND_DOWN });

// Initialize TON Helper (singleton)
const TonHelper = new TonClientHelper(
    `${config.ton.rpc}api/v2/jsonRPC`,
    config.ton.apiKey
);

// Configuration
const CONTRACT_ADDRESS = config.contracts.bundle_presale;
const ABI_FILE_NAME = 'bundle_presale.json';
const PURCHASE_STATUS_SUCCESS = 1n;
const LT_SETTING_KEY = 'bundle_purchase_last_lt';

// Processing constants
const INTER_LOG_DELAY = 500; // ms between processing each log to avoid rate limits
const RETRY_DELAY_BASE = 1000; // ms for exponential backoff on rate limit errors
const MAX_RETRIES = 5; // Per API call

// Error classification helpers
function isRetryableContractError(error) {
    const errorMessage = error.message?.toLowerCase() || '';
    return errorMessage.includes('500') ||
           errorMessage.includes('429') ||
           errorMessage.includes('rate') ||
           errorMessage.includes('timeout');
}

function isRetryableDatabaseError(error) {
    const errorMessage = error.message || '';
    return errorMessage.includes('Deadlock') ||
           errorMessage.includes('Lock wait timeout') ||
           errorMessage.includes('ECONNREFUSED');
}

// Helper functions
function convertHexToTonAddress(hexAddress) {
    return helper.scanner_helper.hexToTonAddressConverted(hexAddress);
}

function convertNanoToDecimal(nanoAmount, decimals = 6) {
    const divisor = new Decimal(10).pow(decimals);
    const amount = new Decimal(nanoAmount.toString());
    return amount.dividedBy(divisor).toFixed(decimals, Decimal.ROUND_DOWN);
}

function getWalletSearchCondition(addressFormats) {
    return {
        [query.Op.or]: [
            addressFormats.bounceable,
            addressFormats.non_bounceable,
        ]
    };
}

/**
 * Get the last stored LT from system_setting
 */
async function getLastStoredLT(transaction) {
    try {
        const setting = await query.models.system_setting.findOne({
            where: { key: LT_SETTING_KEY, is_delete: false },
            transaction
        });
        return setting ? setting.value : null;
    } catch (error) {
        console.error(`Error fetching last LT: ${error.message}`);
        return null;
    }
}

/**
 * Store the last LT in system_setting
 */
async function storeLastLT(lt, transaction) {
    try {
        const [setting, created] = await query.models.system_setting.findOrCreate({
            where: { key: LT_SETTING_KEY, is_delete: false },
            defaults: {
                key: LT_SETTING_KEY,
                value: String(lt),
                created_at: common.util_helper.getCurrentEpochTime(),
                is_delete: false
            },
            transaction
        });

        if (!created) {
            await setting.update({
                value: String(lt),
                updated_at: common.util_helper.getCurrentEpochTime()
            }, { transaction });
        }
    } catch (error) {
        console.error(`Error storing last LT: ${error.message}`);
    }
}

/**
 * Get transaction hash for a specific log ID
 * @param {string} contractAddress - Contract address
 * @param {BigInt} logId - Log ID to search for
 * @param {BigInt} logTimestamp - Log timestamp
 * @param {string|null} fromLT - Starting LT for search optimization
 * @param {Object} transaction - Database transaction
 * @returns {Promise<{txHash: string|null, lastLT: string|null}>} Transaction hash and last LT
 */
async function getTransactionHash(contractAddress, logId, logTimestamp, fromLT, transaction) {
    let retries = 0;

    while (retries < MAX_RETRIES) {
        try {
            const timestamp = Number(logTimestamp);

            console.log(`Searching for log ${logId} at timestamp ${timestamp}${fromLT ? ` (from LT: ${fromLT})` : ''}`);

            if (isNaN(timestamp) || timestamp <= 0) {
                console.warn(`Log ${logId}: Invalid timestamp (${logTimestamp}), scanning without time filter`);
            }

            // Fetch transactions using helper function with LT optimization
            const transactions = await helper.scanner_helper.getTransactions(
                contractAddress.toString(),
                fromLT, // Start from last known LT
                logTimestamp,
                {
                    limit: 100,
                    fetchAll: true
                }
            );

            if (!transactions || transactions.length === 0) {
                console.log(`Log ${logId}: No transactions found`);
                return { txHash: null, lastLT: null };
            }

            console.log(`Checking ${transactions.length} transactions for log ${logId}`);

            // Search for matching transaction
            let foundTxHash = null;
            let lastLT = null;

            for (const tx of transactions) {
                const txTime = tx.utime || 0;

                if (txTime !== timestamp) {
                    continue;
                }

                // Store the latest LT from all transactions
                if (tx.transaction_id && tx.transaction_id.lt) {
                    lastLT = String(tx.transaction_id.lt);
                }

                foundTxHash = tx.transaction_id.hash;
            }

            if (foundTxHash) {
                // Store the last LT for next search
                if (lastLT) {
                    await storeLastLT(lastLT, transaction);
                    console.log(`Stored last LT: ${lastLT}`);
                }
                return { txHash: foundTxHash, lastLT };
            }

            console.log(`Log ${logId}: Transaction not found in ${transactions.length} transactions`);
            return { txHash: null, lastLT };
        } catch (error) {
            if (error.message?.includes('429') || error.message?.includes('rate') || error.message?.includes('Too Many Requests')) {
                retries++;
                const delay = RETRY_DELAY_BASE * Math.pow(2, retries);
                console.warn(`Rate limit for log ${logId}, retrying in ${delay}ms (${retries}/${MAX_RETRIES})`);
                await common.util_helper.wait_stopper(delay);
            } else {
                console.error(`Error fetching transaction hash for log ${logId}: ${error.message}`);
                return { txHash: null, lastLT: null };
            }
        }
    }

    console.error(`Max retries exceeded for log ${logId}`);
    return { txHash: null, lastLT: null };
}

/**
 * Process referral rewards for bundle purchase
 * A. Direct Upline: 5% token earning from each downline purchase
 * B. Direct Downline: 3% bonus on first bundle purchase only (if they have an upline)
 */
async function processReferralRewards(buyer, buyerWallet, bundle, tokenAmount, logId, txHash, transaction) {
    try {
        const UPLINE_REWARD_PERCENTAGE = 0.05; // 5%
        const DOWNLINE_BONUS_PERCENTAGE = 0.03; // 3%

        // Find buyer's referral relationship
        const buyerReferral = await query.models.user_referral.findOne({
            where: {
                user_id: buyer.id,
                is_delete: false
            },
            transaction
        });

        if (!buyerReferral) {
            console.log(`  No referral record found for user ${buyer.id}`);
            return;
        }

        const uplineUserId = buyerReferral.referral_user_id;

        // ===== B. DIRECT DOWNLINE BONUS (First Purchase Only) =====
        // Check if this is the buyer's first bundle purchase
        const previousPurchaseCount = await query.models.bundle_purchase.count({
            where: {
                user_id: buyer.id,
                is_delete: false
            },
            transaction
        });

        // previousPurchaseCount includes the current purchase, so first purchase = 1
        const isFirstPurchase = previousPurchaseCount == 1;

        let downlineBonusAmount = 0;

        if (isFirstPurchase && uplineUserId) {
            const downlineBonus = new Decimal(tokenAmount)
                .times(DOWNLINE_BONUS_PERCENTAGE)
                .toDecimalPlaces(9, Decimal.ROUND_DOWN)
                .toNumber();

            downlineBonusAmount = downlineBonus;

            // Update buyer's wallet balance with bonus
            const buyerBeforeBalance = buyerWallet.balance || 0;

            await buyerWallet.update(
                { balance: query.sequelize.literal(`balance + ${downlineBonus}`) },
                { lock: transaction.LOCK.UPDATE, transaction }
            );

            const buyerAfterBonus = await query.models.wallet.findOne({
                where: { id: buyerWallet.id },
                lock: true,
                transaction
            });

            // Create wallet transaction for downline bonus
            await query.models.wallet_transaction.create({
                user_id: buyer.id,
                wallet_id: buyerWallet.id,
                tx_id: txHash,
                type: 'IN',
                amount: downlineBonus,
                before_balance: buyerBeforeBalance,
                after_balance: buyerAfterBonus.balance,
                category: 'presale_bundle_first_purchase_bonus',
                description: `First purchase bonus - Bundle ${bundle.id}`,
                remark: `Log ID: ${logId}, 3% first purchase bonus`,
                created_at: common.util_helper.getCurrentEpochTime(),
            }, { transaction });

            console.log(`  First Purchase Bonus: User ${buyer.id} received ${downlineBonus} tokens (3%)`);
        } else if (!isFirstPurchase) {
            console.log(`  Downline Bonus: Not first purchase for user ${buyer.id}`);
        }

        // Update bundle_purchase record with bonus token amount
        const bundlePurchase = await query.models.bundle_purchase.findOne({
            where: {
                user_id: buyer.id,
                log_id: logId,
                is_delete: false
            },
            transaction
        });

        if (bundlePurchase && downlineBonusAmount > 0) {
            await bundlePurchase.update(
                { bonus_token: downlineBonusAmount },
                { transaction }
            );
            console.log(`  Updated bundle_purchase record with bonus: ${downlineBonusAmount} tokens`);
        }

        // ===== A. DIRECT UPLINE REWARD (5% from each downline purchase) =====
        if (uplineUserId) {
            const uplineReward = new Decimal(tokenAmount)
                .times(UPLINE_REWARD_PERCENTAGE)
                .toDecimalPlaces(9, Decimal.ROUND_DOWN)
                .toNumber();

            // Find upline user's wallet
            const uplineUser = await query.models.user.findOne({
                where: {
                    id: uplineUserId,
                    is_delete: false
                },
                transaction
            });

            if (!uplineUser) {
                console.log(`  Upline user ${uplineUserId} not found`);
                return;
            }

            const addressFormats = convertHexToTonAddress(
                uplineUser.wallet_address.startsWith('0:')
                    ? uplineUser.wallet_address
                    : uplineUser.wallet_address
            );
            const walletSearchCondition = getWalletSearchCondition(addressFormats);

            const [uplineWallet, uplineWalletCreated] = await query.models.wallet.findOrCreate({
                where: {
                    wallet_address: walletSearchCondition,
                    is_delete: false
                },
                defaults: {
                    user_id: uplineUser.id,
                    wallet_address: uplineUser.wallet_address,
                    type: 'token',
                    balance: 0,
                    network_type: 'ton',
                    created_at: common.util_helper.getCurrentEpochTime(),
                },
                lock: transaction.LOCK.UPDATE,
                transaction
            });

            if (uplineWalletCreated) {
                console.log(`  Created wallet for upline user ${uplineUser.id}`);
            }

            const uplineBeforeBalance = uplineWallet.balance || 0;

            await uplineWallet.update(
                { balance: query.sequelize.literal(`balance + ${uplineReward}`) },
                { lock: transaction.LOCK.UPDATE, transaction }
            );

            const uplineAfterReward = await query.models.wallet.findOne({
                where: { id: uplineWallet.id },
                lock: true,
                transaction
            });

            // Create wallet transaction for upline reward
            await query.models.wallet_transaction.create({
                user_id: uplineUser.id,
                wallet_id: uplineWallet.id,
                tx_id: txHash,
                type: 'IN',
                amount: uplineReward,
                before_balance: uplineBeforeBalance,
                after_balance: uplineAfterReward.balance,
                category: 'presale_bundle_referral_reward',
                description: `Referral reward from user ${buyer.wallet_address} - Bundle ${bundle.id}`,
                remark: `Log ID: ${logId}, 5% referral commission from downline`,
                created_at: common.util_helper.getCurrentEpochTime(),
            }, { transaction });

            console.log(`  Upline Reward: User ${uplineUser.wallet_address} received ${uplineReward} tokens (5% from user ${buyer.wallet_address})`);
        } else {
            console.log(`  No upline found for user ${buyer.wallet_address}`);
        }
    } catch (error) {
        console.error(`  Error processing referral rewards: ${error.message}`);
        throw error; // Re-throw to trigger transaction rollback
    }
}

// Main scanner function
async function bundlePurchaseScanner(delayTime, retryTime) {
    const tonClient = await TonHelper.getTonClient();
    const abiParser = new ABIParser(ABI_FILE_NAME);
    const contract = abiParser.createCaller(tonClient, CONTRACT_ADDRESS);

    console.log('Bundle Purchase Scanner Started');
    console.log(`Contract: ${CONTRACT_ADDRESS}`);
    console.log(`Polling Interval: ${delayTime}ms`);
    common.util_helper.spacer(1);

    while (true) {
        try {
            const transaction = await query.sequelize.transaction();

            const maxLogRecord = await query.models.bundle_purchase.findOne({
                attributes: [[query.sequelize.fn('MAX', query.sequelize.col('log_id')), 'max_log_id']],
                where: { is_delete: false },
                transaction
            });

            const lastProcessedLogId = maxLogRecord?.get('max_log_id')
                ? BigInt(maxLogRecord.get('max_log_id'))
                : 0n;

            await transaction.commit();

            let latestLogId;
            try {
                latestLogId = await contract.getLatestLogId();
            } catch (error) {
                console.error(`❌ Failed to fetch latest log ID: ${error.message}`);
                console.log('Retrying in 10 seconds...');
                await common.util_helper.wait_stopper(10000);
                continue;
            }

            console.log(`Latest log ID on contract: ${latestLogId}`);
            console.log(`Last processed log ID: ${lastProcessedLogId}`);

            if (latestLogId <= lastProcessedLogId) {
                console.log('✅ No new purchase logs found');
                common.util_helper.spacer(1);
                await common.util_helper.wait_stopper(delayTime);
                continue;
            }

            const logsToProcess = Number(latestLogId - lastProcessedLogId);
            console.log(`Processing ${logsToProcess} new logs sequentially...`);
            common.util_helper.spacer(1);

            let successCount = 0;
            let skipCount = 0;
            let errorCount = 0;

            // Get the last stored LT for optimization
            const initialTransaction = await query.sequelize.transaction();
            let currentLT = await getLastStoredLT(initialTransaction);
            await initialTransaction.commit();

            if (currentLT) {
                console.log(`Starting scan from stored LT: ${currentLT}`);
            }

            // Process each log ID one by one
            for (let logId = lastProcessedLogId + 1n; logId <= latestLogId; logId++) {
                console.log(`\n${'='.repeat(60)}`);
                console.log(`Processing Log ID: ${logId}`);
                console.log('='.repeat(60));

                let logProcessed = false;
                let retryCount = 0;

                // Create a new transaction for each log
                const logTransaction = await query.sequelize.transaction();

                while (!logProcessed && retryCount < MAX_RETRIES) {
                    try {
                        // Check if already processed before fetching (prevent double insert)
                        const existing = await query.models.bundle_purchase.findOne({
                            where: { log_id: Number(logId), is_delete: false },
                            transaction: logTransaction
                        });

                        if (existing) {
                            console.log(`⚠️  Log ${logId}: Already processed`);
                            skipCount++;
                            logProcessed = true;
                            await logTransaction.commit();
                            break;
                        }

                        let log;
                        try {
                            log = await contract.getPurchaseLog(logId);
                        } catch (fetchError) {
                            console.error(`❌ Log ${logId}: Failed to fetch from contract - ${fetchError.message}`);

                            // Check if it's a retryable error
                            if (isRetryableContractError(fetchError)) {
                                retryCount++;
                                const retryDelay = RETRY_DELAY_BASE * Math.pow(2, retryCount);
                                console.log(`Retry ${retryCount}/${MAX_RETRIES} for log ${logId} in ${retryDelay}ms`);
                                await common.util_helper.wait_stopper(retryDelay);
                                continue; // Continue retry loop
                            } else {
                                // Non-retryable error, skip this log
                                console.error(`❌ Log ${logId}: Non-retryable error, skipping`);
                                errorCount++;
                                logProcessed = true;
                                break;
                            }
                        }

                        if (!log) {
                            console.log(`⚠️  Log ${logId}: Not found`);
                            skipCount++;
                            logProcessed = true;
                            await logTransaction.commit();
                            break;
                        }

                        if (log.status != PURCHASE_STATUS_SUCCESS) {
                            console.log(`⚠️  Log ${logId}: Skipped (status: ${log.status})`);
                            skipCount++;
                            logProcessed = true;
                            await logTransaction.commit();
                            break;
                        }

                        const { txHash, lastLT } = await getTransactionHash(
                            CONTRACT_ADDRESS,
                            logId,
                            log.timestamp,
                            currentLT,
                            logTransaction
                        );

                        // Update current LT for next iteration
                        if (lastLT) {
                            currentLT = lastLT;
                        }

                        const addressFormats = convertHexToTonAddress(log.buyer);
                        const walletSearchCondition = getWalletSearchCondition(addressFormats);

                        const user = await query.models.user.findOne({
                            where: {
                                wallet_address: walletSearchCondition,
                                is_delete: false
                            },
                            transaction: logTransaction
                        });

                        if (!user) {
                            console.log(`❌ Log ${logId}: User not found for wallet - ${log.buyer}`);
                            errorCount++;
                            logProcessed = true;

                            // Rollback transaction for user not found
                            if (!logTransaction.finished) {
                                await logTransaction.rollback();
                            }
                            break;
                        }

                        const [wallet, created] = await query.models.wallet.findOrCreate({
                            where: {
                                wallet_address: walletSearchCondition,
                                is_delete: false
                            },
                            defaults: {
                                user_id: user.id,
                                wallet_address: user.wallet_address,
                                type: 'token',
                                balance: 0,
                                network_type: 'ton',
                                created_at: common.util_helper.getCurrentEpochTime(),
                            },
                            lock: logTransaction.LOCK.UPDATE,
                            transaction: logTransaction
                        });

                        if (created) {
                            console.log(`Log ${logId}: Created new wallet for user ${user.id}`);
                        }

                        const bundle = await query.models.bundle.findOne({
                            where: {
                                id: Number(log.bundleId),
                                is_delete: false
                            },
                            transaction: logTransaction
                        });

                        if (!bundle) {
                            console.log(`❌ Log ${logId}: Bundle ${log.roundId} not found`);
                            errorCount++;
                            logProcessed = true;
                            await logTransaction.commit();
                            break;
                        }

                        const usdAmount = convertNanoToDecimal(log.usdAmount, 18);
                        const tokenAmount = bundle.token_allocation;

                        const purchaseObj = {
                            user_id: user.id,
                            wallet_id: wallet.id,
                            bundle_id: Number(log.bundleId),
                            log_id: Number(logId),
                            tx_id: txHash,
                            payment_jetton: Address.parse(log.jettonMaster).toString(),
                            contract_address: CONTRACT_ADDRESS,
                            usd_amount: usdAmount,
                            token_amount: tokenAmount,
                            created_at: common.util_helper.getCurrentEpochTime(),
                        }

                        await query.models.bundle_purchase.create(purchaseObj, { transaction: logTransaction });

                        const beforeBalance = wallet.balance || 0;

                        await wallet.update(
                            { balance: query.sequelize.literal(`balance + ${parseFloat(tokenAmount)}`) },
                            { lock: logTransaction.LOCK.UPDATE, transaction: logTransaction }
                        );

                        const updatedWallet = await query.models.wallet.findOne({
                            where: {
                                id: wallet.id
                            },
                            lock: true,
                            transaction: logTransaction
                        });

                        const afterBalance = updatedWallet.balance;

                        await query.models.wallet_transaction.create({
                            user_id: user.id,
                            wallet_id: wallet.id,
                            tx_id: txHash,
                            type: 'IN',
                            amount: parseFloat(tokenAmount),
                            before_balance: beforeBalance,
                            after_balance: afterBalance,
                            category: 'presale_bundle_purchase',
                            description: `Bundle ${log.bundleId} - $${usdAmount} purchase`,
                            remark: `Log ID: ${logId}, Tx: ${txHash || 'N/A'}`,
                            created_at: common.util_helper.getCurrentEpochTime(),
                        }, { transaction: logTransaction });

                        console.log(`✅ Log ${logId}: User ${user.id} purchased bundle ${log.bundleId} for ${usdAmount} USD | Tx: ${txHash || 'N/A'} | Balance: ${beforeBalance} → ${afterBalance}`);

                        // ===== REFERRAL REWARD SYSTEM =====
                        await processReferralRewards(
                            user,
                            wallet,
                            bundle,
                            tokenAmount,
                            logId,
                            txHash,
                            logTransaction
                        );
                        successCount++;
                        logProcessed = true;

                        // Commit transaction for this log
                        await logTransaction.commit();

                        // Delay between logs to avoid rate limits
                        await common.util_helper.wait_stopper(INTER_LOG_DELAY);
                    } catch (error) {
                        if (isRetryableDatabaseError(error)) {
                            retryCount++;
                            const retryDelay = RETRY_DELAY_BASE * Math.pow(2, retryCount);
                            console.log(`Database/Connection error, retry ${retryCount}/${MAX_RETRIES} in ${retryDelay}ms`);

                            // Rollback transaction before retry
                            if (!logTransaction.finished) {
                                await logTransaction.rollback();
                            }

                            await common.util_helper.wait_stopper(retryDelay);
                            continue; // Continue retry loop
                        } else {
                            // Non-retryable error, skip this log
                            console.error(`❌ Log ${logId}: Non-retryable error, skipping`);
                            errorCount++;
                            logProcessed = true;

                            // Rollback transaction for non-retryable errors
                            if (!logTransaction.finished) {
                                await logTransaction.rollback();
                            }
                            break;
                        }
                    }
                }

                // If max retries exceeded, rollback transaction
                if (!logProcessed && retryCount >= MAX_RETRIES) {
                    console.error(`❌ Log ${logId}: Max retries (${MAX_RETRIES}) exceeded, skipping`);
                    errorCount++;

                    if (!logTransaction.finished) {
                        await logTransaction.rollback();
                    }
                }
            }

            await common.util_helper.wait_stopper(delayTime);
            common.util_helper.spacer(1);
        } catch (error) {
            console.error('❌ Scanner error:', error.message);
            console.error(error);
            console.log(`============ Error, retrying in ${retryTime / 1000}s =============`);
            common.util_helper.spacer(2);
            await common.util_helper.wait_stopper(retryTime);
        }
    }
}

// Start scanner
const delayTime = 8000; // 8 seconds
const retryTime = 4000; // 4 seconds

bundlePurchaseScanner(delayTime, retryTime);
