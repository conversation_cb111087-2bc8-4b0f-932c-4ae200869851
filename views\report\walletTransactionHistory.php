<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'Wallet Transaction History');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_wallet_transaction', [
      'model'   => $model,
      'page'    => 'wallet-transaction-history',
      'pjax_id' => "#pjax-wallet-transaction-history",
    ]); ?>
  </div>
  <div class="card-body">
    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label'  => Yii::t('app','Username'),
          'format' => 'raw',
          'value'  => function ($model) {
            $display = "";

            $user = $model->user;

            $_name = empty($user->username) ? $user->first_last_name : $user->username;
            $display = "";
            $display .= "Username : ".$_name;

            return $display;
          },
        ],
        [
          'label'  => Yii::t('app','Wallet Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->user->wallet_address)) {
              return "-";
            }
            
            $link = Yii::$app->formatHelper->formatTon('address', $model->user->wallet_address, 5);

            return $link;
          }
        ],
        [
          'label' => Yii::t('app','Type'),
          'value' => function ($model) {
            if (empty($model->type)) {
              return "-";
            }
            return strtoupper($model->type);
          }
        ],
        [
          'label' => Yii::t('app','Category'),
          'value' => function ($model) {
            if (empty($model->category)) {
              return "-";
            }
            return ucwords(str_replace("_", " ", $model->category));
          }
        ],
        [
          'label'  => Yii::t('app','Transaction ID'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->tx_id)) {
              return "-";
            }

            $link = Yii::$app->formatHelper->formatTon('transaction', $model->tx_id, 5);

            return $link;
          }
        ],
        [
          'label' => Yii::t('app','Amount'),
          'value' => function ($model) {
            return $model->amount * 1;
          }
        ],
        [
          'label' => Yii::t('app','Before Balance'),
          'value' => function ($model) {
            return $model->before_balance * 1;
          }
        ],
        [
          'label' => Yii::t('app','After Balance'),
          'value' => function ($model) {
            return $model->after_balance * 1;
          }
        ],
        [
          'label' => Yii::t('app','Description'),
          'value' => function ($model) {
            if (empty($model->description)) {
              return "-";
            }
            return $model->description;
          }
        ],
        [
          'label' => Yii::t('app','Remark'),
          'value' => function ($model) {
            return ucwords(str_replace("_", " ", $model->remark));
          }
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          }
        ]
      ]
    ]); ?>
  </div>
</div>
