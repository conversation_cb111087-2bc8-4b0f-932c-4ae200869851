a:14:{s:6:"config";s:6366:"a:5:{s:10:"phpVersion";s:6:"7.4.33";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:5:"Aleko";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"7.4.33";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:26:{s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:64:"C:\xampp\htdocs\aleko-bo\vendor/2amigos/yii2-ckeditor-widget/src";}}s:25:"alexantr/yii2-colorpicker";a:3:{s:4:"name";s:25:"alexantr/yii2-colorpicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@alexantr/colorpicker";s:57:"C:\xampp\htdocs\aleko-bo\vendor/alexantr/yii2-colorpicker";}}s:24:"asmoday74/yii2-ckeditor5";a:3:{s:4:"name";s:24:"asmoday74/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:20:"@asmoday74/ckeditor5";s:56:"C:\xampp\htdocs\aleko-bo\vendor/asmoday74/yii2-ckeditor5";}}s:29:"cetver/yii2-language-selector";a:3:{s:4:"name";s:29:"cetver/yii2-language-selector";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:24:"@cetver/LanguageSelector";s:61:"C:\xampp\htdocs\aleko-bo\vendor/cetver/yii2-language-selector";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:65:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:58:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte3/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:61:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:56:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-dialog/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-fileinput/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/date";s:67:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datepicker/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:60:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-date-range/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:71:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-typeahead/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:54:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-grid/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:64:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-select2/src";}}s:20:"nickcv/yii2-mandrill";a:3:{s:4:"name";s:20:"nickcv/yii2-mandrill";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@nickcv/mandrill";s:56:"C:\xampp\htdocs\aleko-bo\vendor/nickcv/yii2-mandrill/src";}}s:23:"sangroya/yii2-ckeditor5";a:3:{s:4:"name";s:23:"sangroya/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@sangroya/ckeditor5";s:55:"C:\xampp\htdocs\aleko-bo\vendor/sangroya/yii2-ckeditor5";}}s:27:"unclead/yii2-multiple-input";a:3:{s:4:"name";s:27:"unclead/yii2-multiple-input";s:7:"version";s:8:"********";s:5:"alias";a:3:{s:22:"@unclead/multipleinput";s:63:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/src";s:28:"@unclead/multipleinput/tests";s:65:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/tests";s:31:"@unclead/multipleinput/examples";s:68:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/examples";}}s:21:"yiier/yii2-aliyun-oss";a:3:{s:4:"name";s:21:"yiier/yii2-aliyun-oss";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@yiier/AliyunOSS";s:53:"C:\xampp\htdocs\aleko-bo\vendor/yiier/yii2-aliyun-oss";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-faker/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-httpclient/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-jui/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:10:"@yii/debug";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-debug/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.1.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:60:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-swiftmailer/src";}}}}";s:3:"log";s:65370:"a:1:{s:8:"messages";a:214:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532787.925869;i:4;a:0:{}i:5;i:2705424;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532787.925879;i:4;a:0:{}i:5;i:2706600;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532787.932782;i:4;a:0:{}i:5;i:3884064;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532787.935479;i:4;a:0:{}i:5;i:4204016;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532787.93575;i:4;a:0:{}i:5;i:4228152;}i:8;a:6:{i:0;s:35:"Route requested: 'gii/default/view'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1761532787.939439;i:4;a:0:{}i:5;i:4745432;}i:9;a:6:{i:0;s:30:"Route to run: gii/default/view";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1761532787.940796;i:4;a:0:{}i:5;i:4883256;}i:10;a:6:{i:0;s:67:"Running action: yii\gii\controllers\DefaultController::actionView()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1761532787.947382;i:4;a:0:{}i:5;i:5666344;}i:11;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532787.978766;i:4;a:0:{}i:5;i:6932016;}i:12;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532787.978805;i:4;a:0:{}i:5;i:6933704;}i:17;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014207;i:4;a:0:{}i:5;i:7000208;}i:20;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.015257;i:4;a:0:{}i:5;i:7018568;}i:23;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.017799;i:4;a:0:{}i:5;i:7112816;}i:26;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.01855;i:4;a:0:{}i:5;i:7119824;}i:29;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057011;i:4;a:0:{}i:5;i:7135552;}i:32;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057365;i:4;a:0:{}i:5;i:7131184;}i:35;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057719;i:4;a:0:{}i:5;i:7133112;}i:38;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061408;i:4;a:0:{}i:5;i:7145888;}i:41;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061699;i:4;a:0:{}i:5;i:7143216;}i:44;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.062025;i:4;a:0:{}i:5;i:7144384;}i:47;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074344;i:4;a:0:{}i:5;i:7152896;}i:50;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074621;i:4;a:0:{}i:5;i:7152424;}i:53;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074964;i:4;a:0:{}i:5;i:7152696;}i:56;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087711;i:4;a:0:{}i:5;i:7167376;}i:59;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.088021;i:4;a:0:{}i:5;i:7163704;}i:62;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.0965;i:4;a:0:{}i:5;i:7166592;}i:65;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100185;i:4;a:0:{}i:5;i:7183936;}i:68;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100483;i:4;a:0:{}i:5;i:7181832;}i:71;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100931;i:4;a:0:{}i:5;i:7183008;}i:74;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115648;i:4;a:0:{}i:5;i:7188576;}i:77;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115904;i:4;a:0:{}i:5;i:7189760;}i:80;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.136082;i:4;a:0:{}i:5;i:7190928;}i:83;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140064;i:4;a:0:{}i:5;i:7200328;}i:86;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140352;i:4;a:0:{}i:5;i:7199360;}i:89;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.151337;i:4;a:0:{}i:5;i:7200864;}i:92;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155485;i:4;a:0:{}i:5;i:7205472;}i:95;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155717;i:4;a:0:{}i:5;i:7207184;}i:98;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.156079;i:4;a:0:{}i:5;i:7208872;}i:101;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.159928;i:4;a:0:{}i:5;i:7215328;}i:104;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160152;i:4;a:0:{}i:5;i:7216024;}i:107;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160496;i:4;a:0:{}i:5;i:7216592;}i:110;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.16493;i:4;a:0:{}i:5;i:7226976;}i:113;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165219;i:4;a:0:{}i:5;i:7225512;}i:116;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165591;i:4;a:0:{}i:5;i:7225768;}i:119;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178464;i:4;a:0:{}i:5;i:7235152;}i:122;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178774;i:4;a:0:{}i:5;i:7234216;}i:125;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.179114;i:4;a:0:{}i:5;i:7234472;}i:128;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.20135;i:4;a:0:{}i:5;i:7249136;}i:131;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201684;i:4;a:0:{}i:5;i:7249472;}i:134;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.202087;i:4;a:0:{}i:5;i:7249720;}i:137;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.20633;i:4;a:0:{}i:5;i:7259128;}i:140;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.20664;i:4;a:0:{}i:5;i:7258200;}i:143;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.207053;i:4;a:0:{}i:5;i:7259344;}i:146;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.21152;i:4;a:0:{}i:5;i:7273048;}i:149;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211868;i:4;a:0:{}i:5;i:7269808;}i:152;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.212268;i:4;a:0:{}i:5;i:7270984;}i:155;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216144;i:4;a:0:{}i:5;i:7288648;}i:158;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216469;i:4;a:0:{}i:5;i:7283304;}i:161;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216793;i:4;a:0:{}i:5;i:7284216;}i:164;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228639;i:4;a:0:{}i:5;i:7294624;}i:167;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228915;i:4;a:0:{}i:5;i:7293128;}i:170;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.229246;i:4;a:0:{}i:5;i:7294024;}i:173;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241453;i:4;a:0:{}i:5;i:7303480;}i:176;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241744;i:4;a:0:{}i:5;i:7302472;}i:179;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.242106;i:4;a:0:{}i:5;i:7302728;}i:182;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245036;i:4;a:0:{}i:5;i:7307288;}i:185;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.24526;i:4;a:0:{}i:5;i:7309040;}i:188;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245551;i:4;a:0:{}i:5;i:7309296;}i:191;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.257877;i:4;a:0:{}i:5;i:7322016;}i:194;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258169;i:4;a:0:{}i:5;i:7319392;}i:197;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258491;i:4;a:0:{}i:5;i:7319648;}i:200;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.279734;i:4;a:0:{}i:5;i:7330032;}i:203;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280048;i:4;a:0:{}i:5;i:7328576;}i:206;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280429;i:4;a:0:{}i:5;i:7328848;}i:209;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296069;i:4;a:0:{}i:5;i:7339272;}i:212;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296346;i:4;a:0:{}i:5;i:7337776;}i:215;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296689;i:4;a:0:{}i:5;i:7338048;}i:218;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.30868;i:4;a:0:{}i:5;i:7347552;}i:221;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308971;i:4;a:0:{}i:5;i:7346544;}i:224;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.309299;i:4;a:0:{}i:5;i:7346816;}i:227;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312736;i:4;a:0:{}i:5;i:7356216;}i:230;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312974;i:4;a:0:{}i:5;i:7355288;}i:233;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.313281;i:4;a:0:{}i:5;i:7356472;}i:236;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.325871;i:4;a:0:{}i:5;i:7370168;}i:239;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326145;i:4;a:0:{}i:5;i:7367008;}i:242;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.32647;i:4;a:0:{}i:5;i:7367280;}i:245;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339053;i:4;a:0:{}i:5;i:7382032;}i:248;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339351;i:4;a:0:{}i:5;i:7378224;}i:251;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339727;i:4;a:0:{}i:5;i:7378480;}i:254;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.35158;i:4;a:0:{}i:5;i:7391224;}i:257;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351887;i:4;a:0:{}i:5;i:7396752;}i:260;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.352237;i:4;a:0:{}i:5;i:7397000;}i:263;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.36433;i:4;a:0:{}i:5;i:7409664;}i:266;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364661;i:4;a:0:{}i:5;i:7407096;}i:269;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364988;i:4;a:0:{}i:5;i:7407352;}i:272;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368291;i:4;a:0:{}i:5;i:7423992;}i:275;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.3686;i:4;a:0:{}i:5;i:7419264;}i:278;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368931;i:4;a:0:{}i:5;i:7421472;}i:281;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381237;i:4;a:0:{}i:5;i:7429944;}i:284;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.3815;i:4;a:0:{}i:5;i:7429504;}i:287;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381859;i:4;a:0:{}i:5;i:7429776;}i:290;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.393982;i:4;a:0:{}i:5;i:7442544;}i:293;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394308;i:4;a:0:{}i:5;i:7439832;}i:296;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394661;i:4;a:0:{}i:5;i:7440080;}i:299;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399726;i:4;a:0:{}i:5;i:7469592;}i:302;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400183;i:4;a:0:{}i:5;i:7457824;}i:305;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400619;i:4;a:0:{}i:5;i:7459376;}i:308;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405139;i:4;a:0:{}i:5;i:7469712;}i:311;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405468;i:4;a:0:{}i:5;i:7468264;}i:314;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405918;i:4;a:0:{}i:5;i:7471816;}i:317;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410484;i:4;a:0:{}i:5;i:7481240;}i:320;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410834;i:4;a:0:{}i:5;i:7480272;}i:323;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.411364;i:4;a:0:{}i:5;i:7481448;}i:326;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415383;i:4;a:0:{}i:5;i:7491776;}i:329;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415695;i:4;a:0:{}i:5;i:7490328;}i:332;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.416148;i:4;a:0:{}i:5;i:7492568;}i:335;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420656;i:4;a:0:{}i:5;i:7504272;}i:338;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420918;i:4;a:0:{}i:5;i:7502208;}i:341;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.421268;i:4;a:0:{}i:5;i:7503376;}i:344;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.42517;i:4;a:0:{}i:5;i:7512776;}i:347;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425442;i:4;a:0:{}i:5;i:7511856;}i:350;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425896;i:4;a:0:{}i:5;i:7513592;}i:353;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430256;i:4;a:0:{}i:5;i:7522976;}i:356;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430599;i:4;a:0:{}i:5;i:7522056;}i:359;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.431052;i:4;a:0:{}i:5;i:7523208;}i:362;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435106;i:4;a:0:{}i:5;i:7533528;}i:365;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435414;i:4;a:0:{}i:5;i:7532080;}i:368;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435844;i:4;a:0:{}i:5;i:7534312;}i:371;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.43962;i:4;a:0:{}i:5;i:7543672;}i:374;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439893;i:4;a:0:{}i:5;i:7542752;}i:377;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.440225;i:4;a:0:{}i:5;i:7544456;}i:380;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.443977;i:4;a:0:{}i:5;i:7556048;}i:383;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444252;i:4;a:0:{}i:5;i:7554072;}i:386;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444588;i:4;a:0:{}i:5;i:7556312;}i:389;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448077;i:4;a:0:{}i:5;i:7576384;}i:392;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448382;i:4;a:0:{}i:5;i:7570120;}i:395;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448741;i:4;a:0:{}i:5;i:7576712;}i:398;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.45262;i:4;a:0:{}i:5;i:7592464;}i:401;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452948;i:4;a:0:{}i:5;i:7588168;}i:404;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.453327;i:4;a:0:{}i:5;i:7589328;}i:407;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457274;i:4;a:0:{}i:5;i:7607840;}i:410;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.45763;i:4;a:0:{}i:5;i:7602408;}i:413;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.458877;i:4;a:0:{}i:5;i:7691288;}i:416;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.532144;i:4;a:0:{}i:5;i:7699776;}i:419;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533379;i:4;a:0:{}i:5;i:7702840;}i:422;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533721;i:4;a:0:{}i:5;i:7705528;}i:425;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533966;i:4;a:0:{}i:5;i:7707840;}i:428;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534186;i:4;a:0:{}i:5;i:7710152;}i:431;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534373;i:4;a:0:{}i:5;i:7711912;}i:434;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534566;i:4;a:0:{}i:5;i:7714008;}i:437;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534775;i:4;a:0:{}i:5;i:7717000;}i:440;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534971;i:4;a:0:{}i:5;i:7719640;}i:443;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535167;i:4;a:0:{}i:5;i:7722960;}i:446;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535347;i:4;a:0:{}i:5;i:7725192;}i:449;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535526;i:4;a:0:{}i:5;i:7726904;}i:452;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535772;i:4;a:0:{}i:5;i:7730184;}i:455;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536038;i:4;a:0:{}i:5;i:7732816;}i:458;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536259;i:4;a:0:{}i:5;i:7735416;}i:461;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536439;i:4;a:0:{}i:5;i:7737512;}i:464;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536623;i:4;a:0:{}i:5;i:7740320;}i:467;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536822;i:4;a:0:{}i:5;i:7742936;}i:470;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537053;i:4;a:0:{}i:5;i:7745864;}i:473;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537296;i:4;a:0:{}i:5;i:7749680;}i:476;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537535;i:4;a:0:{}i:5;i:7751888;}i:479;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537763;i:4;a:0:{}i:5;i:7754112;}i:482;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537966;i:4;a:0:{}i:5;i:7756208;}i:485;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538206;i:4;a:0:{}i:5;i:7759696;}i:488;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538448;i:4;a:0:{}i:5;i:7761960;}i:491;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538664;i:4;a:0:{}i:5;i:7764560;}i:494;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53883;i:4;a:0:{}i:5;i:7766288;}i:497;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539025;i:4;a:0:{}i:5;i:7768408;}i:500;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539267;i:4;a:0:{}i:5;i:7771064;}i:503;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539487;i:4;a:0:{}i:5;i:7773296;}i:506;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53968;i:4;a:0:{}i:5;i:7775904;}i:509;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539846;i:4;a:0:{}i:5;i:7777632;}i:512;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540018;i:4;a:0:{}i:5;i:7779744;}i:515;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540269;i:4;a:0:{}i:5;i:7798776;}i:518;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540468;i:4;a:0:{}i:5;i:7801072;}i:521;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540638;i:4;a:0:{}i:5;i:7803192;}i:524;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540812;i:4;a:0:{}i:5;i:7805296;}i:527;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541002;i:4;a:0:{}i:5;i:7808240;}i:530;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541194;i:4;a:0:{}i:5;i:7810448;}i:533;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541384;i:4;a:0:{}i:5;i:7812704;}i:536;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541553;i:4;a:0:{}i:5;i:7814424;}i:539;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541729;i:4;a:0:{}i:5;i:7816528;}i:542;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541917;i:4;a:0:{}i:5;i:7818736;}i:545;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542114;i:4;a:0:{}i:5;i:7820816;}i:548;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542303;i:4;a:0:{}i:5;i:7822928;}i:551;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542507;i:4;a:0:{}i:5;i:7825184;}i:554;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542709;i:4;a:0:{}i:5;i:7829056;}i:557;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542895;i:4;a:0:{}i:5;i:7830776;}i:560;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543086;i:4;a:0:{}i:5;i:7832912;}i:563;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543358;i:4;a:0:{}i:5;i:7835240;}i:566;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543612;i:4;a:0:{}i:5;i:7837472;}i:569;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543869;i:4;a:0:{}i:5;i:7840112;}i:572;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544125;i:4;a:0:{}i:5;i:7842448;}i:575;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544408;i:4;a:0:{}i:5;i:7844800;}i:578;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544682;i:4;a:0:{}i:5;i:7847160;}i:581;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544923;i:4;a:0:{}i:5;i:7849520;}i:584;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545156;i:4;a:0:{}i:5;i:7852192;}i:587;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545386;i:4;a:0:{}i:5;i:7854520;}i:590;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545602;i:4;a:0:{}i:5;i:7856320;}i:593;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545863;i:4;a:0:{}i:5;i:7857976;}i:596;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546103;i:4;a:0:{}i:5;i:7860600;}i:599;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54633;i:4;a:0:{}i:5;i:7862832;}i:602;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546543;i:4;a:0:{}i:5;i:7865064;}i:605;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546739;i:4;a:0:{}i:5;i:7866792;}i:608;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.547059;i:4;a:0:{}i:5;i:7874744;}i:611;a:6:{i:0;s:108:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/default/model.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.572347;i:4;a:0:{}i:5;i:8031368;}i:612;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.621662;i:4;a:0:{}i:5;i:7956544;}i:613;a:6:{i:0;s:99:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\generators\model/form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.625617;i:4;a:0:{}i:5;i:8513944;}i:614;a:6:{i:0;s:102:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\default\view/files.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.633371;i:4;a:0:{}i:5;i:8666296;}i:615;a:6:{i:0;s:101:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2-gii\src\views\layouts\generator.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.675111;i:4;a:0:{}i:5;i:8648408;}i:616;a:6:{i:0;s:96:"Rendering view file: C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/views/layouts/main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1761532788.676009;i:4;a:0:{}i:5;i:8704408;}}}";s:9:"profiling";s:126038:"a:3:{s:6:"memory";i:8980176;s:4:"time";d:0.7945470809936523;s:8:"messages";a:400:{i:13;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532787.97881;i:4;a:0:{}i:5;i:6934080;}i:14;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532788.001257;i:4;a:0:{}i:5;i:6982744;}i:15;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.001315;i:4;a:0:{}i:5;i:6982256;}i:16;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014141;i:4;a:0:{}i:5;i:6998960;}i:18;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014223;i:4;a:0:{}i:5;i:7000824;}i:19;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014398;i:4;a:0:{}i:5;i:7003320;}i:21;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.015273;i:4;a:0:{}i:5;i:7020680;}i:22;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.015689;i:4;a:0:{}i:5;i:7024208;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.017816;i:4;a:0:{}i:5;i:7113384;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.018504;i:4;a:0:{}i:5;i:7118336;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.018562;i:4;a:0:{}i:5;i:7120424;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.056945;i:4;a:0:{}i:5;i:7134912;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057028;i:4;a:0:{}i:5;i:7136152;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057207;i:4;a:0:{}i:5;i:7137968;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057379;i:4;a:0:{}i:5;i:7134576;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.05769;i:4;a:0:{}i:5;i:7136208;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057726;i:4;a:0:{}i:5;i:7133752;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061362;i:4;a:0:{}i:5;i:7145280;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061422;i:4;a:0:{}i:5;i:7146504;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061572;i:4;a:0:{}i:5;i:7148464;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061712;i:4;a:0:{}i:5;i:7145328;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.06199;i:4;a:0:{}i:5;i:7147816;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.062035;i:4;a:0:{}i:5;i:7145000;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074285;i:4;a:0:{}i:5;i:7152272;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074361;i:4;a:0:{}i:5;i:7153496;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074508;i:4;a:0:{}i:5;i:7154808;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074635;i:4;a:0:{}i:5;i:7154536;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074932;i:4;a:0:{}i:5;i:7156168;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074974;i:4;a:0:{}i:5;i:7153336;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087656;i:4;a:0:{}i:5;i:7166752;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087726;i:4;a:0:{}i:5;i:7168016;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087894;i:4;a:0:{}i:5;i:7170488;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.088038;i:4;a:0:{}i:5;i:7165816;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.096429;i:4;a:0:{}i:5;i:7170024;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.096515;i:4;a:0:{}i:5;i:7167208;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.10013;i:4;a:0:{}i:5;i:7177680;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.1002;i:4;a:0:{}i:5;i:7184536;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100366;i:4;a:0:{}i:5;i:7186232;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100495;i:4;a:0:{}i:5;i:7183944;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100893;i:4;a:0:{}i:5;i:7186424;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.10094;i:4;a:0:{}i:5;i:7183648;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115589;i:4;a:0:{}i:5;i:7187968;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115665;i:4;a:0:{}i:5;i:7189192;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115822;i:4;a:0:{}i:5;i:7190568;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115916;i:4;a:0:{}i:5;i:7191872;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.136013;i:4;a:0:{}i:5;i:7194360;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.136096;i:4;a:0:{}i:5;i:7191544;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140009;i:4;a:0:{}i:5;i:7199704;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140079;i:4;a:0:{}i:5;i:7200928;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140251;i:4;a:0:{}i:5;i:7202496;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140363;i:4;a:0:{}i:5;i:7201472;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.151271;i:4;a:0:{}i:5;i:7203960;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.151351;i:4;a:0:{}i:5;i:7201504;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155434;i:4;a:0:{}i:5;i:7204864;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155499;i:4;a:0:{}i:5;i:7206088;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155647;i:4;a:0:{}i:5;i:7207528;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155727;i:4;a:0:{}i:5;i:7209296;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.156048;i:4;a:0:{}i:5;i:7212304;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.156087;i:4;a:0:{}i:5;i:7209488;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.159883;i:4;a:0:{}i:5;i:7214704;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.159941;i:4;a:0:{}i:5;i:7215928;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160073;i:4;a:0:{}i:5;i:7217176;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160162;i:4;a:0:{}i:5;i:7218136;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160467;i:4;a:0:{}i:5;i:7219768;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160504;i:4;a:0:{}i:5;i:7217192;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.164877;i:4;a:0:{}i:5;i:7226336;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.164945;i:4;a:0:{}i:5;i:7227576;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165108;i:4;a:0:{}i:5;i:7229136;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.16523;i:4;a:0:{}i:5;i:7227624;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165562;i:4;a:0:{}i:5;i:7229256;}i:117;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165599;i:4;a:0:{}i:5;i:7226384;}i:118;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178403;i:4;a:0:{}i:5;i:7234520;}i:120;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.17848;i:4;a:0:{}i:5;i:7235768;}i:121;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178653;i:4;a:0:{}i:5;i:7237336;}i:123;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178788;i:4;a:0:{}i:5;i:7236328;}i:124;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.179086;i:4;a:0:{}i:5;i:7237960;}i:126;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.179122;i:4;a:0:{}i:5;i:7235088;}i:127;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201282;i:4;a:0:{}i:5;i:7248512;}i:129;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201367;i:4;a:0:{}i:5;i:7253832;}i:130;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201555;i:4;a:0:{}i:5;i:7255648;}i:132;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201695;i:4;a:0:{}i:5;i:7251584;}i:133;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.202054;i:4;a:0:{}i:5;i:7253216;}i:135;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.202096;i:4;a:0:{}i:5;i:7250320;}i:136;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206287;i:4;a:0:{}i:5;i:7258488;}i:138;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206344;i:4;a:0:{}i:5;i:7259728;}i:139;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206515;i:4;a:0:{}i:5;i:7261288;}i:141;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206654;i:4;a:0:{}i:5;i:7260312;}i:142;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.207021;i:4;a:0:{}i:5;i:7262776;}i:144;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.207061;i:4;a:0:{}i:5;i:7259960;}i:145;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211473;i:4;a:0:{}i:5;i:7272424;}i:147;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211535;i:4;a:0:{}i:5;i:7273648;}i:148;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211712;i:4;a:0:{}i:5;i:7275472;}i:150;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.21189;i:4;a:0:{}i:5;i:7271920;}i:151;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.212234;i:4;a:0:{}i:5;i:7274400;}i:153;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.212277;i:4;a:0:{}i:5;i:7271624;}i:154;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216105;i:4;a:0:{}i:5;i:7288040;}i:156;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216156;i:4;a:0:{}i:5;i:7289264;}i:157;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216299;i:4;a:0:{}i:5;i:7291096;}i:159;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216483;i:4;a:0:{}i:5;i:7285416;}i:160;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216763;i:4;a:0:{}i:5;i:7287048;}i:162;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216802;i:4;a:0:{}i:5;i:7284856;}i:163;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228579;i:4;a:0:{}i:5;i:7294000;}i:165;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228653;i:4;a:0:{}i:5;i:7295264;}i:166;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2288;i:4;a:0:{}i:5;i:7296712;}i:168;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228927;i:4;a:0:{}i:5;i:7295240;}i:169;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.229217;i:4;a:0:{}i:5;i:7296872;}i:171;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.229253;i:4;a:0:{}i:5;i:7294640;}i:172;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241397;i:4;a:0:{}i:5;i:7302856;}i:174;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241482;i:4;a:0:{}i:5;i:7304080;}i:175;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241629;i:4;a:0:{}i:5;i:7305648;}i:177;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241756;i:4;a:0:{}i:5;i:7304584;}i:178;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.242061;i:4;a:0:{}i:5;i:7306216;}i:180;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.242118;i:4;a:0:{}i:5;i:7303344;}i:181;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.244994;i:4;a:0:{}i:5;i:7306664;}i:183;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245049;i:4;a:0:{}i:5;i:7307888;}i:184;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245185;i:4;a:0:{}i:5;i:7309040;}i:186;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245271;i:4;a:0:{}i:5;i:7311152;}i:187;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245522;i:4;a:0:{}i:5;i:7312784;}i:189;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245558;i:4;a:0:{}i:5;i:7309912;}i:190;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.257819;i:4;a:0:{}i:5;i:7321384;}i:192;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.257894;i:4;a:0:{}i:5;i:7322632;}i:193;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258052;i:4;a:0:{}i:5;i:7324200;}i:195;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.25818;i:4;a:0:{}i:5;i:7321504;}i:196;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258459;i:4;a:0:{}i:5;i:7323136;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2585;i:4;a:0:{}i:5;i:7320264;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.279671;i:4;a:0:{}i:5;i:7329400;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.27975;i:4;a:0:{}i:5;i:7330648;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.27992;i:4;a:0:{}i:5;i:7332216;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280065;i:4;a:0:{}i:5;i:7330688;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280396;i:4;a:0:{}i:5;i:7332320;}i:207;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280438;i:4;a:0:{}i:5;i:7329488;}i:208;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296009;i:4;a:0:{}i:5;i:7338648;}i:210;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296086;i:4;a:0:{}i:5;i:7339912;}i:211;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296237;i:4;a:0:{}i:5;i:7341488;}i:213;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296357;i:4;a:0:{}i:5;i:7339888;}i:214;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296651;i:4;a:0:{}i:5;i:7341520;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2967;i:4;a:0:{}i:5;i:7338688;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.30862;i:4;a:0:{}i:5;i:7346928;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308697;i:4;a:0:{}i:5;i:7348192;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308846;i:4;a:0:{}i:5;i:7349640;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308983;i:4;a:0:{}i:5;i:7348656;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.309268;i:4;a:0:{}i:5;i:7350288;}i:225;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.309308;i:4;a:0:{}i:5;i:7347456;}i:226;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312705;i:4;a:0:{}i:5;i:7355608;}i:228;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312746;i:4;a:0:{}i:5;i:7356832;}i:229;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312885;i:4;a:0:{}i:5;i:7358408;}i:231;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312984;i:4;a:0:{}i:5;i:7357400;}i:232;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.313249;i:4;a:0:{}i:5;i:7359888;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.313289;i:4;a:0:{}i:5;i:7357112;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.325808;i:4;a:0:{}i:5;i:7369560;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.325885;i:4;a:0:{}i:5;i:7370784;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326027;i:4;a:0:{}i:5;i:7372488;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326155;i:4;a:0:{}i:5;i:7369120;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326442;i:4;a:0:{}i:5;i:7370752;}i:243;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326478;i:4;a:0:{}i:5;i:7367920;}i:244;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.338993;i:4;a:0:{}i:5;i:7381424;}i:246;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339069;i:4;a:0:{}i:5;i:7382648;}i:247;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339226;i:4;a:0:{}i:5;i:7384352;}i:249;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339362;i:4;a:0:{}i:5;i:7380336;}i:250;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339689;i:4;a:0:{}i:5;i:7381968;}i:252;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339738;i:4;a:0:{}i:5;i:7379096;}i:253;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351527;i:4;a:0:{}i:5;i:7390600;}i:255;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351594;i:4;a:0:{}i:5;i:7391824;}i:256;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351743;i:4;a:0:{}i:5;i:7393392;}i:258;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351918;i:4;a:0:{}i:5;i:7398864;}i:259;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.352205;i:4;a:0:{}i:5;i:7400496;}i:261;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.352246;i:4;a:0:{}i:5;i:7397600;}i:262;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364266;i:4;a:0:{}i:5;i:7409024;}i:264;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364346;i:4;a:0:{}i:5;i:7410264;}i:265;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364498;i:4;a:0:{}i:5;i:7411952;}i:267;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364674;i:4;a:0:{}i:5;i:7409208;}i:268;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364956;i:4;a:0:{}i:5;i:7410840;}i:270;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364996;i:4;a:0:{}i:5;i:7407968;}i:271;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368241;i:4;a:0:{}i:5;i:7423360;}i:273;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368305;i:4;a:0:{}i:5;i:7424608;}i:274;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368471;i:4;a:0:{}i:5;i:7427072;}i:276;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.36861;i:4;a:0:{}i:5;i:7421376;}i:277;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.3689;i:4;a:0:{}i:5;i:7424904;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368939;i:4;a:0:{}i:5;i:7422088;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381178;i:4;a:0:{}i:5;i:7429312;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381253;i:4;a:0:{}i:5;i:7430560;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381399;i:4;a:0:{}i:5;i:7432000;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381517;i:4;a:0:{}i:5;i:7431616;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381829;i:4;a:0:{}i:5;i:7433248;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381867;i:4;a:0:{}i:5;i:7430416;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.393923;i:4;a:0:{}i:5;i:7441936;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.393998;i:4;a:0:{}i:5;i:7443160;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.39416;i:4;a:0:{}i:5;i:7444864;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394319;i:4;a:0:{}i:5;i:7441944;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394629;i:4;a:0:{}i:5;i:7443576;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.39467;i:4;a:0:{}i:5;i:7440680;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399669;i:4;a:0:{}i:5;i:7468952;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399744;i:4;a:0:{}i:5;i:7470192;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399945;i:4;a:0:{}i:5;i:7473160;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400196;i:4;a:0:{}i:5;i:7459936;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400584;i:4;a:0:{}i:5;i:7461568;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400628;i:4;a:0:{}i:5;i:7460016;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405064;i:4;a:0:{}i:5;i:7469104;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405156;i:4;a:0:{}i:5;i:7470328;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405338;i:4;a:0:{}i:5;i:7472288;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405481;i:4;a:0:{}i:5;i:7470376;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405871;i:4;a:0:{}i:5;i:7473952;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405927;i:4;a:0:{}i:5;i:7472456;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410406;i:4;a:0:{}i:5;i:7480632;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410503;i:4;a:0:{}i:5;i:7481856;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410717;i:4;a:0:{}i:5;i:7483688;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410846;i:4;a:0:{}i:5;i:7482384;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.411321;i:4;a:0:{}i:5;i:7484864;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.411373;i:4;a:0:{}i:5;i:7482088;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415332;i:4;a:0:{}i:5;i:7491168;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415398;i:4;a:0:{}i:5;i:7492392;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415571;i:4;a:0:{}i:5;i:7494352;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415707;i:4;a:0:{}i:5;i:7492440;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.416095;i:4;a:0:{}i:5;i:7496000;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.416159;i:4;a:0:{}i:5;i:7493184;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420605;i:4;a:0:{}i:5;i:7503640;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420671;i:4;a:0:{}i:5;i:7504888;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.42081;i:4;a:0:{}i:5;i:7506712;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420929;i:4;a:0:{}i:5;i:7504320;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.421228;i:4;a:0:{}i:5;i:7506792;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.421278;i:4;a:0:{}i:5;i:7504016;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425134;i:4;a:0:{}i:5;i:7512152;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425183;i:4;a:0:{}i:5;i:7513416;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425339;i:4;a:0:{}i:5;i:7515248;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425452;i:4;a:0:{}i:5;i:7513968;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425852;i:4;a:0:{}i:5;i:7517008;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425907;i:4;a:0:{}i:5;i:7514232;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430202;i:4;a:0:{}i:5;i:7522368;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430272;i:4;a:0:{}i:5;i:7523592;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430481;i:4;a:0:{}i:5;i:7525160;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430613;i:4;a:0:{}i:5;i:7524168;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.431014;i:4;a:0:{}i:5;i:7526640;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.431062;i:4;a:0:{}i:5;i:7523824;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435057;i:4;a:0:{}i:5;i:7532896;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435121;i:4;a:0:{}i:5;i:7534144;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435292;i:4;a:0:{}i:5;i:7536096;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435427;i:4;a:0:{}i:5;i:7534192;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435808;i:4;a:0:{}i:5;i:7537744;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435853;i:4;a:0:{}i:5;i:7534928;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439582;i:4;a:0:{}i:5;i:7543040;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439632;i:4;a:0:{}i:5;i:7544288;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439775;i:4;a:0:{}i:5;i:7545984;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439906;i:4;a:0:{}i:5;i:7544864;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.440194;i:4;a:0:{}i:5;i:7547872;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.440233;i:4;a:0:{}i:5;i:7545096;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.443929;i:4;a:0:{}i:5;i:7555440;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.443992;i:4;a:0:{}i:5;i:7556664;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444143;i:4;a:0:{}i:5;i:7558880;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444267;i:4;a:0:{}i:5;i:7556184;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444554;i:4;a:0:{}i:5;i:7559728;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444596;i:4;a:0:{}i:5;i:7556960;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448045;i:4;a:0:{}i:5;i:7575768;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448087;i:4;a:0:{}i:5;i:7577024;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448253;i:4;a:0:{}i:5;i:7581040;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448392;i:4;a:0:{}i:5;i:7572232;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448708;i:4;a:0:{}i:5;i:7580152;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448749;i:4;a:0:{}i:5;i:7577312;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452562;i:4;a:0:{}i:5;i:7591824;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452635;i:4;a:0:{}i:5;i:7593064;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452814;i:4;a:0:{}i:5;i:7595264;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452966;i:4;a:0:{}i:5;i:7590280;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.453291;i:4;a:0:{}i:5;i:7592744;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.453336;i:4;a:0:{}i:5;i:7589968;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457229;i:4;a:0:{}i:5;i:7607232;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457287;i:4;a:0:{}i:5;i:7608456;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457445;i:4;a:0:{}i:5;i:7610928;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457641;i:4;a:0:{}i:5;i:7604520;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457927;i:4;a:0:{}i:5;i:7608072;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.458889;i:4;a:0:{}i:5;i:7691904;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.506496;i:4;a:0:{}i:5;i:7732808;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.532169;i:4;a:0:{}i:5;i:7699752;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.532451;i:4;a:0:{}i:5;i:7701664;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533395;i:4;a:0:{}i:5;i:7702840;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533581;i:4;a:0:{}i:5;i:7705312;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533731;i:4;a:0:{}i:5;i:7705528;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533877;i:4;a:0:{}i:5;i:7708000;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533974;i:4;a:0:{}i:5;i:7707840;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534097;i:4;a:0:{}i:5;i:7710312;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534193;i:4;a:0:{}i:5;i:7710152;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534316;i:4;a:0:{}i:5;i:7712624;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53438;i:4;a:0:{}i:5;i:7711912;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5345;i:4;a:0:{}i:5;i:7714384;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534573;i:4;a:0:{}i:5;i:7713968;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534685;i:4;a:0:{}i:5;i:7715664;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534782;i:4;a:0:{}i:5;i:7716976;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534886;i:4;a:0:{}i:5;i:7718352;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534978;i:4;a:0:{}i:5;i:7719600;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535084;i:4;a:0:{}i:5;i:7721168;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535174;i:4;a:0:{}i:5;i:7722936;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535275;i:4;a:0:{}i:5;i:7724376;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535354;i:4;a:0:{}i:5;i:7725168;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535459;i:4;a:0:{}i:5;i:7726608;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535535;i:4;a:0:{}i:5;i:7726880;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535647;i:4;a:0:{}i:5;i:7728320;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535779;i:4;a:0:{}i:5;i:7730160;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53592;i:4;a:0:{}i:5;i:7732624;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536049;i:4;a:0:{}i:5;i:7732792;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536184;i:4;a:0:{}i:5;i:7735256;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536266;i:4;a:0:{}i:5;i:7735392;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536385;i:4;a:0:{}i:5;i:7737856;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536446;i:4;a:0:{}i:5;i:7737488;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53656;i:4;a:0:{}i:5;i:7739952;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53663;i:4;a:0:{}i:5;i:7740280;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536743;i:4;a:0:{}i:5;i:7741840;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536829;i:4;a:0:{}i:5;i:7742896;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536946;i:4;a:0:{}i:5;i:7744720;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53706;i:4;a:0:{}i:5;i:7745840;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53717;i:4;a:0:{}i:5;i:7747416;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537306;i:4;a:0:{}i:5;i:7749656;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53744;i:4;a:0:{}i:5;i:7752120;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537544;i:4;a:0:{}i:5;i:7751864;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537672;i:4;a:0:{}i:5;i:7754328;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537771;i:4;a:0:{}i:5;i:7754088;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5379;i:4;a:0:{}i:5;i:7756552;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537974;i:4;a:0:{}i:5;i:7756184;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538103;i:4;a:0:{}i:5;i:7758648;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538216;i:4;a:0:{}i:5;i:7759672;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538346;i:4;a:0:{}i:5;i:7761632;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538457;i:4;a:0:{}i:5;i:7761936;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53859;i:4;a:0:{}i:5;i:7763896;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538671;i:4;a:0:{}i:5;i:7764536;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538777;i:4;a:0:{}i:5;i:7766496;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538837;i:4;a:0:{}i:5;i:7766264;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538947;i:4;a:0:{}i:5;i:7768224;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539034;i:4;a:0:{}i:5;i:7768384;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539164;i:4;a:0:{}i:5;i:7770216;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539276;i:4;a:0:{}i:5;i:7771040;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539397;i:4;a:0:{}i:5;i:7773000;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539495;i:4;a:0:{}i:5;i:7773272;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539607;i:4;a:0:{}i:5;i:7775232;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539687;i:4;a:0:{}i:5;i:7775880;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539793;i:4;a:0:{}i:5;i:7777840;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539852;i:4;a:0:{}i:5;i:7777608;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539957;i:4;a:0:{}i:5;i:7779568;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540031;i:4;a:0:{}i:5;i:7796104;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540142;i:4;a:0:{}i:5;i:7797928;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540276;i:4;a:0:{}i:5;i:7798776;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540392;i:4;a:0:{}i:5;i:7800608;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540475;i:4;a:0:{}i:5;i:7801072;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540585;i:4;a:0:{}i:5;i:7802904;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540645;i:4;a:0:{}i:5;i:7803192;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540755;i:4;a:0:{}i:5;i:7805024;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540818;i:4;a:0:{}i:5;i:7805272;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540923;i:4;a:0:{}i:5;i:7806840;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541009;i:4;a:0:{}i:5;i:7808216;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541121;i:4;a:0:{}i:5;i:7810168;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5412;i:4;a:0:{}i:5;i:7810424;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541308;i:4;a:0:{}i:5;i:7812376;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541391;i:4;a:0:{}i:5;i:7812680;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541499;i:4;a:0:{}i:5;i:7814632;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541559;i:4;a:0:{}i:5;i:7814400;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541671;i:4;a:0:{}i:5;i:7816352;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541736;i:4;a:0:{}i:5;i:7816504;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541846;i:4;a:0:{}i:5;i:7818200;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541924;i:4;a:0:{}i:5;i:7818712;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542037;i:4;a:0:{}i:5;i:7820408;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542123;i:4;a:0:{}i:5;i:7820792;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542241;i:4;a:0:{}i:5;i:7822488;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54231;i:4;a:0:{}i:5;i:7822904;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54243;i:4;a:0:{}i:5;i:7825120;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542514;i:4;a:0:{}i:5;i:7825160;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542635;i:4;a:0:{}i:5;i:7827376;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542716;i:4;a:0:{}i:5;i:7829032;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54284;i:4;a:0:{}i:5;i:7831248;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542902;i:4;a:0:{}i:5;i:7830752;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54302;i:4;a:0:{}i:5;i:7832968;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543093;i:4;a:0:{}i:5;i:7832912;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543267;i:4;a:0:{}i:5;i:7836928;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543365;i:4;a:0:{}i:5;i:7835240;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543526;i:4;a:0:{}i:5;i:7839256;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543621;i:4;a:0:{}i:5;i:7837472;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543778;i:4;a:0:{}i:5;i:7841488;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543877;i:4;a:0:{}i:5;i:7840112;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54403;i:4;a:0:{}i:5;i:7844128;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544133;i:4;a:0:{}i:5;i:7842448;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544299;i:4;a:0:{}i:5;i:7846464;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544417;i:4;a:0:{}i:5;i:7844800;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544584;i:4;a:0:{}i:5;i:7848816;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544689;i:4;a:0:{}i:5;i:7847160;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544843;i:4;a:0:{}i:5;i:7851176;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54493;i:4;a:0:{}i:5;i:7849520;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54508;i:4;a:0:{}i:5;i:7853536;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545163;i:4;a:0:{}i:5;i:7852192;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545309;i:4;a:0:{}i:5;i:7856208;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545393;i:4;a:0:{}i:5;i:7854520;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545544;i:4;a:0:{}i:5;i:7858536;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545609;i:4;a:0:{}i:5;i:7856320;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545785;i:4;a:0:{}i:5;i:7860336;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545871;i:4;a:0:{}i:5;i:7857936;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546015;i:4;a:0:{}i:5;i:7860136;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54611;i:4;a:0:{}i:5;i:7860576;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54625;i:4;a:0:{}i:5;i:7863048;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546337;i:4;a:0:{}i:5;i:7862808;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546469;i:4;a:0:{}i:5;i:7865280;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54655;i:4;a:0:{}i:5;i:7865040;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546673;i:4;a:0:{}i:5;i:7867512;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546748;i:4;a:0:{}i:5;i:7866768;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54687;i:4;a:0:{}i:5;i:7869240;}i:609;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.547068;i:4;a:0:{}i:5;i:7875360;}i:610;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.547202;i:4;a:0:{}i:5;i:7877824;}}}";s:2:"db";s:125642:"a:1:{s:8:"messages";a:398:{i:15;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.001315;i:4;a:0:{}i:5;i:6982256;}i:16;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014141;i:4;a:0:{}i:5;i:6998960;}i:18;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014223;i:4;a:0:{}i:5;i:7000824;}i:19;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.014398;i:4;a:0:{}i:5;i:7003320;}i:21;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.015273;i:4;a:0:{}i:5;i:7020680;}i:22;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_purchase' AND `kcu`.`TABLE_NAME` = 'bundle_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.015689;i:4;a:0:{}i:5;i:7024208;}i:24;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.017816;i:4;a:0:{}i:5;i:7113384;}i:25;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.018504;i:4;a:0:{}i:5;i:7118336;}i:27;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.018562;i:4;a:0:{}i:5;i:7120424;}i:28;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.056945;i:4;a:0:{}i:5;i:7134912;}i:30;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057028;i:4;a:0:{}i:5;i:7136152;}i:31;a:6:{i:0;s:25:"SHOW CREATE TABLE `admin`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057207;i:4;a:0:{}i:5;i:7137968;}i:33;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057379;i:4;a:0:{}i:5;i:7134576;}i:34;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin' AND `kcu`.`TABLE_NAME` = 'admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.05769;i:4;a:0:{}i:5;i:7136208;}i:36;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.057726;i:4;a:0:{}i:5;i:7133752;}i:37;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061362;i:4;a:0:{}i:5;i:7145280;}i:39;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061422;i:4;a:0:{}i:5;i:7146504;}i:40;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061572;i:4;a:0:{}i:5;i:7148464;}i:42;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.061712;i:4;a:0:{}i:5;i:7145328;}i:43;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_login_session' AND `kcu`.`TABLE_NAME` = 'admin_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.06199;i:4;a:0:{}i:5;i:7147816;}i:45;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.062035;i:4;a:0:{}i:5;i:7145000;}i:46;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074285;i:4;a:0:{}i:5;i:7152272;}i:48;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074361;i:4;a:0:{}i:5;i:7153496;}i:49;a:6:{i:0;s:30:"SHOW CREATE TABLE `admin_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074508;i:4;a:0:{}i:5;i:7154808;}i:51;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074635;i:4;a:0:{}i:5;i:7154536;}i:52;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'admin_role' AND `kcu`.`TABLE_NAME` = 'admin_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074932;i:4;a:0:{}i:5;i:7156168;}i:54;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.074974;i:4;a:0:{}i:5;i:7153336;}i:55;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087656;i:4;a:0:{}i:5;i:7166752;}i:57;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087726;i:4;a:0:{}i:5;i:7168016;}i:58;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.087894;i:4;a:0:{}i:5;i:7170488;}i:60;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.088038;i:4;a:0:{}i:5;i:7165816;}i:61;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'advance_genre_lookup' AND `kcu`.`TABLE_NAME` = 'advance_genre_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.096429;i:4;a:0:{}i:5;i:7170024;}i:63;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.096515;i:4;a:0:{}i:5;i:7167208;}i:64;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.10013;i:4;a:0:{}i:5;i:7177680;}i:66;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.1002;i:4;a:0:{}i:5;i:7184536;}i:67;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100366;i:4;a:0:{}i:5;i:7186232;}i:69;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100495;i:4;a:0:{}i:5;i:7183944;}i:70;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_log' AND `kcu`.`TABLE_NAME` = 'audit_log'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.100893;i:4;a:0:{}i:5;i:7186424;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.10094;i:4;a:0:{}i:5;i:7183648;}i:73;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115589;i:4;a:0:{}i:5;i:7187968;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115665;i:4;a:0:{}i:5;i:7189192;}i:76;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115822;i:4;a:0:{}i:5;i:7190568;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.115916;i:4;a:0:{}i:5;i:7191872;}i:79;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_assignment' AND `kcu`.`TABLE_NAME` = 'auth_assignment'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.136013;i:4;a:0:{}i:5;i:7194360;}i:81;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.136096;i:4;a:0:{}i:5;i:7191544;}i:82;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140009;i:4;a:0:{}i:5;i:7199704;}i:84;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140079;i:4;a:0:{}i:5;i:7200928;}i:85;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140251;i:4;a:0:{}i:5;i:7202496;}i:87;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.140363;i:4;a:0:{}i:5;i:7201472;}i:88;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item' AND `kcu`.`TABLE_NAME` = 'auth_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.151271;i:4;a:0:{}i:5;i:7203960;}i:90;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.151351;i:4;a:0:{}i:5;i:7201504;}i:91;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155434;i:4;a:0:{}i:5;i:7204864;}i:93;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155499;i:4;a:0:{}i:5;i:7206088;}i:94;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155647;i:4;a:0:{}i:5;i:7207528;}i:96;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.155727;i:4;a:0:{}i:5;i:7209296;}i:97;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_item_child' AND `kcu`.`TABLE_NAME` = 'auth_item_child'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.156048;i:4;a:0:{}i:5;i:7212304;}i:99;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.156087;i:4;a:0:{}i:5;i:7209488;}i:100;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.159883;i:4;a:0:{}i:5;i:7214704;}i:102;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.159941;i:4;a:0:{}i:5;i:7215928;}i:103;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_rule`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160073;i:4;a:0:{}i:5;i:7217176;}i:105;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160162;i:4;a:0:{}i:5;i:7218136;}i:106;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'auth_rule' AND `kcu`.`TABLE_NAME` = 'auth_rule'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160467;i:4;a:0:{}i:5;i:7219768;}i:108;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.160504;i:4;a:0:{}i:5;i:7217192;}i:109;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.164877;i:4;a:0:{}i:5;i:7226336;}i:111;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.164945;i:4;a:0:{}i:5;i:7227576;}i:112;a:6:{i:0;s:26:"SHOW CREATE TABLE `bundle`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165108;i:4;a:0:{}i:5;i:7229136;}i:114;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.16523;i:4;a:0:{}i:5;i:7227624;}i:115;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle' AND `kcu`.`TABLE_NAME` = 'bundle'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165562;i:4;a:0:{}i:5;i:7229256;}i:117;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.165599;i:4;a:0:{}i:5;i:7226384;}i:118;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178403;i:4;a:0:{}i:5;i:7234520;}i:120;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.17848;i:4;a:0:{}i:5;i:7235768;}i:121;a:6:{i:0;s:33:"SHOW CREATE TABLE `bundle_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178653;i:4;a:0:{}i:5;i:7237336;}i:123;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.178788;i:4;a:0:{}i:5;i:7236328;}i:124;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'bundle_reward' AND `kcu`.`TABLE_NAME` = 'bundle_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.179086;i:4;a:0:{}i:5;i:7237960;}i:126;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.179122;i:4;a:0:{}i:5;i:7235088;}i:127;a:6:{i:0;s:32:"SHOW FULL COLUMNS FROM `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201282;i:4;a:0:{}i:5;i:7248512;}i:129;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201367;i:4;a:0:{}i:5;i:7253832;}i:130;a:6:{i:0;s:27:"SHOW CREATE TABLE `country`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201555;i:4;a:0:{}i:5;i:7255648;}i:132;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.201695;i:4;a:0:{}i:5;i:7251584;}i:133;a:6:{i:0;s:770:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'country' AND `kcu`.`TABLE_NAME` = 'country'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.202054;i:4;a:0:{}i:5;i:7253216;}i:135;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.202096;i:4;a:0:{}i:5;i:7250320;}i:136;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206287;i:4;a:0:{}i:5;i:7258488;}i:138;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206344;i:4;a:0:{}i:5;i:7259728;}i:139;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206515;i:4;a:0:{}i:5;i:7261288;}i:141;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.206654;i:4;a:0:{}i:5;i:7260312;}i:142;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'credit' AND `kcu`.`TABLE_NAME` = 'credit'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.207021;i:4;a:0:{}i:5;i:7262776;}i:144;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.207061;i:4;a:0:{}i:5;i:7259960;}i:145;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211473;i:4;a:0:{}i:5;i:7272424;}i:147;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211535;i:4;a:0:{}i:5;i:7273648;}i:148;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.211712;i:4;a:0:{}i:5;i:7275472;}i:150;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.21189;i:4;a:0:{}i:5;i:7271920;}i:151;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'export_job' AND `kcu`.`TABLE_NAME` = 'export_job'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.212234;i:4;a:0:{}i:5;i:7274400;}i:153;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.212277;i:4;a:0:{}i:5;i:7271624;}i:154;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216105;i:4;a:0:{}i:5;i:7288040;}i:156;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216156;i:4;a:0:{}i:5;i:7289264;}i:157;a:6:{i:0;s:37:"SHOW CREATE TABLE `instrument_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216299;i:4;a:0:{}i:5;i:7291096;}i:159;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216483;i:4;a:0:{}i:5;i:7285416;}i:160;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instrument_lookup' AND `kcu`.`TABLE_NAME` = 'instrument_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216763;i:4;a:0:{}i:5;i:7287048;}i:162;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.216802;i:4;a:0:{}i:5;i:7284856;}i:163;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228579;i:4;a:0:{}i:5;i:7294000;}i:165;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228653;i:4;a:0:{}i:5;i:7295264;}i:166;a:6:{i:0;s:40:"SHOW CREATE TABLE `invite_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2288;i:4;a:0:{}i:5;i:7296712;}i:168;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.228927;i:4;a:0:{}i:5;i:7295240;}i:169;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'invite_reward_lookup' AND `kcu`.`TABLE_NAME` = 'invite_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.229217;i:4;a:0:{}i:5;i:7296872;}i:171;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.229253;i:4;a:0:{}i:5;i:7294640;}i:172;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241397;i:4;a:0:{}i:5;i:7302856;}i:174;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241482;i:4;a:0:{}i:5;i:7304080;}i:175;a:6:{i:0;s:28:"SHOW CREATE TABLE `language`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241629;i:4;a:0:{}i:5;i:7305648;}i:177;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.241756;i:4;a:0:{}i:5;i:7304584;}i:178;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'language' AND `kcu`.`TABLE_NAME` = 'language'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.242061;i:4;a:0:{}i:5;i:7306216;}i:180;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.242118;i:4;a:0:{}i:5;i:7303344;}i:181;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.244994;i:4;a:0:{}i:5;i:7306664;}i:183;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245049;i:4;a:0:{}i:5;i:7307888;}i:184;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245185;i:4;a:0:{}i:5;i:7309040;}i:186;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245271;i:4;a:0:{}i:5;i:7311152;}i:187;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245522;i:4;a:0:{}i:5;i:7312784;}i:189;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.245558;i:4;a:0:{}i:5;i:7309912;}i:190;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.257819;i:4;a:0:{}i:5;i:7321384;}i:192;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.257894;i:4;a:0:{}i:5;i:7322632;}i:193;a:6:{i:0;s:33:"SHOW CREATE TABLE `notice_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258052;i:4;a:0:{}i:5;i:7324200;}i:195;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.25818;i:4;a:0:{}i:5;i:7321504;}i:196;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notice_lookup' AND `kcu`.`TABLE_NAME` = 'notice_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.258459;i:4;a:0:{}i:5;i:7323136;}i:198;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2585;i:4;a:0:{}i:5;i:7320264;}i:199;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.279671;i:4;a:0:{}i:5;i:7329400;}i:201;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.27975;i:4;a:0:{}i:5;i:7330648;}i:202;a:6:{i:0;s:33:"SHOW CREATE TABLE `rarity_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.27992;i:4;a:0:{}i:5;i:7332216;}i:204;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280065;i:4;a:0:{}i:5;i:7330688;}i:205;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'rarity_lookup' AND `kcu`.`TABLE_NAME` = 'rarity_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280396;i:4;a:0:{}i:5;i:7332320;}i:207;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.280438;i:4;a:0:{}i:5;i:7329488;}i:208;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296009;i:4;a:0:{}i:5;i:7338648;}i:210;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296086;i:4;a:0:{}i:5;i:7339912;}i:211;a:6:{i:0;s:42:"SHOW CREATE TABLE `referral_reward_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296237;i:4;a:0:{}i:5;i:7341488;}i:213;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296357;i:4;a:0:{}i:5;i:7339888;}i:214;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'referral_reward_lookup' AND `kcu`.`TABLE_NAME` = 'referral_reward_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.296651;i:4;a:0:{}i:5;i:7341520;}i:216;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.2967;i:4;a:0:{}i:5;i:7338688;}i:217;a:6:{i:0;s:47:"SHOW FULL COLUMNS FROM `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.30862;i:4;a:0:{}i:5;i:7346928;}i:219;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308697;i:4;a:0:{}i:5;i:7348192;}i:220;a:6:{i:0;s:42:"SHOW CREATE TABLE `shop_card_items_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308846;i:4;a:0:{}i:5;i:7349640;}i:222;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.308983;i:4;a:0:{}i:5;i:7348656;}i:223;a:6:{i:0;s:800:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_items_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_items_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.309268;i:4;a:0:{}i:5;i:7350288;}i:225;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.309308;i:4;a:0:{}i:5;i:7347456;}i:226;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312705;i:4;a:0:{}i:5;i:7355608;}i:228;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312746;i:4;a:0:{}i:5;i:7356832;}i:229;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312885;i:4;a:0:{}i:5;i:7358408;}i:231;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.312984;i:4;a:0:{}i:5;i:7357400;}i:232;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_card_lookup' AND `kcu`.`TABLE_NAME` = 'shop_card_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.313249;i:4;a:0:{}i:5;i:7359888;}i:234;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.313289;i:4;a:0:{}i:5;i:7357112;}i:235;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.325808;i:4;a:0:{}i:5;i:7369560;}i:237;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.325885;i:4;a:0:{}i:5;i:7370784;}i:238;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_item_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326027;i:4;a:0:{}i:5;i:7372488;}i:240;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326155;i:4;a:0:{}i:5;i:7369120;}i:241;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'shop_item_lookup' AND `kcu`.`TABLE_NAME` = 'shop_item_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326442;i:4;a:0:{}i:5;i:7370752;}i:243;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.326478;i:4;a:0:{}i:5;i:7367920;}i:244;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.338993;i:4;a:0:{}i:5;i:7381424;}i:246;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339069;i:4;a:0:{}i:5;i:7382648;}i:247;a:6:{i:0;s:38:"SHOW CREATE TABLE `social_task_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339226;i:4;a:0:{}i:5;i:7384352;}i:249;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339362;i:4;a:0:{}i:5;i:7380336;}i:250;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'social_task_lookup' AND `kcu`.`TABLE_NAME` = 'social_task_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339689;i:4;a:0:{}i:5;i:7381968;}i:252;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.339738;i:4;a:0:{}i:5;i:7379096;}i:253;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351527;i:4;a:0:{}i:5;i:7390600;}i:255;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351594;i:4;a:0:{}i:5;i:7391824;}i:256;a:6:{i:0;s:31:"SHOW CREATE TABLE `song_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351743;i:4;a:0:{}i:5;i:7393392;}i:258;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.351918;i:4;a:0:{}i:5;i:7398864;}i:259;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'song_lookup' AND `kcu`.`TABLE_NAME` = 'song_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.352205;i:4;a:0:{}i:5;i:7400496;}i:261;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.352246;i:4;a:0:{}i:5;i:7397600;}i:262;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364266;i:4;a:0:{}i:5;i:7409024;}i:264;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364346;i:4;a:0:{}i:5;i:7410264;}i:265;a:6:{i:0;s:25:"SHOW CREATE TABLE `stage`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364498;i:4;a:0:{}i:5;i:7411952;}i:267;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364674;i:4;a:0:{}i:5;i:7409208;}i:268;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage' AND `kcu`.`TABLE_NAME` = 'stage'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364956;i:4;a:0:{}i:5;i:7410840;}i:270;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.364996;i:4;a:0:{}i:5;i:7407968;}i:271;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368241;i:4;a:0:{}i:5;i:7423360;}i:273;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368305;i:4;a:0:{}i:5;i:7424608;}i:274;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368471;i:4;a:0:{}i:5;i:7427072;}i:276;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.36861;i:4;a:0:{}i:5;i:7421376;}i:277;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'stage_purchase' AND `kcu`.`TABLE_NAME` = 'stage_purchase'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.3689;i:4;a:0:{}i:5;i:7424904;}i:279;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.368939;i:4;a:0:{}i:5;i:7422088;}i:280;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381178;i:4;a:0:{}i:5;i:7429312;}i:282;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381253;i:4;a:0:{}i:5;i:7430560;}i:283;a:6:{i:0;s:34:"SHOW CREATE TABLE `system_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381399;i:4;a:0:{}i:5;i:7432000;}i:285;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381517;i:4;a:0:{}i:5;i:7431616;}i:286;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'system_setting' AND `kcu`.`TABLE_NAME` = 'system_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381829;i:4;a:0:{}i:5;i:7433248;}i:288;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.381867;i:4;a:0:{}i:5;i:7430416;}i:289;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.393923;i:4;a:0:{}i:5;i:7441936;}i:291;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.393998;i:4;a:0:{}i:5;i:7443160;}i:292;a:6:{i:0;s:38:"SHOW CREATE TABLE `translation_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.39416;i:4;a:0:{}i:5;i:7444864;}i:294;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394319;i:4;a:0:{}i:5;i:7441944;}i:295;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'translation_lookup' AND `kcu`.`TABLE_NAME` = 'translation_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.394629;i:4;a:0:{}i:5;i:7443576;}i:297;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.39467;i:4;a:0:{}i:5;i:7440680;}i:298;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399669;i:4;a:0:{}i:5;i:7468952;}i:300;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399744;i:4;a:0:{}i:5;i:7470192;}i:301;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.399945;i:4;a:0:{}i:5;i:7473160;}i:303;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400196;i:4;a:0:{}i:5;i:7459936;}i:304;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400584;i:4;a:0:{}i:5;i:7461568;}i:306;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.400628;i:4;a:0:{}i:5;i:7460016;}i:307;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405064;i:4;a:0:{}i:5;i:7469104;}i:309;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405156;i:4;a:0:{}i:5;i:7470328;}i:310;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405338;i:4;a:0:{}i:5;i:7472288;}i:312;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405481;i:4;a:0:{}i:5;i:7470376;}i:313;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_invite_reward' AND `kcu`.`TABLE_NAME` = 'user_invite_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405871;i:4;a:0:{}i:5;i:7473952;}i:315;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.405927;i:4;a:0:{}i:5;i:7472456;}i:316;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410406;i:4;a:0:{}i:5;i:7480632;}i:318;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410503;i:4;a:0:{}i:5;i:7481856;}i:319;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410717;i:4;a:0:{}i:5;i:7483688;}i:321;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.410846;i:4;a:0:{}i:5;i:7482384;}i:322;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_login_session' AND `kcu`.`TABLE_NAME` = 'user_login_session'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.411321;i:4;a:0:{}i:5;i:7484864;}i:324;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.411373;i:4;a:0:{}i:5;i:7482088;}i:325;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415332;i:4;a:0:{}i:5;i:7491168;}i:327;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415398;i:4;a:0:{}i:5;i:7492392;}i:328;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415571;i:4;a:0:{}i:5;i:7494352;}i:330;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.415707;i:4;a:0:{}i:5;i:7492440;}i:331;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notice_reward' AND `kcu`.`TABLE_NAME` = 'user_notice_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.416095;i:4;a:0:{}i:5;i:7496000;}i:333;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.416159;i:4;a:0:{}i:5;i:7493184;}i:334;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420605;i:4;a:0:{}i:5;i:7503640;}i:336;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420671;i:4;a:0:{}i:5;i:7504888;}i:337;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.42081;i:4;a:0:{}i:5;i:7506712;}i:339;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.420929;i:4;a:0:{}i:5;i:7504320;}i:340;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral' AND `kcu`.`TABLE_NAME` = 'user_referral'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.421228;i:4;a:0:{}i:5;i:7506792;}i:342;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.421278;i:4;a:0:{}i:5;i:7504016;}i:343;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425134;i:4;a:0:{}i:5;i:7512152;}i:345;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425183;i:4;a:0:{}i:5;i:7513416;}i:346;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425339;i:4;a:0:{}i:5;i:7515248;}i:348;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425452;i:4;a:0:{}i:5;i:7513968;}i:349;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_referral_reward' AND `kcu`.`TABLE_NAME` = 'user_referral_reward'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425852;i:4;a:0:{}i:5;i:7517008;}i:351;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.425907;i:4;a:0:{}i:5;i:7514232;}i:352;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430202;i:4;a:0:{}i:5;i:7522368;}i:354;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430272;i:4;a:0:{}i:5;i:7523592;}i:355;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430481;i:4;a:0:{}i:5;i:7525160;}i:357;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.430613;i:4;a:0:{}i:5;i:7524168;}i:358;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_reset_card' AND `kcu`.`TABLE_NAME` = 'user_reset_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.431014;i:4;a:0:{}i:5;i:7526640;}i:360;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.431062;i:4;a:0:{}i:5;i:7523824;}i:361;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435057;i:4;a:0:{}i:5;i:7532896;}i:363;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435121;i:4;a:0:{}i:5;i:7534144;}i:364;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435292;i:4;a:0:{}i:5;i:7536096;}i:366;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435427;i:4;a:0:{}i:5;i:7534192;}i:367;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_card' AND `kcu`.`TABLE_NAME` = 'user_shop_card'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435808;i:4;a:0:{}i:5;i:7537744;}i:369;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.435853;i:4;a:0:{}i:5;i:7534928;}i:370;a:6:{i:0;s:39:"SHOW FULL COLUMNS FROM `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439582;i:4;a:0:{}i:5;i:7543040;}i:372;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439632;i:4;a:0:{}i:5;i:7544288;}i:373;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439775;i:4;a:0:{}i:5;i:7545984;}i:375;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.439906;i:4;a:0:{}i:5;i:7544864;}i:376;a:6:{i:0;s:784:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_shop_item' AND `kcu`.`TABLE_NAME` = 'user_shop_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.440194;i:4;a:0:{}i:5;i:7547872;}i:378;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.440233;i:4;a:0:{}i:5;i:7545096;}i:379;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.443929;i:4;a:0:{}i:5;i:7555440;}i:381;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.443992;i:4;a:0:{}i:5;i:7556664;}i:382;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444143;i:4;a:0:{}i:5;i:7558880;}i:384;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444267;i:4;a:0:{}i:5;i:7556184;}i:385;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_social_task' AND `kcu`.`TABLE_NAME` = 'user_social_task'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444554;i:4;a:0:{}i:5;i:7559728;}i:387;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.444596;i:4;a:0:{}i:5;i:7556960;}i:388;a:6:{i:0;s:50:"SHOW FULL COLUMNS FROM `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448045;i:4;a:0:{}i:5;i:7575768;}i:390;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448087;i:4;a:0:{}i:5;i:7577024;}i:391;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448253;i:4;a:0:{}i:5;i:7581040;}i:393;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448392;i:4;a:0:{}i:5;i:7572232;}i:394;a:6:{i:0;s:806:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_song_selected_lookup' AND `kcu`.`TABLE_NAME` = 'user_song_selected_lookup'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448708;i:4;a:0:{}i:5;i:7580152;}i:396;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.448749;i:4;a:0:{}i:5;i:7577312;}i:397;a:6:{i:0;s:31:"SHOW FULL COLUMNS FROM `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452562;i:4;a:0:{}i:5;i:7591824;}i:399;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452635;i:4;a:0:{}i:5;i:7593064;}i:400;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452814;i:4;a:0:{}i:5;i:7595264;}i:402;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.452966;i:4;a:0:{}i:5;i:7590280;}i:403;a:6:{i:0;s:768:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet' AND `kcu`.`TABLE_NAME` = 'wallet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.453291;i:4;a:0:{}i:5;i:7592744;}i:405;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.453336;i:4;a:0:{}i:5;i:7589968;}i:406;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457229;i:4;a:0:{}i:5;i:7607232;}i:408;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457287;i:4;a:0:{}i:5;i:7608456;}i:409;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457445;i:4;a:0:{}i:5;i:7610928;}i:411;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457641;i:4;a:0:{}i:5;i:7604520;}i:412;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'wallet_transaction' AND `kcu`.`TABLE_NAME` = 'wallet_transaction'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.457927;i:4;a:0:{}i:5;i:7608072;}i:414;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.458889;i:4;a:0:{}i:5;i:7691904;}i:415;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.506496;i:4;a:0:{}i:5;i:7732808;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.532169;i:4;a:0:{}i:5;i:7699752;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `admin_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.532451;i:4;a:0:{}i:5;i:7701664;}i:420;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533395;i:4;a:0:{}i:5;i:7702840;}i:421;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533581;i:4;a:0:{}i:5;i:7705312;}i:423;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533731;i:4;a:0:{}i:5;i:7705528;}i:424;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533877;i:4;a:0:{}i:5;i:7708000;}i:426;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.533974;i:4;a:0:{}i:5;i:7707840;}i:427;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534097;i:4;a:0:{}i:5;i:7710312;}i:429;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534193;i:4;a:0:{}i:5;i:7710152;}i:430;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534316;i:4;a:0:{}i:5;i:7712624;}i:432;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53438;i:4;a:0:{}i:5;i:7711912;}i:433;a:6:{i:0;s:40:"SHOW CREATE TABLE `advance_genre_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5345;i:4;a:0:{}i:5;i:7714384;}i:435;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534573;i:4;a:0:{}i:5;i:7713968;}i:436;a:6:{i:0;s:29:"SHOW CREATE TABLE `audit_log`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534685;i:4;a:0:{}i:5;i:7715664;}i:438;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534782;i:4;a:0:{}i:5;i:7716976;}i:439;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_assignment`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534886;i:4;a:0:{}i:5;i:7718352;}i:441;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.534978;i:4;a:0:{}i:5;i:7719600;}i:442;a:6:{i:0;s:29:"SHOW CREATE TABLE `auth_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535084;i:4;a:0:{}i:5;i:7721168;}i:444;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535174;i:4;a:0:{}i:5;i:7722936;}i:445;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535275;i:4;a:0:{}i:5;i:7724376;}i:447;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535354;i:4;a:0:{}i:5;i:7725168;}i:448;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535459;i:4;a:0:{}i:5;i:7726608;}i:450;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535535;i:4;a:0:{}i:5;i:7726880;}i:451;a:6:{i:0;s:35:"SHOW CREATE TABLE `auth_item_child`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535647;i:4;a:0:{}i:5;i:7728320;}i:453;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.535779;i:4;a:0:{}i:5;i:7730160;}i:454;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53592;i:4;a:0:{}i:5;i:7732624;}i:456;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536049;i:4;a:0:{}i:5;i:7732792;}i:457;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536184;i:4;a:0:{}i:5;i:7735256;}i:459;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536266;i:4;a:0:{}i:5;i:7735392;}i:460;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536385;i:4;a:0:{}i:5;i:7737856;}i:462;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536446;i:4;a:0:{}i:5;i:7737488;}i:463;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53656;i:4;a:0:{}i:5;i:7739952;}i:465;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53663;i:4;a:0:{}i:5;i:7740280;}i:466;a:6:{i:0;s:26:"SHOW CREATE TABLE `credit`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536743;i:4;a:0:{}i:5;i:7741840;}i:468;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536829;i:4;a:0:{}i:5;i:7742896;}i:469;a:6:{i:0;s:30:"SHOW CREATE TABLE `export_job`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.536946;i:4;a:0:{}i:5;i:7744720;}i:471;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53706;i:4;a:0:{}i:5;i:7745840;}i:472;a:6:{i:0;s:36:"SHOW CREATE TABLE `shop_card_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53717;i:4;a:0:{}i:5;i:7747416;}i:474;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537306;i:4;a:0:{}i:5;i:7749656;}i:475;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53744;i:4;a:0:{}i:5;i:7752120;}i:477;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537544;i:4;a:0:{}i:5;i:7751864;}i:478;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537672;i:4;a:0:{}i:5;i:7754328;}i:480;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537771;i:4;a:0:{}i:5;i:7754088;}i:481;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5379;i:4;a:0:{}i:5;i:7756552;}i:483;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.537974;i:4;a:0:{}i:5;i:7756184;}i:484;a:6:{i:0;s:34:"SHOW CREATE TABLE `stage_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538103;i:4;a:0:{}i:5;i:7758648;}i:486;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538216;i:4;a:0:{}i:5;i:7759672;}i:487;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538346;i:4;a:0:{}i:5;i:7761632;}i:489;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538457;i:4;a:0:{}i:5;i:7761936;}i:490;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.53859;i:4;a:0:{}i:5;i:7763896;}i:492;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538671;i:4;a:0:{}i:5;i:7764536;}i:493;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538777;i:4;a:0:{}i:5;i:7766496;}i:495;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538837;i:4;a:0:{}i:5;i:7766264;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_invite_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.538947;i:4;a:0:{}i:5;i:7768224;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539034;i:4;a:0:{}i:5;i:7768384;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_login_session`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539164;i:4;a:0:{}i:5;i:7770216;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539276;i:4;a:0:{}i:5;i:7771040;}i:502;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539397;i:4;a:0:{}i:5;i:7773000;}i:504;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539495;i:4;a:0:{}i:5;i:7773272;}i:505;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539607;i:4;a:0:{}i:5;i:7775232;}i:507;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539687;i:4;a:0:{}i:5;i:7775880;}i:508;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539793;i:4;a:0:{}i:5;i:7777840;}i:510;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539852;i:4;a:0:{}i:5;i:7777608;}i:511;a:6:{i:0;s:38:"SHOW CREATE TABLE `user_notice_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.539957;i:4;a:0:{}i:5;i:7779568;}i:513;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540031;i:4;a:0:{}i:5;i:7796104;}i:514;a:6:{i:0;s:33:"SHOW CREATE TABLE `user_referral`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540142;i:4;a:0:{}i:5;i:7797928;}i:516;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540276;i:4;a:0:{}i:5;i:7798776;}i:517;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540392;i:4;a:0:{}i:5;i:7800608;}i:519;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540475;i:4;a:0:{}i:5;i:7801072;}i:520;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540585;i:4;a:0:{}i:5;i:7802904;}i:522;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540645;i:4;a:0:{}i:5;i:7803192;}i:523;a:6:{i:0;s:40:"SHOW CREATE TABLE `user_referral_reward`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540755;i:4;a:0:{}i:5;i:7805024;}i:525;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540818;i:4;a:0:{}i:5;i:7805272;}i:526;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_reset_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.540923;i:4;a:0:{}i:5;i:7806840;}i:528;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541009;i:4;a:0:{}i:5;i:7808216;}i:529;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541121;i:4;a:0:{}i:5;i:7810168;}i:531;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.5412;i:4;a:0:{}i:5;i:7810424;}i:532;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541308;i:4;a:0:{}i:5;i:7812376;}i:534;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541391;i:4;a:0:{}i:5;i:7812680;}i:535;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541499;i:4;a:0:{}i:5;i:7814632;}i:537;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541559;i:4;a:0:{}i:5;i:7814400;}i:538;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_card`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541671;i:4;a:0:{}i:5;i:7816352;}i:540;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541736;i:4;a:0:{}i:5;i:7816504;}i:541;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541846;i:4;a:0:{}i:5;i:7818200;}i:543;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.541924;i:4;a:0:{}i:5;i:7818712;}i:544;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542037;i:4;a:0:{}i:5;i:7820408;}i:546;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542123;i:4;a:0:{}i:5;i:7820792;}i:547;a:6:{i:0;s:34:"SHOW CREATE TABLE `user_shop_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542241;i:4;a:0:{}i:5;i:7822488;}i:549;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54231;i:4;a:0:{}i:5;i:7822904;}i:550;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54243;i:4;a:0:{}i:5;i:7825120;}i:552;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542514;i:4;a:0:{}i:5;i:7825160;}i:553;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542635;i:4;a:0:{}i:5;i:7827376;}i:555;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542716;i:4;a:0:{}i:5;i:7829032;}i:556;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54284;i:4;a:0:{}i:5;i:7831248;}i:558;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.542902;i:4;a:0:{}i:5;i:7830752;}i:559;a:6:{i:0;s:36:"SHOW CREATE TABLE `user_social_task`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54302;i:4;a:0:{}i:5;i:7832968;}i:561;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543093;i:4;a:0:{}i:5;i:7832912;}i:562;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543267;i:4;a:0:{}i:5;i:7836928;}i:564;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543365;i:4;a:0:{}i:5;i:7835240;}i:565;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543526;i:4;a:0:{}i:5;i:7839256;}i:567;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543621;i:4;a:0:{}i:5;i:7837472;}i:568;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543778;i:4;a:0:{}i:5;i:7841488;}i:570;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.543877;i:4;a:0:{}i:5;i:7840112;}i:571;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54403;i:4;a:0:{}i:5;i:7844128;}i:573;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544133;i:4;a:0:{}i:5;i:7842448;}i:574;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544299;i:4;a:0:{}i:5;i:7846464;}i:576;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544417;i:4;a:0:{}i:5;i:7844800;}i:577;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544584;i:4;a:0:{}i:5;i:7848816;}i:579;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544689;i:4;a:0:{}i:5;i:7847160;}i:580;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.544843;i:4;a:0:{}i:5;i:7851176;}i:582;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54493;i:4;a:0:{}i:5;i:7849520;}i:583;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54508;i:4;a:0:{}i:5;i:7853536;}i:585;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545163;i:4;a:0:{}i:5;i:7852192;}i:586;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545309;i:4;a:0:{}i:5;i:7856208;}i:588;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545393;i:4;a:0:{}i:5;i:7854520;}i:589;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545544;i:4;a:0:{}i:5;i:7858536;}i:591;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545609;i:4;a:0:{}i:5;i:7856320;}i:592;a:6:{i:0;s:45:"SHOW CREATE TABLE `user_song_selected_lookup`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545785;i:4;a:0:{}i:5;i:7860336;}i:594;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.545871;i:4;a:0:{}i:5;i:7857936;}i:595;a:6:{i:0;s:26:"SHOW CREATE TABLE `wallet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546015;i:4;a:0:{}i:5;i:7860136;}i:597;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54611;i:4;a:0:{}i:5;i:7860576;}i:598;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54625;i:4;a:0:{}i:5;i:7863048;}i:600;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546337;i:4;a:0:{}i:5;i:7862808;}i:601;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546469;i:4;a:0:{}i:5;i:7865280;}i:603;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54655;i:4;a:0:{}i:5;i:7865040;}i:604;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546673;i:4;a:0:{}i:5;i:7867512;}i:606;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.546748;i:4;a:0:{}i:5;i:7866768;}i:607;a:6:{i:0;s:38:"SHOW CREATE TABLE `wallet_transaction`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.54687;i:4;a:0:{}i:5;i:7869240;}i:609;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.547068;i:4;a:0:{}i:5;i:7875360;}i:610;a:6:{i:0;s:35:"SHOW CREATE TABLE `bundle_purchase`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532788.547202;i:4;a:0:{}i:5;i:7877824;}}}";s:5:"event";s:7019:"a:40:{i:0;a:5:{s:4:"time";d:1761532787.937287;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1761532787.940912;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1761532787.940921;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:3;a:5:{s:4:"time";d:1761532787.946311;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:4;a:5:{s:4:"time";d:1761532787.974825;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:5;a:5:{s:4:"time";d:1761532788.001237;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1761532788.016802;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:7;a:5:{s:4:"time";d:1761532788.458789;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\db\ActiveRecord";}i:8;a:5:{s:4:"time";d:1761532788.57234;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:9;a:5:{s:4:"time";d:1761532788.620996;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:10;a:5:{s:4:"time";d:1761532788.621658;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:11;a:5:{s:4:"time";d:1761532788.625491;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:12;a:5:{s:4:"time";d:1761532788.625614;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:13;a:5:{s:4:"time";d:1761532788.632576;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:14;a:5:{s:4:"time";d:1761532788.633368;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:15;a:5:{s:4:"time";d:1761532788.674024;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:16;a:5:{s:4:"time";d:1761532788.674411;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:17;a:5:{s:4:"time";d:1761532788.6749;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:18;a:5:{s:4:"time";d:1761532788.674936;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1761532788.675107;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1761532788.675532;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:21;a:5:{s:4:"time";d:1761532788.675658;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:22;a:5:{s:4:"time";d:1761532788.676006;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:1761532788.676816;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:1761532788.67685;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:1761532788.677282;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:26;a:5:{s:4:"time";d:1761532788.677288;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:27;a:5:{s:4:"time";d:1761532788.677342;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:28;a:5:{s:4:"time";d:1761532788.678103;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1761532788.67818;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:1761532788.678345;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:1761532788.678353;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:32;a:5:{s:4:"time";d:1761532788.678378;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1761532788.678387;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:34;a:5:{s:4:"time";d:1761532788.678393;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:35;a:5:{s:4:"time";d:1761532788.678397;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:36;a:5:{s:4:"time";d:1761532788.678404;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:37;a:5:{s:4:"time";d:1761532788.678409;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:38;a:5:{s:4:"time";d:1761532788.678818;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:39;a:5:{s:4:"time";d:1761532788.678973;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:1761532787.91856;s:3:"end";d:1761532788.714313;s:6:"memory";i:9009056;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:650:"a:3:{s:8:"messages";a:3:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532787.939421;i:4;a:0:{}i:5;i:4744128;}i:6;a:6:{i:0;s:42:"Request parsed with URL rule: gii/<id:\w+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1761532787.939431;i:4;a:0:{}i:5;i:4745816;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1761532787.939435;i:4;a:0:{}i:5;i:4746120;}}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";}";s:7:"request";s:9124:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:4:"host";s:9:"localhost";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"850";s:13:"cache-control";s:9:"max-age=0";s:9:"sec-ch-ua";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:6:"origin";s:16:"http://localhost";s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:14:"sec-fetch-site";s:11:"same-origin";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-dest";s:8:"document";s:7:"referer";s:39:"http://localhost/aleko-bo/web/gii/model";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:6:"cookie";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/7.4.33";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68fedb73e3d0e";s:16:"X-Debug-Duration";s:3:"761";s:12:"X-Debug-Link";s:50:"/aleko-bo/web/debug/default/view?tag=68fedb73e3d0e";}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";s:12:"actionParams";a:1:{s:2:"id";s:5:"model";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:850:"_csrf=k401ZJhzjaRgsI_Wbth6fBjQhhA4fBdfjrGDrIJUqmflw1Ewzhzb5y7X3483gRcvLObHf28GSG38gfrV9RnJEQ%3D%3D&Generator%5BtableName%5D=bundle_purchase&Generator%5BmodelClass%5D=BundlePurchase&Generator%5BstandardizeCapitals%5D=0&Generator%5Bsingularize%5D=0&Generator%5Bns%5D=app%5Cmodels&Generator%5BbaseClass%5D=yii%5Cdb%5CActiveRecord&Generator%5Bdb%5D=db&Generator%5BuseTablePrefix%5D=0&Generator%5BgenerateRelations%5D=all&Generator%5BgenerateRelationsFromCurrentSchema%5D=0&Generator%5BgenerateRelationsFromCurrentSchema%5D=1&Generator%5BgenerateLabelsFromComments%5D=0&Generator%5BgenerateQuery%5D=0&Generator%5BqueryNs%5D=app%5Cmodels&Generator%5BqueryBaseClass%5D=yii%5Cdb%5CActiveQuery&Generator%5BenableI18N%5D=0&Generator%5BmessageCategory%5D=app&Generator%5BuseSchemaName%5D=0&Generator%5BuseSchemaName%5D=1&Generator%5Btemplate%5D=default&preview=";s:7:"Decoded";a:3:{s:5:"_csrf";s:88:"k401ZJhzjaRgsI_Wbth6fBjQhhA4fBdfjrGDrIJUqmflw1Ewzhzb5y7X3483gRcvLObHf28GSG38gfrV9RnJEQ==";s:9:"Generator";a:18:{s:9:"tableName";s:15:"bundle_purchase";s:10:"modelClass";s:14:"BundlePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}}s:6:"SERVER";a:61:{s:16:"REDIRECT_MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:19:"REDIRECT_MYSQL_HOME";s:16:"\xampp\mysql\bin";s:21:"REDIRECT_OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:29:"REDIRECT_PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:14:"REDIRECT_PHPRC";s:10:"\xampp\php";s:12:"REDIRECT_TMP";s:10:"\xampp\tmp";s:15:"REDIRECT_STATUS";s:3:"200";s:7:"MIBDIRS";s:24:"C:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"C:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:9:"localhost";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"850";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:14:"HTTP_SEC_CH_UA";s:64:""Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:11:"HTTP_ORIGIN";s:16:"http://localhost";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/14******* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:12:"HTTP_REFERER";s:39:"http://localhost/aleko-bo/web/gii/model";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:11:"HTTP_COOKIE";s:172:"_csrf=5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv%22%3B%7D";s:4:"PATH";s:1305:"c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\System32;C:\Windows;C:\Windows\System32\wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files (x86)\cloudflared;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files (x;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files (x86)\dotnet\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\Zed\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:95:"<address>Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33 Server at localhost Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.54 (Win64) OpenSSL/1.1.1p PHP/7.4.33";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:15:"C:/xampp/htdocs";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:38:"C:/xampp/htdocs/aleko-bo/web/index.php";s:11:"REMOTE_PORT";s:5:"55681";s:12:"REDIRECT_URL";s:23:"/aleko-bo/web/gii/model";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:23:"/aleko-bo/web/gii/model";s:11:"SCRIPT_NAME";s:23:"/aleko-bo/web/index.php";s:8:"PHP_SELF";s:23:"/aleko-bo/web/index.php";s:18:"REQUEST_TIME_FLOAT";d:1761532787.909013;s:12:"REQUEST_TIME";i:1761532787;}s:3:"GET";a:1:{s:2:"id";s:5:"model";}s:4:"POST";a:3:{s:5:"_csrf";s:88:"k401ZJhzjaRgsI_Wbth6fBjQhhA4fBdfjrGDrIJUqmflw1Ewzhzb5y7X3483gRcvLObHf28GSG38gfrV9RnJEQ==";s:9:"Generator";a:18:{s:9:"tableName";s:15:"bundle_purchase";s:10:"modelClass";s:14:"BundlePurchase";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:17:"generateRelations";s:3:"all";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:13:"useSchemaName";s:1:"1";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}s:6:"COOKIE";a:1:{s:5:"_csrf";s:130:"5e917d4507190e95724b49946a0a11aed86eb8ddf0a000c049854ee76538fed0a:2:{i:0;s:5:"_csrf";i:1;s:32:"vNdTVoVCNgPYYYmS46AoWz_2r0yywMcv";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:2082:"a:5:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:6:"yii.js";}s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:55:"C:\xampp\htdocs\aleko-bo\vendor/bower-asset/jquery/dist";s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\62d52359";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/62d52359";s:7:"depends";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:51:"C:\xampp\htdocs\aleko-bo\vendor\yiisoft\yii2/assets";s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\9022c51c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/9022c51c";s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\gii\GiiAsset";a:9:{s:10:"sourcePath";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src/assets";s:3:"css";a:1:{i:0;s:12:"css/main.css";}s:2:"js";a:2:{i:0;s:20:"js/bs4-native.min.js";i:1;s:9:"js/gii.js";}s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:8:"basePath";s:44:"C:\xampp\htdocs\aleko-bo\web\assets\82e5729c";s:7:"baseUrl";s:29:"/aleko-bo/web/assets/82e5729c";s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68fedb73e3d0e";s:3:"url";s:39:"http://localhost/aleko-bo/web/gii/model";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:1761532787.909013;s:10:"statusCode";i:200;s:8:"sqlCount";i:199;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8980176;s:14:"processingTime";d:0.7945470809936523;}s:10:"exceptions";a:0:{}}