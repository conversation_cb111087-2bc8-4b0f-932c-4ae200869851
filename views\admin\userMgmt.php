<?php

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = Yii::t('app', 'User Management');

?>

<div class="card">
  <div class="card-header">
    <?= $this->render('/site/_alert_flash', []) ?>
    <?= $this->render('_search_user', [
      'model'   => $model,
      'page'    => 'user-mgmt',
      'pjax_id' => "#pjax-user-mgmt",
    ]); ?>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-5">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>
              <?= $summary ?>
            </h3>
            <h4><?= Yii::t('app','Total User') ?></h4>
          </div>
          <div class="icon">
            <i class="fas fa-chart-bar"></i>
          </div>
        </div>
      </div>
    </div>

    <?= GridView::widget([
      'dataProvider' => $model->getProvider(),
      'layout'       => '{items}{pager}',
      'tableOptions' => [
        'class' => 'table table-bordered table-hover text-nowrap',
      ],
      'options' => [
        'class' => 'grid-view',
      ],
      'pager' => [
        'class' => '\yii\bootstrap4\LinkPager',
        'options' => [
          'class' => 'mt-3',
        ],
      ],
      'showFooter' => false,
      'striped'    => false,
      'resizableColumns' => false,
      'columns'    => [
        [
          'label' => Yii::t('app','Username'),
          'format' => 'raw',
          'value' => function ($model) {
            $username = $model->username;
            $telegram_id = $model->user_telegram_id;
            if (empty($model->username)) {
              $username = $model->first_last_name;
            }

            if (empty($model->user_telegram_id)) {
              $telegram_id = '-';
            }

            $display = "Username : ". $username . "<br>" . "Telegram ID : " . $telegram_id;

            return $display;
          },
        ],
        [
          'label' => Yii::t('app','Social Platform ID'),
          'format' => 'raw',
          'value' => function ($model) {
            $x_id = $model->user_x_id;
            $instagram_id = $model->user_instagram_id;
            if (empty($model->user_x_id)) {
              $x_id = '-';
            }

            if (empty($model->user_instagram_id)) {
              $instagram_id = '-';
            }

            $display = "X ID : ". $x_id. "<br>" . "Instagram ID : " . $instagram_id;

            return $display;
          },
        ],
        [
          'label' => Yii::t('app','Is Premium'),
          'value' => function ($model) {
            return $model->is_premium ? Yii::t('app','Yes') : Yii::t('app','No');
          },
        ],
        [
          'label'  => Yii::t('app','Has Joined Channel'),
          'format' => 'raw',
          'value'  => function ($model) {
            return $model->has_joined_channel ? Yii::t('app','Yes') : Yii::t('app','No');
          },
        ],
        [
          'label'  => Yii::t('app','Wallet Address'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->wallet_address)) {
              return '-';
            }

            $link = Yii::$app->formatHelper->formatTon('address', $model->wallet_address, 5);

            return $link;
          },
        ],
        // [
        //   'label'  => Yii::t('app','Wallet Address List'),
        //   'format' => 'raw',
        //   'value'  => function ($model) {
            
        //     $wallet = $model->wallet;

        //     if (empty($wallet)) {
        //       return '-';
        //     }

        //     $html = '';
        //     $link = Yii::$app->formatHelper->formatTon('address', $wallet->wallet_address, 5);
        //     $html .= Html::tag('div', $link, ['class' => 'mb-1']);

        //     return $html;
        //   },
        // ],
        [
          'label'  => yii::t('app', 'Referral Tier Title'),
          'format' => 'raw',
          'value'  => function ($model) {
            if (empty($model->current_referral_ranking)) {
              return '-';
            }
            return $model->current_referral_ranking;
          },
        ],
        [
          'label'  => Yii::t('app','Login Info'),
          'format' => 'raw',
          'value'  => function ($model) {
            return 'Accumulated Login : ' . $model->accumulate_login . '<br> Continuous Login : ' . $model->continuous_login;
          },
        ],
        [
          'label' => Yii::t('app','Date'),
          'value' => function ($model) {
            return date('Y-m-d H:i:s', $model->created_at);
          },
        ],
      ]
    ]); ?>
  </div>
</div>
