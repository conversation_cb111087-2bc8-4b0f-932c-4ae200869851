import { S3 } from "@aws-sdk/client-s3"
import common from "../../imports/common.js";

const s3 = new S3({
    credentials: {
        accessKeyId: common.config.s3_bucket.accessKeyId,
        secretAccessKey: common.config.s3_bucket.secretAccessKey,
    },
    region: common.config.s3_bucket.region,
})

export default {
    "obj": s3,
    "put": async (path, file, opts) => {
        return s3.putObject({
            Bucket: common.config.s3_bucket.bucket,
            Key: path,
            Body: file,
            ...opts
        });
    },
    "getUrl": (path) => {
        return `https://${common.config.s3_bucket.bucket}.s3.${common.config.s3_bucket.region}.amazonaws.com/${path}`
    }
}