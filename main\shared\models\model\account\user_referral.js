export default function (sequelize, DataTypes) {
    return sequelize.define('user_referral', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        hash_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        group_tag: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        referral_user_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        referral_code: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'user_referral',
        timestamps: false,
    })
}