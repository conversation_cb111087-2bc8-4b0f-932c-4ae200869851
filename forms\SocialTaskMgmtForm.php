<?php

namespace app\forms;

use Yii;
use yii\helpers\ArrayHelper;
use yii\httpclient\Client;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\AuditLog;
use app\models\SocialTaskLookup;

class SocialTaskMgmtForm extends Model
{
  public $task_description;
  public $task_name;
  public $type;
  public $reward_point;
  public $redirect_url;
  public $icon_url;

  public function rules()
  {
    return [
      [['task_description', 'task_name', 'type', 'reward_point', 'redirect_url', 'icon_url'], 'safe'],
      [['task_description', 'task_name', 'redirect_url'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['reward_point'], 'integer', 'min' => 0],
      [['redirect_url'], 'url', 'defaultScheme' => 'https', 'validSchemes' => ['https']],
      [
        ['redirect_url'], 'required', 'on' => ['create', 'update'],
        'when' => function ($model) {
          return $model->type == 'social_task';
        },
        'whenClient' => "function (attribute, value) {
          return $('#socialtaskmgmtform-type').val() == 'social_task';
        }"
      ],
      [['icon_url'], 'file', 'extensions' => 'png, jpg, jpeg', 'maxSize' => 1024 * 1024 * 1],
      [['task_name', 'type', 'reward_point', 'redirect_url'], 'required', 'on' => ['create', 'update']],
    ];
  }

  public function attributeLabels()
  {
    return [
      'task_name'        => 'Title',
      'task_description' => 'Description',
      'type'             => 'Type',
      'reward_point'     => 'Reward Box',
      'redirect_url'     => 'Redirect URL',
      'icon_url'         => 'Icon',
    ];
  }

  /**
   * Gets query for [[SocialTaskLookup]] model.
   * 
   * Select * from social_task_lookup
   * where task_name like %:task_name%
   * and type = :type and type not in (:exclude)
   * and is_delete = 0
   *
   * @return \yii\db\ActiveQuery
   */
  public function getQuery()
  {
    $query = SocialTaskLookup::find();

    if (!empty($this->task_name)) {
      $query->andFilterWhere(['like','task_name',"%".$this->task_name."%", false]);
    }

    if (!empty($this->type)) {
      $query->andFilterWhere(['type' => $this->type]);
    }

    $exclude = SocialTaskLookup::getExcludeType();

    if (!empty($exclude)) {
      $query->andWhere(['not in', 'type', $exclude]);
    }

    $query->andWhere(['is_delete' => 0]);
    return $query;
  }

  /**
   * Gets the data provider for the social task lookup list.
   *
   * @return ActiveDataProvider The data provider for the social task lookup list.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 20,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Load social task data to the model
   * 
   * @param int $id
   * 
   * @throws \Exception
   */
  public function getSocialTask($id)
  {
    $model = SocialTaskLookup::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('Social Task not found');
    }

    $this->task_name        = $model->task_name;
    $this->task_description = $model->task_label_name;
    $this->type             = $model->type;
    $this->reward_point     = $model->reward_point * 1;
    $this->redirect_url     = $model->redirect_url;
    $this->icon_url         = $model->icon_image_url;
  }

  /**
   * Gets the social task lookup model by id
   * 
   * @param int $id
   * 
   * @return \app\models\SocialTaskLookup
   * 
   * @throws \Exception
   */
  private function getModel($id)
  {
    if ($id > 0) {
      $model = SocialTaskLookup::findOne([
        'id'        => $id,
        'is_delete' => 0,
      ]);

      if (empty($model)) {
        throw new \Exception('Social Task not found');
      }
    } else {
      $model = new SocialTaskLookup();
    }

    return $model;
  }

  /**
   * Uploads the given icon file to S3.
   * 
   * @param \yii\web\UploadedFile $icon_file
   * @param string $icon_type
   * 
   * @return string
   */
  protected function uploadIcon($icon_file, $icon_type)
  {
    $file      = $icon_type."-".date("Ymdhis"); // eg: "<icon_type>-20250909125530"
    $filename  = Yii::getAlias("@app/runtime/$file"); 
    $icon_file->saveAs($filename); // temperory save to local

    $s3file = Yii::getAlias("@s3-icon/{$file}");
    $type   = mime_content_type($filename);

    // setup s3 client and upload the icon
    $aws = Yii::$app->aws->client();
    $s3  = $aws->createS3();

    $result = $s3->putObject([
      'ACL'         => 'public-read',
      'Bucket'      => Yii::$app->params['s3_bucket'],
      'ContentType' => $type,
      'SourceFile'  => $filename,
      'Key'         => $s3file,
    ]);

    $filepath = $result->get('ObjectURL'); // get the full public url
    gc_collect_cycles(); // Clear any unused memory
    unlink($filename); // Delete the local file

    return $filepath;
  }

  /**
   * Creates or updates a social task.
   *
   * @param int $id
   *
   * @return \app\models\SocialTaskLookup
   *
   * @throws \Exception
   */
  public function createOrUpdateSocialTask($id = 0)
  {
    $base_model = $this->getModel($id);

    $user = Yii::$app->user->identity;

    // audit log for update
    if ($id > 0) {
      if ($base_model->task_name != $this->task_name) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_SOCIAL_TASK,
          'old_value' => $base_model->task_name,
          'value'     => $this->task_name,
          'remark'    => 'Update Title',
          'action_by' => $user->id,
        ]);
      }

      if ($base_model->type != $this->type) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_SOCIAL_TASK,
          'old_value' => $base_model->type,
          'value'     => $this->type,
          'remark'    => 'Update Type',
          'action_by' => $user->id,
        ]);
      }

      if ($base_model->reward_point != $this->reward_point) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_SOCIAL_TASK,
          'old_value' => $base_model->reward_point * 1,
          'value'     => $this->reward_point * 1,
          'remark'    => 'Update Reward AleXs',
          'action_by' => $user->id,
        ]);
      }

      if ($base_model->redirect_url != $this->redirect_url) {
        AuditLog::create([
          'function'  => AuditLog::MANAGE_SOCIAL_TASK,
          'old_value' => $base_model->redirect_url,
          'value'     => $this->redirect_url,
          'remark'    => 'Update Redirect URL',
          'action_by' => $user->id,
        ]);
      }
    }

    $base_model->task_name         = $this->task_name;
    $base_model->task_label_name   = $this->task_description ? $this->task_description : '-';
    $base_model->type              = $this->type;
    $base_model->reward_point      = $this->reward_point;
    $base_model->redirect_url      = $this->redirect_url;
    $base_model->message_key       = ucwords(str_replace('_', ' ', $this->type));
    $base_model->label_message_key = $this->task_description ? $this->task_description : '-';

    if (!empty($this->icon_url)) {
      $base_model->icon_image_url = $this->uploadIcon($this->icon_url, $this->type);
    }

    if ($id > 0) {
      $base_model->updated_at = time();

      if (!empty($this->icon_url)) {
        $base_model->update(false, ['task_name', 'task_label_name', 'type', 'reward_point', 'redirect_url', 'icon_image_url', 'updated_at']);
      } else {
        $base_model->update(false, ['task_name', 'task_label_name', 'type', 'reward_point', 'redirect_url', 'updated_at']);
      }
    } else { //save and audit log for create
      $base_model->created_at = time();

      if (!$base_model->save()) {
        throw new \Exception(current($base_model->getFirstErrors()));
      }

      AuditLog::create([
        'function'  => AuditLog::MANAGE_SOCIAL_TASK,
        'value'     => $this->task_name,
        'remark'    => 'Create Social Task',
        'action_by' => $user->id,
      ]);
    }

    return $base_model;
  }

  /**
   * Delete Social Task
   * 
   * @param int $id
   * 
   * @return boolean
   */
  public function deleteSocialTask($id)
  {
    $model = SocialTaskLookup::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      return false;
    }

    $model->is_delete = 1;
    $model->update(false, ['is_delete']);

    return true;
  }

  /**
   * Clear Social Task Lookup Cache
   * 
   * @return boolean
   */
  public function clearSocialCache()
  {
    $data = [
      'key' => 'task_lookup',
    ];

    $url = Yii::$app->params['api']['resetCache'];

    if (empty(Yii::$app->params['middleware_bearer'])) {
      return false;
    }

    // Create a new HTTP client post request
    $client  = new Client();
    $request = $client->createRequest();
    $request->setFormat(Client::FORMAT_JSON);
    $request->setUrl($url);
    $request->setData($data);
    $request->setMethod('POST');
    $request->addHeaders([
      "AUTHORIZATION" => "Bearer ".Yii::$app->params['middleware_bearer'],
    ]);
    $request->setOptions([
      'timeout' => 30
    ]);

    $response = $client->send($request);

    if (!$response->isOk) {
      return false;
    }

    return true;
  }
}
