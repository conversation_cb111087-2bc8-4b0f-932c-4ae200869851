<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "system_setting".
 *
 * @property int $id
 * @property string|null $key
 * @property string|null $value
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 */
class SystemSetting extends \yii\db\ActiveRecord
{
    const BSC_EXPLORER = 'bsc_explorer';
    const TON_EXPLORER = 'ton_explorer';
    const LAST_CHECKED_TIME = 'last_checked_time';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'system_setting';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['key', 'value', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['created_at'], 'required'],
            [['created_at', 'updated_at', 'is_delete'], 'integer'],
            [['key', 'value'], 'string', 'max' => 255],
            [['key'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'key' => 'Key',
            'value' => 'Value',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Find or create a new SystemSetting by key.
     *
     * @param string $key
     * @param string $default_value
     * 
     * @return SystemSetting
     * @throws \Exception
     */
    public static function findOrCreate($key, $default_value = "")
    {
        $model = SystemSetting::findOne(['key' => $key, 'is_delete' => 0]);

        if (!empty($model)) {
            return $model;
        } else {
            $model             = new SystemSetting;
            $model->key        = $key;
            $model->value      = (string) $default_value;
            $model->created_at = time();

            if (!$model->save()) {
                throw new \Exception(current($model->getFirstErrors()));
            }
            return $model;
        }
    }

}
