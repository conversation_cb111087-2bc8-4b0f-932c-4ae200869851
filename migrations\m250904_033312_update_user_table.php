<?php

use yii\db\Migration;

class m250904_033312_update_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('user', 'user_x_id', $this->text()->after('verification_token_expired_date'));
        $this->addColumn('user', 'user_instagram_id', $this->text()->after('user_x_id'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('user', 'user_x_id');
        $this->dropColumn('user', 'user_instagram_id');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250904_033312_update_user_table cannot be reverted.\n";

        return false;
    }
    */
}
