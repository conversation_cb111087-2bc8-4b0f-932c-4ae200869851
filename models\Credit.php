<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "credit".
 *
 * @property int $id
 * @property int $user_id
 * @property string $type
 * @property float $balance
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property WalletTransaction[] $walletTransactions
 */
class Credit extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'credit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['updated_at'], 'default', 'value' => null],
            [['balance'], 'default', 'value' => 0.00000000],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'type', 'created_at'], 'required'],
            [['user_id', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['balance'], 'number'],
            [['type'], 'string', 'max' => 255],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'type' => 'Type',
            'balance' => 'Balance',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[WalletTransactions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWalletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, ['credit_id' => 'id']);
    }

}
