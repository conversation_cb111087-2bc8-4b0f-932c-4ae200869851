<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%bundle_reward}}`.
 */
class m251024_080956_create_bundle_reward_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%bundle_reward}}', [
            'id'         => $this->primaryKey(),
            'bundle_id'  => $this->integer()->notNull(),
            'type'       => $this->string(255)->notNull(),
            'amount'     => $this->integer()->notNull(),
            'created_at' => $this->bigInteger()->notNull(),
            'updated_at' => $this->bigInteger(),
            'is_delete'  => 'tinyint(1) DEFAULT 0 NOT NULL',
            'key `findAll` (`is_delete`)',
            'key `findByBundleId` (`bundle_id`)',
            'key `findByType` (`type`)',
            'key `findByDate` (`created_at`)',
        ], "engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%bundle_reward}}');
    }
}
