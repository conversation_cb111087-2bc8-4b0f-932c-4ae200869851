<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use app\models\ExportJob;
use app\forms\UserMgmtForm;

class ExportJobForm extends Model
{
  public $ref_no;
  public $function;
  public $data;
  public $user_id;
  public $status;
  public $job_id;

  public function rules()
  {
    return [
      [['ref_no', 'function', 'data', 'user_id', 'status', 'job_id'], 'safe'],
    ];
  }

  public function attributeLabels()
  {
    return [
      'ref_no' => Yii::t('app', 'Reference No'),
    ];
  }

  /**
   * Gets an array of all available functions in export job.
   *
   * @return string[] An array with function names as keys and values.
   */
  public function getFunction()
  {
    $query = ExportJob::find();
    $query->select('function');
    $query->groupBy(['function']);
    $result = ArrayHelper::map($query->all(), 'function', function ($model) {
      return ucfirst($model['function']);
    });

    return $result;
  }

  /**
   * Gets the query for export job based on the current form data.
   * 
   * select * from export_job
   * where function = :function and status = :status and ref_no = :ref_no
   * and is_delete = 0
   *
   * @return ActiveQuery The query for the export job.
   */
  public function getQuery()
  {
    $query = ExportJob::find();

    if (isset($this->function)) {
      $query->andFilterWhere(['function' => $this->function]);
    }

    if (isset($this->status)) {
      $query->andFilterWhere(['status' => $this->status]);
    }

    $query->andFilterWhere(['ref_no' => $this->ref_no]);

    $query->andWhere(['is_delete' => 0]);
    return $query;
  }

  /**
   * Gets the data provider for the export job based on the current form data.
   * 
   * @return ActiveDataProvider The data provider for the export job.
   */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 20,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Gets the export job data from the database based on the current form data.
   * 
   * @return ExportJob The export job data from the database.
   */
  public function getExportFile()
  {
    $query = $this->getQuery();
    return $query->one();
  }

  /**
   * Finds the export job model by ref no.
   * 
   * @return ExportJob The export job model if found.
   */
  protected function findModelByRefno()
  {
    $model = ExportJob::findOne([
      'ref_no'    => $this->ref_no,
      'is_delete' => 0,
    ]);

    return $model;
  }

  /**
   * Finds the export job model by id.
   * 
   * @param int $id
   * 
   * @return ExportJob The export job model if found.
   * 
   * @throws \Exception if the job does not exist.
   */

  protected function findModel($id)
  {
    $model = ExportJob::findOne([
      'id'        => $id,
      'is_delete' => 0,
    ]);

    if (empty($model)) {
      throw new \Exception('The job does not exist.');
    }

    return $model;
  }

  /**
   * Updates the export job model with the given status, file path, filename.
   * 
   * @param ExportJob $model The export job model to update.
   * @param string    $status The status of the export job.
   * @param string    $file   The file path of the export file.
   * @param string    $filename The filename of the export file.
   * 
   * @return void
   */
  protected function updateFields(ExportJob $model, $status, $file = null, $filename = null)
  {
    $model->status     = $status;
    $model->filepath   = $file;
    $model->filename   = $filename;
    $model->updated_at = time();
    $model->update(false, ['status', 'updated_at', 'filepath', 'filename']);
  }

  /**
   * Creates the export job model with the given ref no, function, action by, data and status.
   * 
   * @return bool If the export job model is created successfully.
   * 
   * @throws \Exception If the validation of the export job model fails.
   */
  public function create()
  {
    if (!$this->validate()) {
      throw new \Exception(current($this->getFirstErrors()), 2000);
    }

    $model = $this->findModelByRefno();

    if (!empty($model)) { // if refno exists, return
      return;
    }

    $model             = new ExportJob;
    $model->ref_no     = $this->ref_no;
    $model->function   = $this->function;
    $model->action_by  = $this->user_id;
    $model->data       = json_encode($this->data);
    $model->status     = 'queue';
    $model->created_at = time();

    $model->save();

    return true;
  }

  /**
   * Run the export job.
   * 
   * @return bool If the export job is executed successfully.
   * 
   * @throws \Exception If there is an error when running the export job.
   */
  public function run()
  {
    $model = $this->findModel($this->job_id);
    $data  = json_decode($this->data, true); //return as array
    $excel = "";
    $this->updateFields($model, "processing");

    // add other functions for export at here, remember to create report template in app\components\report
    try {
      if ($this->function == "user-management") {
        $searchModel = new UserMgmtForm;
        $query = $searchModel->searchExportQuery($data);
        $excel = Yii::createObject([
          'class' => 'app\components\report\UserManagement',
        ]);
      } 

      // generate the Excel file, temperory save it in local and upload to S3
      $file      = date("Ymdhis") . "-{$this->function}.xlsx";
      $filename  = Yii::getAlias("@app/report.xlsx");
      $excel->generate($query, $data)->save($filename);

      $s3file  = Yii::getAlias("@s3-export/{$file}");
      $aws = Yii::$app->aws->client();
      $s3  = $aws->createS3();
      $result = $s3->putObject([
        'ACL'         => 'public-read',
        'Bucket'      => Yii::$app->params['s3_bucket'],
        'ContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'SourceFile'  => $filename,
        'Key'         => $s3file,
      ]);

      $filepath = $result->get('ObjectURL');
      $this->updateFields($model, "done", $filepath, $file); // update the export job model

      unlink($filename); // remove the local file
      gc_collect_cycles(); // free up memory
    } catch (\Exception $e) {
      $this->updateFields($model, "fail");
      throw $e;
    }
    return true;
  }
}