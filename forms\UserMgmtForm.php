<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\User;

class UserMgmtForm extends Model
{
  public $username;
  public $user_x_id;
  public $user_telegram_id;
  public $user_instagram_id;
  public $wallet_address;
  public $current_referral_ranking;
  public $date_range;
  public $date_start;
  public $date_end;
  public $has_joined;

  public function rules()
  {
    return [
      [[
        'username', 'user_x_id', 'user_telegram_id', 'user_instagram_id', 'wallet_address', 'current_referral_ranking', 'date_range', 'date_start',
        'date_end', 'has_joined'
      ], 'safe'],
      [['username', 'user_x_id', 'user_telegram_id', 'user_instagram_id', 'wallet_address', 'date_range'], 'filter', 'filter' => function ($value) {
        $value = strip_tags(trim($value));
        return $value;
      }],
      [['date_range'], 'match', 'pattern' => '/^.+\s\-\s.+$/'],
      [['date_range'], 'validateDate'],
    ];
  }

  /**
   * Validate date range
   *
   * @param string $attribute attribute name
   * @param array $params validation parameters
   *
   * @return void
   */
  public function validateDate($attribute, $params)
  {
    $dateRangeValue = $this->date_range;

    $dates = explode(' - ', $dateRangeValue, 2);
    if (count($dates) !== 2) {
      $this->addError($attribute, Yii::t('app', 'Incorrect Date Range'));
    }

    $this->date_start = isset($dates[0]) ? strtotime($dates[0].' 00:00:00') : null;
    $this->date_end   = isset($dates[1]) ? strtotime($dates[1].' 23:59:59') : null;
  }

  /**
   * Get the query for data provider
   *
   * Select * from user u
   * where u.username like %:username% or u.first_last_name like %:username%
   * and u.user_x_id = :user_x_id
   * and u.user_telegram_id = :user_telegram_id
   * and u.user_instagram_id = :user_instagram_id
   * and u.wallet_address like %:wallet_address%
   * and u.current_referral_ranking = :current_referral_ranking
   * and u.has_joined_channel = :has_joined
   * and u.created_at between :date_start and :date_end
   * and u.is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = User::find()
    ->alias('u');
    //->joinWith('wallet');

    if (!empty($this->username)) {
      $query->andWhere(['or',
        ['like','u.username',"%".$this->username."%", false],
        ['like','u.first_last_name',"%".$this->username."%", false]
      ]);
    }

    if (!empty($this->user_x_id)) {
      $query->andFilterWhere(['u.user_x_id' => $this->user_x_id]);
    }

    if (!empty($this->user_telegram_id)) {
      $query->andFilterWhere(['u.user_telegram_id' => $this->user_telegram_id]);
    }

    if (!empty($this->user_instagram_id)) {
      $query->andFilterWhere(['u.user_instagram_id' => $this->user_instagram_id]);
    }

    if ($this->has_joined != '') {
      if ($this->has_joined == 1) {
        $query->andFilterWhere(['u.has_joined_channel' => $this->has_joined]);
      } else {
        $query->andWhere(['or',
          ['u.has_joined_channel' => 0],
          ['u.has_joined_channel' => null]
        ]);
      }
    }

    if (!empty($this->wallet_address)) {
      $query->andFilterWhere(['like','u.wallet_address',"%".$this->wallet_address."%", false]);
    }

    if (!empty($this->current_referral_ranking)) {
      $query->andFilterWhere(['u.current_referral_ranking' => $this->current_referral_ranking]);
    }

    if ($this->date_start != '' && $this->date_end != '') {
      $query->andFilterWhere(['between', 'u.created_at', $this->date_start, $this->date_end]);
    }

    $query->andWhere(['u.is_delete' => 0]);

    return $query;
  }

  /**
   * Search and filter for export user data
   * 
   * @param array $params
   * 
   * @return \yii\db\ActiveQuery
   */
  public function searchExportQuery($params)
  {
    if (!empty($params)) {
      $this->load($params);
    }

    $query = $this->getQuery();
    $query->orderBy(['id'=> SORT_DESC]);

    return $query;
  }

/**
 * Gets the data provider for the user management list.
 *
 * @return ActiveDataProvider The data provider for the user management list.
 */
  public function getProvider()
  {
    $dataProvider = new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination' => [
        'pageSize' => 50,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Gets user summary info.
   *
   * @return int The number of user(s) based on the current form data.
   */
  public function getUserSummaryInfo()
  {
    $this->load(Yii::$app->request->queryParams);

    $query = $this->getQuery();

    $totalUser = $query->count();

    return $totalUser;
  }
}
