<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "instrument_lookup".
 *
 * @property int $id
 * @property string $instrument_name
 * @property string $instrument_image_normal
 * @property string $instrument_image_rare
 * @property string $instrument_image_legendary
 * @property string $instrument_image_mythic
 * @property float $instrument_price_normal
 * @property float $instrument_price_rare
 * @property float $instrument_price_legendary
 * @property float $instrument_price_mythic
 * @property int $instrument_is_active
 * @property int $created_at
 * @property int|null $updated_at
 * @property int|null $secondary_updated_at
 * @property int $is_delete
 *
 * @property AdvanceGenreLookup[] $advanceGenreLookups
 * @property AdvanceGenreLookup[] $advanceGenreLookups0
 * @property AdvanceGenreLookup[] $advanceGenreLookups1
 * @property AdvanceGenreLookup[] $advanceGenreLookups2
 * @property UserShopCard[] $userShopCards
 */
class InstrumentLookup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'instrument_lookup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['instrument_name', 'instrument_image_normal', 'instrument_image_rare', 'instrument_image_legendary', 'instrument_image_mythic', 'instrument_price_normal', 'instrument_price_rare', 'instrument_price_legendary', 'instrument_price_mythic', 'created_at'], 'required'],
            [['instrument_image_normal', 'instrument_image_rare', 'instrument_image_legendary', 'instrument_image_mythic'], 'string'],
            [['instrument_price_normal', 'instrument_price_rare', 'instrument_price_legendary', 'instrument_price_mythic'], 'number'],
            [['instrument_is_active', 'created_at', 'updated_at', 'secondary_updated_at', 'is_delete'], 'integer'],
            [['instrument_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'instrument_name' => 'Instrument Name',
            'instrument_image_normal' => 'Instrument Image Normal',
            'instrument_image_rare' => 'Instrument Image Rare',
            'instrument_image_legendary' => 'Instrument Image Legendary',
            'instrument_image_mythic' => 'Instrument Image Mythic',
            'instrument_price_normal' => 'Instrument Price Normal',
            'instrument_price_rare' => 'Instrument Price Rare',
            'instrument_price_legendary' => 'Instrument Price Legendary',
            'instrument_price_mythic' => 'Instrument Price Mythic',
            'instrument_is_active' => 'Instrument Is Active',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'secondary_updated_at' => 'Secondary Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[AdvanceGenreLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdvanceGenreLookups()
    {
        return $this->hasMany(AdvanceGenreLookup::className(), ['boost_instrument_one_id' => 'id']);
    }

    /**
     * Gets query for [[AdvanceGenreLookups0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdvanceGenreLookups0()
    {
        return $this->hasMany(AdvanceGenreLookup::className(), ['boost_instrument_two_id' => 'id']);
    }

    /**
     * Gets query for [[AdvanceGenreLookups1]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdvanceGenreLookups1()
    {
        return $this->hasMany(AdvanceGenreLookup::className(), ['boost_instrument_three_id' => 'id']);
    }

    /**
     * Gets query for [[AdvanceGenreLookups2]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAdvanceGenreLookups2()
    {
        return $this->hasMany(AdvanceGenreLookup::className(), ['boost_instrument_four_id' => 'id']);
    }

    /**
     * Gets query for [[UserShopCards]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserShopCards()
    {
        return $this->hasMany(UserShopCard::className(), ['instrument_id' => 'id']);
    }

    /**
     * Gets all instrument names as an associative array.
     *
     * Select id, instrument_name from instrument_lookup
     * where is_delete = 0
     * order by id ASC
     *
     * @return array
     */
    public static function getAllInstrument()
    {
        $query = self::find()
            ->select([
                'id',
                'instrument_name'
            ])
            ->where(['is_delete' => 0])
            ->orderBy('id ASC')
            ->all();

        return ArrayHelper::map($query, 'instrument_name', 'instrument_name');
    }
}
