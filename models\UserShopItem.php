<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_shop_item".
 *
 * @property int $id
 * @property int $user_id
 * @property int $item_id
 * @property int $is_used
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property User $user
 * @property ShopItemLookup $item
 * @property UserSongSelectedLookup[] $userSongSelectedLookups
 * @property UserSongSelectedLookup[] $userSongSelectedLookups0
 * @property UserSongSelectedLookup[] $userSongSelectedLookups1
 */
class UserShopItem extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_shop_item';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'item_id', 'created_at'], 'required'],
            [['user_id', 'item_id', 'is_used', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['user_id' => 'id']],
            [['item_id'], 'exist', 'skipOnError' => true, 'targetClass' => ShopItemLookup::className(), 'targetAttribute' => ['item_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'item_id' => 'Item ID',
            'is_used' => 'Is Used',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Item]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getItem()
    {
        return $this->hasOne(ShopItemLookup::className(), ['id' => 'item_id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['tap_booster_user_shop_item_id' => 'id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups0()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['exp_booster_user_shop_item_id' => 'id']);
    }

    /**
     * Gets query for [[UserSongSelectedLookups1]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUserSongSelectedLookups1()
    {
        return $this->hasMany(UserSongSelectedLookup::className(), ['token_booster_user_shop_item_id' => 'id']);
    }
}
