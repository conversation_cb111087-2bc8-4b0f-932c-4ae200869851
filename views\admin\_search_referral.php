<?php

use yii\web\View;
use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'username')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Username')) ?>
      <?= $form->field($model, 'address')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Wallet Address')) ?>
      <?= $form->field($model, 'referral_code')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Referral Code')) ?>
      <?= $form->field($model, 'referral_address')->textInput(['placeholder' => ''])->label(Yii::t('app', 'Referral Wallet Address')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-success float-right']) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>
