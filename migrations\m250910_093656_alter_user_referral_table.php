<?php

use yii\db\Migration;

class m250910_093656_alter_user_referral_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            'findByReferralUserId',
            'user_referral',
            ['referral_user_id', 'is_delete', 'created_at']
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex(
            'findByReferralUserId',
            'user_referral'
        );
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250910_093656_alter_user_referral_table cannot be reverted.\n";

        return false;
    }
    */
}
