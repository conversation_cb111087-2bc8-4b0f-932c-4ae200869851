<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "wallet_transaction".
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $wallet_id
 * @property int|null $credit_id
 * @property string|null $tx_id
 * @property string $type
 * @property float $amount
 * @property float $before_balance
 * @property float $after_balance
 * @property string|null $description
 * @property string|null $remark
 * @property string $category
 * @property int $is_ticket_distributed
 * @property int $created_at
 * @property int|null $updated_at
 * @property int $is_delete
 *
 * @property Credit $credit
 * @property User $user
 * @property Wallet $wallet
 */
class WalletTransaction extends \yii\db\ActiveRecord
{

    /**
     * ENUM field values
     */
    const TYPE_IN = 'IN';
    const TYPE_OUT = 'OUT';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'wallet_transaction';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wallet_id', 'credit_id', 'tx_id', 'description', 'remark', 'updated_at'], 'default', 'value' => null],
            [['is_delete'], 'default', 'value' => 0],
            [['user_id', 'type', 'amount', 'before_balance', 'after_balance', 'category', 'created_at'], 'required'],
            [['user_id', 'wallet_id', 'credit_id', 'is_ticket_distributed', 'created_at', 'updated_at', 'is_delete'], 'integer'],
            [['tx_id', 'type'], 'string'],
            [['amount', 'before_balance', 'after_balance'], 'number'],
            [['description', 'remark', 'category'], 'string', 'max' => 255],
            ['type', 'in', 'range' => array_keys(self::optsType())],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
            [['wallet_id'], 'exist', 'skipOnError' => true, 'targetClass' => Wallet::class, 'targetAttribute' => ['wallet_id' => 'id']],
            [['credit_id'], 'exist', 'skipOnError' => true, 'targetClass' => Credit::class, 'targetAttribute' => ['credit_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'wallet_id' => 'Wallet ID',
            'credit_id' => 'Credit ID',
            'tx_id' => 'Tx ID',
            'type' => 'Type',
            'amount' => 'Amount',
            'before_balance' => 'Before Balance',
            'after_balance' => 'After Balance',
            'description' => 'Description',
            'remark' => 'Remark',
            'category' => 'Category',
            'is_ticket_distributed' => 'Is Ticket Distributed',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_delete' => 'Is Delete',
        ];
    }

    /**
     * Gets query for [[Credit]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCredit()
    {
        return $this->hasOne(Credit::class, ['id' => 'credit_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for [[Wallet]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWallet()
    {
        return $this->hasOne(Wallet::class, ['id' => 'wallet_id']);
    }


    /**
     * column type ENUM value labels
     * @return string[]
     */
    public static function optsType()
    {
        return [
            self::TYPE_IN => 'IN',
            self::TYPE_OUT => 'OUT',
        ];
    }

    /**
     * @return string
     */
    public function displayType()
    {
        return self::optsType()[$this->type];
    }

    /**
     * Check if transaction type is IN
     * @return bool
     */
    public function isTypeIn()
    {
        return $this->type === self::TYPE_IN;
    }

    public function setTypeToIn()
    {
        $this->type = self::TYPE_IN;
    }

    /**
     * Checks if the type of transaction is OUT
     * @return bool
     */
    public function isTypeOut()
    {
        return $this->type === self::TYPE_OUT;
    }

    /**
     * Set the type of transaction to OUT
     */
    public function setTypeToOut()
    {
        $this->type = self::TYPE_OUT;
    }

    /**
     * Gets all categories of wallet transactions
     * 
     * @return array with category values as keys and formatted labels as values
     */
    public static function getAllType()
    {
        $query = self::find();
        $query->andWhere(['is_delete' => 0]);
        $query->groupBy('category');
        $query->orderBy('category ASC');
        $result = $query->all();

        return ArrayHelper::map($result, 'category', function ($model) {
            return ucwords(str_replace('_', ' ', $model->category));
        });
    }
}
