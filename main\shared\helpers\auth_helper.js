import common from "../imports/common.js";
import helper from "../imports/helper.js";
import query from "../imports/query.js";
import passport from "passport";

function getBearerToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1];
        return token;
    }
    return null;
}

function signJWT(user, wallet_id, wallet_address, extra_text) {
    const signed_jwt = helper.jwt_helper.sign({
        exp: common.moment().utc().unix() + (common.config.secret_config.jwt_expire * (24 * 3600)),
        id: user.id,
        username: user.username,
        first_last_name: user.first_last_name,
        profile_pic: user.profile_pic,
        hash: common.util_helper.loginHash(user),
        loginType: 'user_telegram_login',
        randomizedhex: common.crypto.randomBytes(64).toString('hex'),
        wallet_id: wallet_id,
        wallet_address: wallet_address,
    }, extra_text.toString());

    return signed_jwt
}

function authenticateJWT(req, res, next) {
    return passport.authenticate('jwt', { session: false }, (err, user, info) => {
        if (err || !user) {
            return res.status(403).json({
                "status": 403, "error": true, "msg": (info ? (info.message ? info.message : "Something went wrong") : "Something went wrong"), "data": {}
            })
        }
        req.user = user
        next()
    })(req, res, next)
}

function walletJWTChecker(req, res, next) {
    const { wallet_id, wallet_address, user_telegram_id } = req.user;
    if (wallet_id === null || wallet_address === null) {
        return res.status(403).json({
            "status": 403, "error": true, "msg": "Please connect wallet to proceed.", "data": {}
        })
    }
    next();
}

async function checkIsTeleAccount(req, res, next) {
    try {
        const { id: user_id } = req.user;
        const find_user = await query.models.user.findOne({ where: { id: user_id, is_delete: 0 } });

        if (!find_user) {
            return res.status(404).json({
                status: 404,
                error: true,
                msg: "User not found.",
                data: {}
            });
        }

        if (!find_user.user_telegram_id) {
            return res.status(403).json({
                status: 403,
                error: true,
                msg: "Invalid Token.",
                data: {}
            });
        }

        if (find_user.is_verified === false) {
            return res.status(404).json({
                status: 404,
                error: true,
                msg: "Account not verified. Verify account before proceed.",
                data: {}
            });
        }

        return next();
    } catch (error) {
        console.error("Error in checkIsTeleAccount:", error);
        return res.status(500).json({
            status: 500,
            error: true,
            msg: "Internal Server Error",
            data: {}
        });
    }
}

async function checkOnlyIsVerified(req, res, next) {
    try {
        const { id: user_id } = req.user;
        const find_user = await query.models.user.findOne({ where: { id: user_id, is_delete: 0 } });
        if (find_user.is_verified === false) {
            return res.status(404).json({
                status: 404,
                error: true,
                msg: "Account not verified. Verify account before proceed.",
                data: {}
            });
        }

        return next();
    } catch (error) {
        console.error("Error in checkOnlyIsVerified:", error);
        return res.status(500).json({
            status: 500,
            error: true,
            msg: "Internal Server Error",
            data: {}
        });
    }
}

export default {
    getBearerToken,
    signJWT,
    authenticateJWT,
    walletJWTChecker,
    checkIsTeleAccount,
    checkOnlyIsVerified
}