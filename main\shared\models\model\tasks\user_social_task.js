export default function (sequelize, DataTypes) {
    return sequelize.define('user_social_task', {
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        social_task_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        wallet_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        level_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        is_done: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.INTEGER
        },
        updated_at: {
            type: DataTypes.INTEGER
        },
        is_delete: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        }
    }, {
        sequelize,
        tableName: 'user_social_task',
        timestamps: false,
    })
}