a:10:{s:6:"config";s:6376:"a:5:{s:10:"phpVersion";s:6:"7.4.33";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"7.4.33";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:26:{s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:64:"C:\xampp\htdocs\aleko-bo\vendor/2amigos/yii2-ckeditor-widget/src";}}s:25:"alexantr/yii2-colorpicker";a:3:{s:4:"name";s:25:"alexantr/yii2-colorpicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@alexantr/colorpicker";s:57:"C:\xampp\htdocs\aleko-bo\vendor/alexantr/yii2-colorpicker";}}s:24:"asmoday74/yii2-ckeditor5";a:3:{s:4:"name";s:24:"asmoday74/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:20:"@asmoday74/ckeditor5";s:56:"C:\xampp\htdocs\aleko-bo\vendor/asmoday74/yii2-ckeditor5";}}s:29:"cetver/yii2-language-selector";a:3:{s:4:"name";s:29:"cetver/yii2-language-selector";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:24:"@cetver/LanguageSelector";s:61:"C:\xampp\htdocs\aleko-bo\vendor/cetver/yii2-language-selector";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:65:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:58:"C:\xampp\htdocs\aleko-bo\vendor/hail812/yii2-adminlte3/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:61:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:56:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-dialog/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-fileinput/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/date";s:67:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datepicker/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:60:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-date-range/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:71:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:66:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-typeahead/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:54:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-grid/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:64:"C:\xampp\htdocs\aleko-bo\vendor/kartik-v/yii2-widget-select2/src";}}s:20:"nickcv/yii2-mandrill";a:3:{s:4:"name";s:20:"nickcv/yii2-mandrill";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@nickcv/mandrill";s:56:"C:\xampp\htdocs\aleko-bo\vendor/nickcv/yii2-mandrill/src";}}s:23:"sangroya/yii2-ckeditor5";a:3:{s:4:"name";s:23:"sangroya/yii2-ckeditor5";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@sangroya/ckeditor5";s:55:"C:\xampp\htdocs\aleko-bo\vendor/sangroya/yii2-ckeditor5";}}s:27:"unclead/yii2-multiple-input";a:3:{s:4:"name";s:27:"unclead/yii2-multiple-input";s:7:"version";s:8:"********";s:5:"alias";a:3:{s:22:"@unclead/multipleinput";s:63:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/src";s:28:"@unclead/multipleinput/tests";s:65:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/tests";s:31:"@unclead/multipleinput/examples";s:68:"C:\xampp\htdocs\aleko-bo\vendor/unclead/yii2-multiple-input/examples";}}s:21:"yiier/yii2-aliyun-oss";a:3:{s:4:"name";s:21:"yiier/yii2-aliyun-oss";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@yiier/AliyunOSS";s:53:"C:\xampp\htdocs\aleko-bo\vendor/yiier/yii2-aliyun-oss";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-faker/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:59:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-httpclient/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-jui/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"********";s:5:"alias";a:1:{s:10:"@yii/debug";s:54:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-debug/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.1.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:52:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-gii/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:60:"C:\xampp\htdocs\aleko-bo\vendor/yiisoft/yii2-swiftmailer/src";}}}}";s:3:"log";s:6771:"a:1:{s:8:"messages";a:22:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532708.162647;i:4;a:0:{}i:5;i:2403064;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532708.162656;i:4;a:0:{}i:5;i:2403864;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532708.163233;i:4;a:0:{}i:5;i:2443384;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1761532708.163238;i:4;a:0:{}i:5;i:2444560;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1761532708.167968;i:4;a:0:{}i:5;i:2904464;}i:5;a:6:{i:0;s:24:"Route to run: migrate/up";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1761532708.17474;i:4;a:0:{}i:5;i:3799504;}i:6;a:6:{i:0;s:69:"Running action: yii\console\controllers\MigrateController::actionUp()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1761532708.177032;i:4;a:0:{}i:5;i:4149160;}i:7;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.18013;i:4;a:0:{}i:5;i:4557344;}i:8;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532708.180148;i:4;a:0:{}i:5;i:4557904;}i:13;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192443;i:4;a:0:{}i:5;i:4611376;}i:16;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.193354;i:4;a:0:{}i:5;i:4637744;}i:19;a:6:{i:0;s:90:"SELECT `version`, `apply_time` FROM `migration` ORDER BY `apply_time` DESC, `version` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.19841;i:4;a:0:{}i:5;i:5240952;}i:22;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.204666;i:4;a:0:{}i:5;i:5338416;}i:25;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.20866;i:4;a:0:{}i:5;i:5342224;}i:28;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208878;i:4;a:0:{}i:5;i:5344728;}i:31;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1761532708.912816;i:4;a:0:{}i:5;i:5492800;}i:32;a:6:{i:0;s:1148:"CREATE TABLE `bundle_purchase` (
	`id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` int(11) NOT NULL,
	`wallet_id` int(11) NOT NULL,
	`bundle_id` int(11) NOT NULL,
	`log_id` int(11),
	`tx_id` varchar(255),
	`payment_jetton` varchar(255),
	`contract_address` varchar(255),
	`amount` decimal(36,18) NOT NULL DEFAULT 1,
	`usd_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`token_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`bonus_token` decimal(36,18) DEFAULT 0,
	`created_at` bigint(20) NOT NULL,
	`updated_at` bigint(20),
	`is_delete` tinyint(1) DEFAULT 0 NOT NULL,
	foreign key (`user_id`) references `user` (`id`),
	foreign key (`wallet_id`) references `wallet` (`id`),
	foreign key (`bundle_id`) references `bundle` (`id`),
	key `findAll` (`is_delete`),
	key `findTxId` (`tx_id`),
	key `findByBundleId` (`bundle_id`),
	key `findByUserId` (`user_id`),
	key `findByWalletId` (`wallet_id`),
	key `findByLogId` (`log_id`),
	key `findByDate` (`created_at`),
	key `findByPaymentJetton` (`payment_jetton`),
	key `findByContractAddress` (`contract_address`)
) engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532708.979422;i:4;a:0:{}i:5;i:5608920;}i:35;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1761532709.13055;i:4;a:0:{}i:5;i:5605984;}i:36;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.130953;i:4;a:0:{}i:5;i:5608400;}i:39;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.140836;i:4;a:0:{}i:5;i:5612208;}i:42;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141105;i:4;a:0:{}i:5;i:5614712;}i:45;a:6:{i:0;s:116:"INSERT INTO `migration` (`version`, `apply_time`) VALUES ('m251025_073828_create_bundle_purchase_table', 1761532709)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.141493;i:4;a:0:{}i:5;i:5615808;}}}";s:9:"profiling";s:10884:"a:3:{s:6:"memory";i:5672080;s:4:"time";d:1.0807111263275146;s:8:"messages";a:26:{i:9;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532708.180151;i:4;a:0:{}i:5;i:4558600;}i:10;a:6:{i:0;s:56:"Opening DB connection: mysql:host=127.0.0.1;dbname=aleko";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1761532708.181705;i:4;a:0:{}i:5;i:4607264;}i:11;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.181711;i:4;a:0:{}i:5;i:4608256;}i:12;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192391;i:4;a:0:{}i:5;i:4611880;}i:14;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192453;i:4;a:0:{}i:5;i:4612352;}i:15;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192604;i:4;a:0:{}i:5;i:4613536;}i:17;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.193363;i:4;a:0:{}i:5;i:4640872;}i:18;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.193679;i:4;a:0:{}i:5;i:4642504;}i:20;a:6:{i:0;s:90:"SELECT `version`, `apply_time` FROM `migration` ORDER BY `apply_time` DESC, `version` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.198421;i:4;a:0:{}i:5;i:5242064;}i:21;a:6:{i:0;s:90:"SELECT `version`, `apply_time` FROM `migration` ORDER BY `apply_time` DESC, `version` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.19896;i:4;a:0:{}i:5;i:5271008;}i:23;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.204679;i:4;a:0:{}i:5;i:5339408;}i:24;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208615;i:4;a:0:{}i:5;i:5342728;}i:26;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.20867;i:4;a:0:{}i:5;i:5343200;}i:27;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208805;i:4;a:0:{}i:5;i:5344352;}i:29;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208885;i:4;a:0:{}i:5;i:5347216;}i:30;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.209161;i:4;a:0:{}i:5;i:5348848;}i:33;a:6:{i:0;s:1148:"CREATE TABLE `bundle_purchase` (
	`id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` int(11) NOT NULL,
	`wallet_id` int(11) NOT NULL,
	`bundle_id` int(11) NOT NULL,
	`log_id` int(11),
	`tx_id` varchar(255),
	`payment_jetton` varchar(255),
	`contract_address` varchar(255),
	`amount` decimal(36,18) NOT NULL DEFAULT 1,
	`usd_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`token_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`bonus_token` decimal(36,18) DEFAULT 0,
	`created_at` bigint(20) NOT NULL,
	`updated_at` bigint(20),
	`is_delete` tinyint(1) DEFAULT 0 NOT NULL,
	foreign key (`user_id`) references `user` (`id`),
	foreign key (`wallet_id`) references `wallet` (`id`),
	foreign key (`bundle_id`) references `bundle` (`id`),
	key `findAll` (`is_delete`),
	key `findTxId` (`tx_id`),
	key `findByBundleId` (`bundle_id`),
	key `findByUserId` (`user_id`),
	key `findByWalletId` (`wallet_id`),
	key `findByLogId` (`log_id`),
	key `findByDate` (`created_at`),
	key `findByPaymentJetton` (`payment_jetton`),
	key `findByContractAddress` (`contract_address`)
) engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532708.979437;i:4;a:0:{}i:5;i:5613648;}i:34;a:6:{i:0;s:1148:"CREATE TABLE `bundle_purchase` (
	`id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` int(11) NOT NULL,
	`wallet_id` int(11) NOT NULL,
	`bundle_id` int(11) NOT NULL,
	`log_id` int(11),
	`tx_id` varchar(255),
	`payment_jetton` varchar(255),
	`contract_address` varchar(255),
	`amount` decimal(36,18) NOT NULL DEFAULT 1,
	`usd_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`token_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`bonus_token` decimal(36,18) DEFAULT 0,
	`created_at` bigint(20) NOT NULL,
	`updated_at` bigint(20),
	`is_delete` tinyint(1) DEFAULT 0 NOT NULL,
	foreign key (`user_id`) references `user` (`id`),
	foreign key (`wallet_id`) references `wallet` (`id`),
	foreign key (`bundle_id`) references `bundle` (`id`),
	key `findAll` (`is_delete`),
	key `findTxId` (`tx_id`),
	key `findByBundleId` (`bundle_id`),
	key `findByUserId` (`user_id`),
	key `findByWalletId` (`wallet_id`),
	key `findByLogId` (`log_id`),
	key `findByDate` (`created_at`),
	key `findByPaymentJetton` (`payment_jetton`),
	key `findByContractAddress` (`contract_address`)
) engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.130414;i:4;a:0:{}i:5;i:5614032;}i:37;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.130962;i:4;a:0:{}i:5;i:5609392;}i:38;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.140793;i:4;a:0:{}i:5;i:5612712;}i:40;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.140848;i:4;a:0:{}i:5;i:5613184;}i:41;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141025;i:4;a:0:{}i:5;i:5614336;}i:43;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141113;i:4;a:0:{}i:5;i:5617200;}i:44;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141439;i:4;a:0:{}i:5;i:5618832;}i:46;a:6:{i:0;s:116:"INSERT INTO `migration` (`version`, `apply_time`) VALUES ('m251025_073828_create_bundle_purchase_table', 1761532709)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.141499;i:4;a:0:{}i:5;i:5616664;}i:47;a:6:{i:0;s:116:"INSERT INTO `migration` (`version`, `apply_time`) VALUES ('m251025_073828_create_bundle_purchase_table', 1761532709)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.144522;i:4;a:0:{}i:5;i:5617176;}}}";s:2:"db";s:10488:"a:1:{s:8:"messages";a:24:{i:11;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.181711;i:4;a:0:{}i:5;i:4608256;}i:12;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192391;i:4;a:0:{}i:5;i:4611880;}i:14;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192453;i:4;a:0:{}i:5;i:4612352;}i:15;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.192604;i:4;a:0:{}i:5;i:4613536;}i:17;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.193363;i:4;a:0:{}i:5;i:4640872;}i:18;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.193679;i:4;a:0:{}i:5;i:4642504;}i:20;a:6:{i:0;s:90:"SELECT `version`, `apply_time` FROM `migration` ORDER BY `apply_time` DESC, `version` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.198421;i:4;a:0:{}i:5;i:5242064;}i:21;a:6:{i:0;s:90:"SELECT `version`, `apply_time` FROM `migration` ORDER BY `apply_time` DESC, `version` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.19896;i:4;a:0:{}i:5;i:5271008;}i:23;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.204679;i:4;a:0:{}i:5;i:5339408;}i:24;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208615;i:4;a:0:{}i:5;i:5342728;}i:26;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.20867;i:4;a:0:{}i:5;i:5343200;}i:27;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208805;i:4;a:0:{}i:5;i:5344352;}i:29;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.208885;i:4;a:0:{}i:5;i:5347216;}i:30;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532708.209161;i:4;a:0:{}i:5;i:5348848;}i:33;a:6:{i:0;s:1148:"CREATE TABLE `bundle_purchase` (
	`id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` int(11) NOT NULL,
	`wallet_id` int(11) NOT NULL,
	`bundle_id` int(11) NOT NULL,
	`log_id` int(11),
	`tx_id` varchar(255),
	`payment_jetton` varchar(255),
	`contract_address` varchar(255),
	`amount` decimal(36,18) NOT NULL DEFAULT 1,
	`usd_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`token_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`bonus_token` decimal(36,18) DEFAULT 0,
	`created_at` bigint(20) NOT NULL,
	`updated_at` bigint(20),
	`is_delete` tinyint(1) DEFAULT 0 NOT NULL,
	foreign key (`user_id`) references `user` (`id`),
	foreign key (`wallet_id`) references `wallet` (`id`),
	foreign key (`bundle_id`) references `bundle` (`id`),
	key `findAll` (`is_delete`),
	key `findTxId` (`tx_id`),
	key `findByBundleId` (`bundle_id`),
	key `findByUserId` (`user_id`),
	key `findByWalletId` (`wallet_id`),
	key `findByLogId` (`log_id`),
	key `findByDate` (`created_at`),
	key `findByPaymentJetton` (`payment_jetton`),
	key `findByContractAddress` (`contract_address`)
) engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532708.979437;i:4;a:0:{}i:5;i:5613648;}i:34;a:6:{i:0;s:1148:"CREATE TABLE `bundle_purchase` (
	`id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` int(11) NOT NULL,
	`wallet_id` int(11) NOT NULL,
	`bundle_id` int(11) NOT NULL,
	`log_id` int(11),
	`tx_id` varchar(255),
	`payment_jetton` varchar(255),
	`contract_address` varchar(255),
	`amount` decimal(36,18) NOT NULL DEFAULT 1,
	`usd_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`token_amount` decimal(36,18) NOT NULL DEFAULT 0,
	`bonus_token` decimal(36,18) DEFAULT 0,
	`created_at` bigint(20) NOT NULL,
	`updated_at` bigint(20),
	`is_delete` tinyint(1) DEFAULT 0 NOT NULL,
	foreign key (`user_id`) references `user` (`id`),
	foreign key (`wallet_id`) references `wallet` (`id`),
	foreign key (`bundle_id`) references `bundle` (`id`),
	key `findAll` (`is_delete`),
	key `findTxId` (`tx_id`),
	key `findByBundleId` (`bundle_id`),
	key `findByUserId` (`user_id`),
	key `findByWalletId` (`wallet_id`),
	key `findByLogId` (`log_id`),
	key `findByDate` (`created_at`),
	key `findByPaymentJetton` (`payment_jetton`),
	key `findByContractAddress` (`contract_address`)
) engine = InnoDB default character set = utf8mb4, default collate = utf8mb4_unicode_ci";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.130414;i:4;a:0:{}i:5;i:5614032;}i:37;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.130962;i:4;a:0:{}i:5;i:5609392;}i:38;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.140793;i:4;a:0:{}i:5;i:5612712;}i:40;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.140848;i:4;a:0:{}i:5;i:5613184;}i:41;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141025;i:4;a:0:{}i:5;i:5614336;}i:43;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141113;i:4;a:0:{}i:5;i:5617200;}i:44;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1761532709.141439;i:4;a:0:{}i:5;i:5618832;}i:46;a:6:{i:0;s:116:"INSERT INTO `migration` (`version`, `apply_time`) VALUES ('m251025_073828_create_bundle_purchase_table', 1761532709)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.141499;i:4;a:0:{}i:5;i:5616664;}i:47;a:6:{i:0;s:116:"INSERT INTO `migration` (`version`, `apply_time`) VALUES ('m251025_073828_create_bundle_purchase_table', 1761532709)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1761532709.144522;i:4;a:0:{}i:5;i:5617176;}}}";s:5:"event";s:1660:"a:9:{i:0;a:5:{s:4:"time";d:1761532708.170834;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}i:1;a:5:{s:4:"time";d:1761532708.175969;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}i:2;a:5:{s:4:"time";d:1761532708.175989;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"yii\console\controllers\MigrateController";}i:3;a:5:{s:4:"time";d:1761532708.1817;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:4;a:5:{s:4:"time";d:1761532708.91283;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1761532709.130662;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1761532709.145165;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"yii\console\controllers\MigrateController";}i:7;a:5:{s:4:"time";d:1761532709.145178;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}i:8;a:5:{s:4:"time";d:1761532709.145191;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1761532708.155006;s:3:"end";d:1761532709.235808;s:6:"memory";i:5672080;}";s:4:"dump";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68fedb2429203";s:3:"url";s:15:"php yii migrate";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:9:"MSI Bravo";s:4:"time";d:1761532708.14226;s:10:"statusCode";i:0;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:5672080;s:14:"processingTime";d:1.0807111263275146;}s:10:"exceptions";a:0:{}}