<?php

$apiDomain = "";

$translationDomain = "";


return [
    'translation_read_path' => [],
    'translation_set_path'  => [],

    'translation_bearer' => '',
    'translation_mode'   => 's3', // local or api or s3
    'jwt_secret'         => 'abcd1234',
    'jwt_expiry'         => 900, // 30 minutes
    'company_name'       => "Aleko",
    'company_logo'       => "",
    'support_email'      => "",
    'email_footer'       => "",
    's3_bucket'          => 'zetta-cms',
    's3_region'          => 'ap-southeast-1',

    'middleware_bearer' => '',

    's3_presigned_expire_second' => '10',
    'ratelimit_max_attempt'      => 1,
    'ratelimit_lock_second'      => 60, // 1 minute

    'RequestCoderatelimit_key'         => 'requestCode',
    'RequestCodeRatelimit_max_attempt' => 1,

    'bsVersion' => '4.x', // this will set globally `bsVersion` for all Krajee Extensions
    'bsDependencyEnabled' => false,

    'api'  => [
        'resetCache' => "{$apiDomain}/cache/clear",
        'translationWrite' => "{$translationDomain}/translation/save-latest",
    ]
];
