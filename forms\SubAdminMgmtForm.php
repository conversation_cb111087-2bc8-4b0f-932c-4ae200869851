<?php

namespace app\forms;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Admin;

class SubAdminMgmtForm extends Model
{
  public $admin_id;
  public $username;
  public $email;
  public $role;
  public $password;
  public $repeat_password;
  public $permissions;

  public function rules()
  {
    return [
      [['admin_id', 'username', 'email', 'role', 'password', 'repeat_password', 'permissions'], 'safe'],

      // Scenario for create_user rules
      [['email', 'password', 'repeat_password'], 'required', 'on' => 'create_user'],

      // Scenario for change-password rules
      [['username'], 'safe', 'on' => 'update'],

      [['email'], 'email'],
      [['email'], 'validateEmail'],
      [['email'], 'unique', 'targetClass' => '\app\models\Admin', 'filter' => function ($query) {
        return $query->andWhere(['is_delete' => 0]);
      }],

      [['password', 'repeat_password'], 'string', 'min' => 8],
      [['repeat_password'], 'compare', 'compareAttribute' => 'password'],

      [['username', 'email', 'password', 'repeat_password'], 'filter', 'filter' => function ($value) {
        $value = trim($value);
        $value = strip_tags($value);
        return $value;
      }],
    ];
  }

  /**
   * Validate email address
   *
   * @param string $attribute the attribute currently being validated
   * @param array $params the additional name-value pairs given in the rule
   * @param \yii\validators\Validator $validator the validator object
   *
   * @see \yii\validators\Validator::validate()
   */
  public function validateEmail($attribute, $params, $validator)
  {
    if (preg_match("/^[a-zA-Z0-9_.@-]+$/", $this->email) != 1) {
      $this->addError($attribute, Yii::t('app', 'Sorry, only letters (a-z), numbers (0-9), and periods (.) are allowed".'));
    }
  }

  /**
   * Gets query for data provider
   *
   * Select * from admin
   * where role = 'subadmin'
   * and email like :email%
   * and id != :admin_id
   * and is_delete = 0
   *
   * @return ActiveQuery
   */
  public function getQuery()
  {
    $query = Admin::find();
    $query->andFilterWhere(['role' => 'subadmin']);
    $query->andFilterWhere(['like', 'email', $this->email . "%", false]);
    $query->andFilterWhere(['!=', 'id', $this->admin_id]);
    $query->andWhere(['is_delete' => 0]);
    return $query;
  }

  /**
   * Gets the data provider for the sub admin management list.
   *
   * @return ActiveDataProvider The data provider for the sub admin management list.
   */
  public function getSubAdminProvider()
  {
    $dataProvider =  new ActiveDataProvider([
      'query' => $this->getQuery(),
      'sort'  => [
        'defaultOrder' => [
          'id' => SORT_DESC,
        ],
      ],
      'pagination'   => [
        'pageSize' => 10,
      ],
    ]);

    return $dataProvider;
  }

  /**
   * Get the sub admin profile
   *
   * @param int $id Sub Admin ID
   *
   * @return void
   */
  public function getProfile($id)
  {
    $admin = Admin::findOne(['id' => $id, 'is_delete' => 0]);
    if ($admin) {
      $this->username = $admin->username;
      $this->email    = $admin->email;
      $this->role     = $admin->role;
    }
  }

  /**
   * Creates a new sub admin.
   * 
   * select * from admin 
   * where email = $this->validateEmail
   * and role = 'subadmin'
   * and is_delete = 0
   *
   * @return Admin the newly created admin.
   *
   * @throws \Exception if the admin could not be created.
   */
  public function createSubAdmin()
  {
    $admin_model = Admin::findOne(['email' => $this->email, 'role' => 'subadmin', 'is_delete' => 0]);

    if (!empty($admin_model)) {
      throw new \Exception('Email already exists.', 1);
    }

    $model = new Admin;
    $model = $model->createAdmin([
      'username' => $this->username,
      'email'    => $this->email,
      'role'     => 'subadmin',
      'password' => $this->password,
    ]);

    return $model;
  }

  /**
   * Load all permissions of a user into the permissions property.
   *
   * @param int $id User ID
   *
   * @return void
   */
  public function loadPermissions($id)
  {
    $auth = Yii::$app->authManager;
    $permissions = $auth->getPermissionsByUser($id);

    $this->permissions = [];
    foreach ($permissions as $permission) {
      $this->permissions[$permission->name] = true;
    }
  }

  /**
   * Update the access control for a user.
   *
   * @param int $id User ID
   *
   * @return array Updated permissions
   */
  public function updateAccessControl($id)
  {
    $auth = Yii::$app->authManager;

    // Get all current permissions of the user
    $currentPermissions = $auth->getPermissionsByUser($id);

    // Revoke all current permissions
    foreach ($currentPermissions as $permission) {
      $auth->revoke($permission, $id);
    }

    // Assign new permissions
    foreach ($this->permissions as $key => $value) {
      if ($value) {
        $perm = $auth->getPermission($key);
        if ($perm) {
          $auth->assign($perm, $id);
        }
      }
    }

    // Get updated permissions
    $updatedPermissions = array_map(
      function ($permission) {
        return $permission->name;
      },
      $auth->getPermissionsByUser($id)
    );

    return $updatedPermissions;
  }

  /**
   * Update the profile of a user.
   * 
   * Select * from admin
   * where id = $id
   * and is_delete = 0
   *
   * @param int $id User ID
   *
   * @return void
   *
   * @throws \Exception
   */
  public function updateProfile($id)
  {
    $model = Admin::findOne(['id' => $id, 'is_delete' => 0]);

    $model->username = $this->username;

    if (Yii::$app->user->identity->role == 'admin') {
      $model->email = $this->email;
    }

    $model->updated_at = time();

    if (!$model->update(false, ['username', 'email', 'updated_at'])) {
      throw new \Exception(Yii::t('app', 'Failed to update profile.'));
    }
  }

  /**
   * Suspend/Unsuspend the sub admin.
   * 
   * Select * from admin
   * where id = $id
   * and is_delete = 0
   *
   * @param int $id Sub Admin ID
   *
   * @return bool
   *
   * @throws \Exception
   */
  public function suspendAccount($id)
  {
    $model = Admin::findOne(['id' => $id, 'is_delete' => 0]);

    if (empty($model)) {
      return false;
    }

    $model->is_suspend = $model->is_suspend == 1 ? 0 : 1;
    $model->updated_at = time();

    if (!$model->update(false, ['is_suspend', 'updated_at'])) {
      throw new \Exception(Yii::t('app', 'Failed to suspend account.'));
    }

    return true;
  }
}
