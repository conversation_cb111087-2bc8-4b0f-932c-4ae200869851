import server from "../../shared/imports/server.js";
import common from "../../shared/imports/common.js";
import query from "../../shared/imports/query.js"
import validator from "../../shared/imports/validator.js"
import helper from "../../shared/imports/helper.js";

const router = server.express.Router();

router.get("/", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    try {
        const find_all_notice = await query.models.notice_lookup.findAll({
            where: {
                is_delete: false,
            },
            transaction: transaction,
            raw: true
        })
        const find_user_notice_reward = await query.models.user_notice_reward.findAll({
            where: {
                user_id: user_id,
                is_delete: false
            },
            transaction: transaction,
            raw: true
        })
        let formatted_list = [];

        find_all_notice.forEach(element => {
            const is_reward = element.is_reward
            let data = null

            if (is_reward === 1) {
                const find_is_claim = find_user_notice_reward.find(t => t.notice_id === element.id);
                data = {
                    id: element.id,
                    icon_image_url: element.icon_image_url,
                    title: element.notice_label,
                    notice_type: "reward",
                    alex_reward_amount: element.alex_reward_amount,
                    boost_reward_amount: element.boost_reward_amount,
                    is_reward_claimed: find_is_claim ? true : false,
                    created_at: element.created_at,
                }
            } else {
                //Basic Notice Banner
                data = {
                    id: element.id,
                    icon_image_url: element.icon_image_url,
                    title: element.notice_label,
                    description: element.notice_description,
                    notice_type: "normal",
                    created_at: element.created_at,
                }
            }
            if (data !== null) {
                formatted_list.push(data)
            }
        });

        await transaction.commit();
        return res.status(200).json({
            "data": formatted_list,
            "status": 200,
            "msg": `OK`,
            "error": false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            "data": {},
            "status": 400,
            "msg": await common.util_helper.handleErrorMessageAPI(user_id, error),
            "error": true
        });
    }
})

router.post("/:notice_id", helper.auth_helper.authenticateJWT, helper.auth_helper.checkOnlyIsVerified, async (req, res) => {
    const { id: user_id, wallet_id, wallet_address } = req.user;
    const transaction = await query.sequelize.transaction();
    const current_time = common.util_helper.getCurrentEpochTime();
    const notice_id = req.params.notice_id;

    try {
        let find_notice = await query.models.notice_lookup.findOne({ where: { id: notice_id, is_delete: 0 }, transaction: transaction })
        if (!find_notice) {
            await transaction.rollback();
            return res.status(400).json({
                "data": {},
                "status": 400,
                "msg": "Reward Notice not found.",
                "error": true
            });
        }
        if (find_notice.is_reward !== true) {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Notice has no claimable reward.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        }

        let find_user_notice_reward = await query.models.user_notice_reward.findOne({
            where: {
                user_id: user_id,
                notice_id: notice_id,
                is_delete: false
            },
            transaction: transaction,
            raw: true
        })

        if (!find_user_notice_reward) {
            find_user_notice_reward = await query.models.user_notice_reward.create({
                user_id: user_id,
                notice_id: find_notice.id,
                is_claim: 1,
                created_at: common.util_helper.getCurrentEpochTime(),
                updated_at: common.util_helper.getCurrentEpochTime(),
            }, {
                transaction: transaction,
            })
        } else if (find_user_notice_reward.is_claim === true) {
            await transaction.rollback();
            return res.status(400).json({
                "data": "Reward notice claim already completed.",
                "status": 400,
                "msg": "OK",
                "error": true
            })
        } else {
            await find_user_notice_reward.update({
                is_claim: 1,
                updated_at: common.util_helper.getCurrentEpochTime()
            }, {
                lock: transaction.LOCK.UPDATE,
                transaction: transaction,
            })
        }

        const wallet_transaction_alex = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ALEX,
            find_notice.alex_reward_amount,
            common.enum_key.WALLET_TRANSACTION_TYPE.IN,
            null,
            "notice_reward_claim",
            transaction
        );
        const wallet_transaction_energy = await helper.transaction_helper.wallet_transaction_processor(
            user_id,
            common.enum_key.WALLET_TRANSACTION_CREDIT_PROCESS_TYPE.ENERGY,
            find_notice.boost_reward_amount,
            common.enum_key.WALLET_TRANSACTION_TYPE.IN,
            null,
            "notice_reward_claim",
            transaction
        );

        await transaction.commit();
        return res.status(200).json({
            data: {},
            status: 200,
            msg: "OK",
            error: false
        });
    } catch (error) {
        await transaction.rollback();
        return res.status(400).json({
            data: {},
            status: 400,
            msg: await common.util_helper.handleErrorMessageAPI(`user_id: ${user_id} --> wallet_id: ${wallet_id} --> wallet_address: ${wallet_address}`, error),
            error: true
        });
    }
})

export default router;