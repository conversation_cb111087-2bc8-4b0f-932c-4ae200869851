<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;
use kartik\daterange\DateRangePicker;

?>

<?php $form = ActiveForm::begin([
  'id'      => 'general-search',
  'layout'  => 'inline',
  'action'  => $page,
  'method'  => 'get',
  'options' => [
    'data-pjax' => true,
  ],
  'enableClientScript' => false,
  'fieldConfig' => [
    'labelOptions' => [
      'class' => 'mr-1',
    ],
    'inputOptions' => [
      'class' => 'input-sm form-control mr-1 col-12',
    ],
    'options' => [
      'class' => 'col-4 form-group mb-1',
    ],
  ],
]); ?>

<div class="card col-12">
  <div class="card-body">
    <div class="form-row">
      <?= $form->field($model, 'function')->dropDownList($model->getFunction(), [
        'prompt' => Yii::t('app', 'All'),
      ])->label(Yii::t('app', 'Function')) ?>
      <?= $form->field($model, 'status')->dropDownList([
        ''         => Yii::t('app', 'All'),
        'done'     => Yii::t('app', 'Done'),
        'fail'     => Yii::t('app', 'Fail'),
        'processing' => Yii::t('app', 'Processing'),
        'queue'   => Yii::t('app', 'Queue'),
      ])->label(Yii::t('app', 'Status')) ?>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-success float-right']) ?>
  </div>
</div>

<?php ActiveForm::end(); ?>
