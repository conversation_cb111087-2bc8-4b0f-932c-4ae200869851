<?php

use yii\helpers\Html;
use yii\widgets\Breadcrumbs;
use yii\bootstrap4\ActiveForm;

$this->title = Yii::t('app', 'Change Password');

?>

<div class="card">
  <?= Breadcrumbs::widget([
    'itemTemplate' => "<li> <i> {link} / </i> </li>\n",
    'links' => [
      $this->title,
    ],
  ]);
  ?>

  <?php $form = ActiveForm::begin([
    'id'     => 'your-form',
    'method' => 'post',
    'options' => [
      'data-pjax' => true,
    ],
    'fieldConfig' => [
      'inputOptions' => [
        'class' => 'input-sm form-control',
      ],
    ],
  ]);
  ?>
  <div class="card-body">
    <?= $this->render('/site/_alert_flash', []) ?>
    <div class="row">
      <div class="col-md-12">
        <?= $form->field($model, 'old_password')->passwordInput(['placeholder' => Yii::t('app', 'Old Password')])->label(Yii::t('app', 'Old Password')); ?>
      </div>
      <div class="col-md-12">
        <?= $form->field($model, 'password')->passwordInput(['placeholder' => Yii::t('app', 'New Password')])->label(Yii::t('app', 'New Password')); ?>
      </div>
      <div class="col-md-12">
        <?= $form->field($model, 'retype_password')->passwordInput(['placeholder' => Yii::t('app', 'Repeat Your New Password')])->label(Yii::t('app', 'Repeat New Password')); ?>
      </div>
    </div>
  </div>
  <div class="card-footer">
    <?= Html::submitButton(Yii::t('app', 'Update'), ['class' => 'btn btn-success float-right']) ?>
  </div>
  <?php ActiveForm::end(); ?>
</div>