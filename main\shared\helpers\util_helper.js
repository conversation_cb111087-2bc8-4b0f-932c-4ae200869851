import common from "../imports/common.js";
import moment from "moment-timezone";
const MALAYSIA_TZ = "Asia/Kuala_Lumpur";

function getCurrentEpochTime() {
    const unix_epoch = common.moment().utc().unix();
    return unix_epoch;
}

function getTodayFirstMinuteEpoch() {
    const today_first_minute = moment.tz(MALAYSIA_TZ).hour(0).minute(0).second(0).millisecond(0);
    return today_first_minute.unix();
}

function getTomorrow8amMalaysiaEpoch() {
  const tomorrows8am = moment
    .tz(MALAYSIA_TZ)
    .add(1, "day") // Go back one day
        .hour(8)
        .minute(0)
        .second(0)
        .millisecond(0);
    return tomorrows8am.unix();
}

function getTodays8amMalaysiaEpoch() {
    const todays8am = moment.tz(MALAYSIA_TZ).hour(8).minute(0).second(0).millisecond(0);
    return todays8am.unix();
}

function getYesterdays8amMalaysiaEpoch() {
  const yesterdays8am = moment
    .tz(MALAYSIA_TZ)
    .subtract(1, "day") // Go back one day
        .hour(8)
        .minute(0)
        .second(0)
        .millisecond(0);

    return yesterdays8am.unix();
}

function getLastMondayEpoch(now = moment.tz(MALAYSIA_TZ)) {
  const mondayThisWeek = now.clone().tz(MALAYSIA_TZ).isoWeekday(1).startOf("day").hour(8);
  return Math.floor((now.isBefore(mondayThisWeek) ? mondayThisWeek.clone().subtract(7, "days") : mondayThisWeek).valueOf() / 1000);
}

function getNextMondayEpoch(now = moment.tz(MALAYSIA_TZ)) {
  const mondayThisWeek = now.clone().tz(MALAYSIA_TZ).isoWeekday(1).startOf("day").hour(8);
  return Math.floor((now.isBefore(mondayThisWeek) ? mondayThisWeek : mondayThisWeek.clone().add(7, "days")).valueOf() / 1000);
}

function spacer(num = 2) {
    console.log("\n".repeat(num));
}

function jsonLongLogging(data) {
    console.log(JSON.stringify(data, null, 2));
}

async function wait_stopper(mili = 500) {
    await new Promise((resolve) => setTimeout(resolve, mili));
}

function handleErrorMessageAPI(user_id, error) {
    const error_message =
        error.message || "Something went wrong, please try again.";
    console.error({
        data_id: user_id,
        error: error.message,
    });
    return error_message;
}

const handleErrorMessageBot = async (ctx, error) => {
    const error_message =
        error.message || "Something went wrong, please contact support";
    console.error({
        user_id: ctx.from.id,
        error: error.message,
    });
    await ctx.reply(error_message, { parse_mode: "HTML" });
};

function parseReferralCode(text) {
    if (!text) {
        return null
    }

    const parts = text.split(" ");
    if (parts.length > 1) {
        const referral_code = parts[1].match(/ref-([^_]+)/);
        return referral_code ? referral_code[1] : null;
    }
    return null;
}

function gotReferralChecker(ctx) {
    const referral_code = parseReferralCode(ctx.msg.text);
    return {
        got_referral: referral_code !== null,
        referral_code: referral_code,
    };
}

function loginHash(user) {
    const sha512 = common.crypto.createHash('sha3-512')
    sha512.update("!ALEKO! USER GAME START !ALEKO!")
    sha512.update(user.first_last_name.toString())
    sha512.update(user.user_telegram_id ?? "")
    sha512.update(user.id.toString())
    return sha512.digest('hex')
}

function capitalize(str) {
    // Check if the input is a non-empty string
    if (typeof str !== 'string' || !str) {
        return '';
    }
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export default {
    getCurrentEpochTime,
    spacer,
    jsonLongLogging,
    wait_stopper,
    handleErrorMessageAPI,
    handleErrorMessageBot,
    gotReferralChecker,
    loginHash,
    capitalize,
    getTodays8amMalaysiaEpoch,
    getYesterdays8amMalaysiaEpoch,
    getTomorrow8amMalaysiaEpoch,
    getTodayFirstMinuteEpoch,
  getLastMondayEpoch,
  getNextMondayEpoch,
};
