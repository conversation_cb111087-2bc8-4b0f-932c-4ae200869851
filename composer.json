{"name": "yiisoft/yii2-app-basic", "description": "Yii 2 Basic Project Template", "keywords": ["yii2", "framework", "basic", "project template"], "homepage": "https://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "https://www.yiiframework.com/forum/", "wiki": "https://www.yiiframework.com/wiki/", "irc": "ircs://irc.libera.chat:6697/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.2.4", "yiisoft/yii2": "~2.0.45", "yiisoft/yii2-swiftmailer": "~2.0.0 || ~2.1.0", "lcobucci/jwt": "3.3.3", "mobiledetect/mobiledetectlib": "^2.8", "yiisoft/yii2-httpclient": "^2.0", "nickcv/yii2-mandrill": "*", "kartik-v/yii2-widget-datetimepicker": "dev-master", "kartik-v/yii2-date-range": "dev-master", "kartik-v/yii2-widget-datepicker": "1.4.x-dev", "kartik-v/yii2-widget-typeahead": "dev-master", "almasaeed2010/adminlte": "^3.2", "hail812/yii2-adminlte3": "~1.1", "kartik-v/yii2-grid": "dev-master", "supplyhog/yii2-clipboardjs": "dev-master", "2amigos/yii2-ckeditor-widget": "^2.1", "cetver/yii2-language-selector": "dev-master", "kartik-v/yii2-widget-fileinput": "dev-master", "phpoffice/phpspreadsheet": "^1.12", "aws/aws-sdk-php": "^3.209", "sentry/sdk": "2.0", "psychob/ethereum-address-validator": "dev-master", "dolondro/google-authenticator": "dev-main", "endroid/qr-code": "^3.5", "mandrill/mandrill": "dev-master", "kartik-v/yii2-widget-select2": "2.2.x-dev", "aliyuncs/oss-sdk-php": "dev-master", "yiier/yii2-aliyun-oss": "*", "http-interop/http-factory-guzzle": "^1.1", "sangroya/yii2-ckeditor5": "*", "yiisoft/yii2-jui": "2.0.x-dev", "ramsey/uuid": "3.x-dev", "alexantr/yii2-colorpicker": "2.0.x-dev", "mikemix/php-uuid-v6": "dev-master", "unclead/yii2-multiple-input": "~2.0", "yiisoft/yii2-bootstrap4": "^2.0", "kornrunner/keccak": "^1.1", "symfony/intl": "^5.4", "asmoday74/yii2-ckeditor5": "1.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.1.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/verify": "~0.5.0 || ~1.1.0", "codeception/specify": "~0.4.6", "symfony/browser-kit": ">=2.7 <=4.2.4", "codeception/module-filesystem": "^1.0.0", "codeception/module-yii2": "^1.0.0", "codeception/module-asserts": "^1.0.0"}, "config": {"allow-plugins": {"yiisoft/yii2-composer": true, "php-http/discovery": false}, "process-timeout": 1800, "fxp-asset": {"enabled": false}}, "scripts": {"post-install-cmd": ["yii\\composer\\Installer::postInstall"], "post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject", "yii\\composer\\Installer::postInstall"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}]}, "yii\\composer\\Installer::postInstall": {"generateCookieValidationKey": ["config/web.php"]}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}